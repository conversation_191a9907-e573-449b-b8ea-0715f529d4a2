// React & Libraries
import React, { Fragment, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';

// Ant Design Components
import { Upload, Select, Modal, AutoComplete, Switch, Checkbox, Alert, Table, TreeSelect, Popconfirm, Tooltip, notification } from 'antd';
import { CloseOutlined, PlusOutlined, PlusCircleFilled, PlusCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

// UI Components
import H3FormInput from '@Uilib/h3FormInput';
import H3Text from '@Uilib/h3Text';
import PRZButton from '../UI/PRZButton';
import PRZSelect from '../UI/PRZSelect';

// Custom Components
import CustomFieldV2 from '@Components/Common/CustomFieldV2';
import RichTextEditor from '@Components/Common/RichTextEditor';
import SelectQualityRule from '@Components/Common/SelectQualityRule';
import SelectUOM from '../../Admin/Common/SelectUOM';
import SelectTAX from '../../Admin/Common/SelectTax';

// Actions
import ProductActions from '@Actions/productActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import TenantActions from '@Actions/tenantActions';
import CategoryActions from '@Actions/categoryActions';
import QCPointActions from '@Actions/quality/qcPointActions';

// Helpers & Constants
import Constants, { S3_URL } from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

// Assets & Styles
import { cdnUrl } from '@Utils/cdnHelper';
import './style.scss';

const { Option } = Select;

const ProductForm = ({ searchProducts, getDocCFV2, tenants, isAdmin, getTenants, addProductInfo, productKey, getSubCategories, subCategories, getSubCategoriesLoading, searchByReferenceIdResults, searchByReferenceId, isCloneProduct, history, poType, callback, addProductToInventory, updateProductToInventory, addProductToInventoryLoading, searchResults, selectedSkuProduct, updateProductToInventoryLoading, user, getDocCFV2Success, cfV2DocProducts, getTenantsLoading, getQCPoints, selectedTenant, getProductById,
}) => {
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [productName, setProductName] = useState('');
  const [taxId, setTaxId] = useState('');
  const [hsnCode, setHsnCode] = useState('');
  const [inventoryUom, setInventoryUom] = useState('');
  const [fileList, setFileList] = useState([]);
  const [isPerishable, setIsPerishable] = useState(false);
  const [isSalesEnabled, setIsSalesEnabled] = useState(true);
  const [isPurchaseEnabled, setIsPurchaseEnabled] = useState(true);
  const [allTenantsSelected, setAllTenantsSelected] = useState(true);
  const [productType, setProductType] = useState('STORABLE');
  const [shelfLifeDuration, setShelfLifeDuration] = useState('MONTHS');
  const [inclusiveOfMSP, setInclusiveOfMSP] = useState(false);
  const [isMarketplaceProduct, setIsMarketplaceProduct] = useState(false);
  const [uomList, setUomList] = useState([]);
  const [excludeUomIds, setExcludeUomIds] = useState([]);
  const [barcode, setBarcode] = useState('');
  const [taxInfo, setTaxInfo] = useState('');
  const [description, setDescription] = useState('');
  const [productSkuId, setProductSkuId] = useState('');
  const [threshold, setThreshold] = useState('');
  const [overFlowPercentage, setOverFlowPercentage] = useState('');
  const [selectedTenants, setSelectedTenants] = useState([]);
  const [expiryDays, setExpiryDays] = useState('');
  const [productCategory, setProductCategory] = useState('');
  const [referenceId, setReferenceId] = useState('');
  const [inventoryUomName, setInventoryUomName] = useState('');
  const [purchaseUom, setPurchaseUom] = useState('');
  const [sellingPrice, setSellingPrice] = useState('');
  const [costPrice, setCostPrice] = useState('');
  const [mrp, setMrp] = useState('');
  const [sellingPriceMarkupPer, setSellingPriceMarkupPer] = useState(0);
  const [customFieldProductsList, setCustomFieldProductsList] = useState([]);
  const [allCustomFieldProductsList, setAllCustomFieldProductsList] = useState(
    [],
  );
  const [showSelectedTenants, setShowSelectedTenants] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTitle, setPreviewTitle] = useState('');
  const [uomCategory, setUomCategory] = useState('');
  const [isUserReady, setIsUserReady] = useState(false);
  const [batchConsumptionMethod, setBatchConsumptionMethod] = useState('FIFO');
  const [selectedCategory, setSelectedCategory] = useState();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredTreeData, setFilteredTreeData] = useState([]);
  const [arNumberPrefix, setArNumberPrefix] = useState('');
  const [arNumberCounter, setArNumberCounter] = useState('');
  const [expiryDateFormat, setExpiryDateFormat] = useState('DD/MM/YYYY');
  const [manufacturingDateFormat, setManufacturingDateFormat] = useState('DD/MM/YYYY');
  const [selectedQCRule, setSelectedQCRule] = useState();
  const [secondaryUom, setSecondaryUom] = useState();
  const [updateDocumentReason, setUpdateDocumentReason] = useState('');
  const [cloneProductSkuId, setCloneProductSkuId] = useState('');
  const [searchCloneProduct, setSearchCloneProduct] = useState('');
  const [isUpdateCase, setIsUpdateCase] = useState(!!selectedSkuProduct);
  const [pushToZoho, setPushToZoho] = useState(false);

  const handleDelete = (key) => {
    const copyData = uomList.filter((item) => item.key !== key);
    setUomList(copyData);
  };
  const productTypeToolTipContent = (
    <span>
      The Product Type field categorizes items based on their nature and
      inventory tracking capability:
      <br />
      - 'Storable': Products tracked in inventory systems, such as physical
      goods.
      <br />
      - 'Non-storable': Items not tracked in inventory, such as services or
      digital products.
      <br />
      - 'Service': Intangible offerings involving services rather than physical
      goods.
      <br />- 'Bundle': Collections of products sold as a single unit.
    </span>
  );

  const uomColumns = [
    {
      title: 'UOM Name',
      width: '220px',
      render: (text, record) => (
        <fragment>
          {record?.newRow ? (
            <SelectUOM
              hideTitle
              onChange={(value) => {
                const copyData = JSON.parse(JSON.stringify(uomList));
                const updatedCopyData = copyData.map((item) => {
                  if (item.key === record.key) {
                    return {
                      ...value,
                      key: item?.key,
                      uom_id: value?.uom_id,
                      uom_name: value?.uom_name,
                      is_base_uom: false,
                      group_id: value?.group_id,
                      uqc: value.uqc,
                      precision: value?.precision,
                      ratio: value?.ratio,
                      is_editable: true,
                      newRow: true,
                    };
                  }
                  return item;
                });
                setUomList(updatedCopyData);
              }}
              containerClassName="product-uom__input"
              errorOutline={formSubmitted && !record.uom_id}
              selectedUom={record.uom_id}
              disabled={
                addProductToInventoryLoading ||
                updateProductToInventoryLoading ||
                getTenantsLoading
              }
              excludeUomIds={excludeUomIds}
            />
          ) : (
            <div>
              {record?.uom_name?.toProperCase()}{' '}
              {record?.is_base_uom && (
                <span className="base-uom-tag">Base</span>
              )}
            </div>
          )}
        </fragment>
      ),
    },
    {
      title: 'UQC',
      dataIndex: 'uqc',
    },
    {
      title: 'Ratio',
      render: (text, record) => (
        <div>
          <input
            value={record.ratio}
            type="text"
            onWheel={(event) => event.target.blur()}
            onChange={(event) => {
              const inputValue = event.target.value;

              // Allow only valid numeric input (including periods and minus signs)
              if (/^-?\d*\.?\d*$/.test(inputValue)) {
                const uomListCopy = JSON.parse(JSON.stringify(uomList));
                for (let i = 0; i < uomListCopy?.length; i++) {
                  if (uomListCopy[i]?.key === record?.key) {
                    uomListCopy[i].ratio = inputValue; // Keep it as a string while typing
                    break;
                  }
                }
                setUomList(uomListCopy);
              }
            }}
            className={record.ratio > 0 ? 'uomInput' : 'uomInputError'}
            disabled={
              !record?.is_editable || (selectedSkuProduct && !record?.newRow)
            }
          />
        </div>
      ),
    },
    {
      title: 'Precision',
      render: (text, record) => (
        <div>
          <input
            value={record.precision}
            type="Number"
            onWheel={(event) => event.target.blur()}
            onChange={(event) => {
              const uomListCopy = JSON.parse(JSON.stringify(uomList));
              for (let i = 0; i < uomListCopy?.length; i++) {
                if (uomListCopy[i]?.key === record?.key) {
                  uomListCopy[i].precision = Number.parseFloat(event.target.value);
                  break;
                }
              }
              setUomList(uomListCopy);
            }}
            className={record.precision >= 0 ? 'uomInput' : 'uomInputError'}
          />
        </div>
      ),
    },
    {
      title: '',
      render: (text, record) => (
        <Fragment>
          {record.newRow && (
            <div
              className="product-uom__delete-line-button"
              onClick={() => handleDelete(record.key)}
            >
              <CloseOutlined />
            </div>
          )}
        </Fragment>
      ),
      width: '40px',
      fixed: 'right',
    },
  ];

  useEffect(() => {
    if (tenants && !selectedTenants?.length && allTenantsSelected) {
      const newSelectedTenants = tenants?.data?.map(
        (tenant) => tenant.tenant_id
      );
      setSelectedTenants(newSelectedTenants);
    }

    if (selectedSkuProduct) {
      if (cfV2DocProducts?.data?.success && !isUserReady) {
        let oldCustomField = selectedSkuProduct?.custom_fields || [];
        if (isAdmin && selectedSkuProduct?.tenant_products?.length > 0) {
          oldCustomField =
            selectedSkuProduct?.tenant_products[0]?.custom_fields || [];
        }

        const formattedCustomFieldProducts =
          CustomFieldHelpers.getDocumentCf(
            cfV2DocProducts?.data?.document_line_custom_fields,
            oldCustomField
          )?.filter((item) => item?.isActive && !['Rate'].includes(item?.fieldName)) || [];
        setCustomFieldProductsList(formattedCustomFieldProducts);
        setIsUserReady(true);
      }
    } else if (cfV2DocProducts && !isUserReady) {
      const formattedCustomFieldProducts =
        CustomFieldHelpers.getCfStructure(
          cfV2DocProducts?.data?.document_line_custom_fields,
          true
        )?.sort((a, b) => {
          // First, sort by System Fiedl (true comes before false)
          if (a.isSystemField !== b.isSystemField) {
            return b.isSystemField - a.isSystemField;
          }
          // If System field is the same, sort by cfId
          return a.cfId - b.cfId;
        }) || [];

      setCustomFieldProductsList(formattedCustomFieldProducts);
      setIsUserReady(true);
    }
  }, [selectedTenants, cfV2DocProducts, isUserReady, allTenantsSelected]);

  useEffect(() => {
    getSubCategories();
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'PRODUCTS',
    };
    getDocCFV2(payload);
    getTenants(1, '', true, user?.tenant_info?.org_id, 100, (tenantsData) => {
      if (selectedSkuProduct && isAdmin) {
        const selectedTenantsList = [];
        if (selectedSkuProduct?.tenant_products?.length > 0) {
          for (
            let i = 0;
            i < selectedSkuProduct?.tenant_products?.length;
            i++
          ) {
            selectedTenantsList.push(
              selectedSkuProduct?.tenant_products?.[i]?.tenant_id
            );
          }
        }
        setAllTenantsSelected(
          selectedTenantsList?.length === tenantsData?.data?.length
        );
        setSelectedTenants(selectedTenantsList);
      }
    });

    if (poType === 'SERVICE_ORDER') {
      setProductType('SERVICE');
    }
    if (selectedSkuProduct) {
      const dataArrived = Object.keys(selectedSkuProduct);
      if (
        dataArrived?.length &&
        !('selling_price_margin' in selectedSkuProduct)
      )
        throw new Error('selling_price_margin key not coming from backend');
      const spWithTax =
        selectedSkuProduct?.selling_price *
        (1 + selectedSkuProduct?.tax_info?.tax_value / 100);
      if (isAdmin) {
        const selectedTenantsList = [];
        if (selectedSkuProduct?.tenant_products?.length > 0) {
          for (
            let i = 0;
            i < selectedSkuProduct?.tenant_products?.length;
            i++
          ) {
            selectedTenantsList.push(
              selectedSkuProduct?.tenant_products?.[i]?.tenant_id
            );
          }
        }
        setProductName(selectedSkuProduct?.product_sku_name);
        setManufacturingDateFormat(
          selectedSkuProduct?.manufacturing_date_format
        );
        setExpiryDateFormat(selectedSkuProduct?.expiry_date_format);
        setSelectedCategory(
          selectedSkuProduct?.product_category_info?.product_category_id
        );
        setIsMarketplaceProduct(selectedSkuProduct?.is_marketplace_product);
        setBarcode(selectedSkuProduct?.barcode);
        setTaxId(selectedSkuProduct?.tax_id);
        setTaxInfo(selectedSkuProduct?.tax_info);
        setHsnCode(selectedSkuProduct?.hsn_code);
        setFileList(selectedSkuProduct?.assets);
        setDescription(selectedSkuProduct?.description);
        setProductSkuId(selectedSkuProduct?.product_sku_id);
        setThreshold(selectedSkuProduct?.tenant_products?.[0]?.threshold_qty);
        setOverFlowPercentage(
          selectedSkuProduct?.tenant_products?.[0]?.grn_over_flow_percent
        );
        setSelectedTenants(selectedTenantsList);
        setAllTenantsSelected(
          selectedTenantsList?.length === tenants?.data?.length
        );
        setExpiryDays(
          selectedSkuProduct?.expiry_days !== -1
            ? (selectedSkuProduct?.expiry_days > 60
              ? (selectedSkuProduct?.expiry_days / 30).toFixed(2)
              : selectedSkuProduct?.expiry_days)
            : null
        );
        setIsPerishable(
          Number(selectedSkuProduct?.expiry_days) > 0
            ? !!selectedSkuProduct?.expiry_days !== -1
            : false
        );
        setProductType(selectedSkuProduct?.product_type);
        setProductCategory(selectedSkuProduct?.product_category);
        setReferenceId(selectedSkuProduct?.ref_product_code);
        setShelfLifeDuration(
          selectedSkuProduct?.expiry_days > 60 ? 'MONTHS' : 'DAYS'
        );
        setInventoryUomName(selectedSkuProduct?.uom_info?.uom_name);
        setInclusiveOfMSP(selectedSkuProduct?.is_sp_inclusive_of_tax);
        setUomList(
          selectedSkuProduct?.uom_list?.map((item) => ({
            ...item,
            key: uuidv4(),
          }))
        );
        setInventoryUom(selectedSkuProduct?.uom_info?.uom_id);
        setPurchaseUom(selectedSkuProduct?.purchase_uom_info?.uom_id);
        setSecondaryUom(selectedSkuProduct?.secondary_uom_info?.uom_id);
        setBatchConsumptionMethod(
          selectedSkuProduct?.default_outwards_method || 'FIFO'
        );
        setSellingPriceMarkupPer(selectedSkuProduct?.selling_price_margin || 0);
        setArNumberPrefix(selectedSkuProduct?.ar_number_prefix);
        setArNumberCounter(selectedSkuProduct?.ar_number_counter);
        setSelectedQCRule(selectedSkuProduct?.qcr_group_id);
        setPushToZoho(selectedSkuProduct?.push_product_to_zoho);
      } else {
        setProductName(
          isCloneProduct
            ? `${selectedSkuProduct?.product_info?.product_sku_name} Copy`
            : selectedSkuProduct?.product_info?.product_sku_name ?? ''
        );
        setManufacturingDateFormat(
          selectedSkuProduct?.product_info?.manufacturing_date_format
        );
        setExpiryDateFormat(
          selectedSkuProduct?.product_info?.expiry_date_format
        );
        setIsMarketplaceProduct(
          selectedSkuProduct?.product_info?.is_marketplace_product
        );
        setBarcode(selectedSkuProduct?.barcode);
        setTaxId(selectedSkuProduct?.product_info?.tax_id);
        setTaxInfo(selectedSkuProduct?.tax_info);
        setHsnCode(selectedSkuProduct?.product_info?.hsn_code);
        setFileList(selectedSkuProduct?.product_info?.assets);
        setDescription(selectedSkuProduct?.product_info?.description);
        setProductSkuId(selectedSkuProduct?.product_info?.product_sku_id);
        setThreshold(selectedSkuProduct?.threshold_qty);
        setOverFlowPercentage(
          selectedSkuProduct?.product_info?.grn_over_flow_percent
        );
        setExpiryDays(
          selectedSkuProduct?.product_info?.expiry_days > 60
            ? (selectedSkuProduct?.product_info?.expiry_days / 30).toFixed(2)
            : (selectedSkuProduct?.product_info?.expiry_days === -1
              ? null
              : selectedSkuProduct?.product_info?.expiry_days)
        );
        setIsPerishable(
          Number(selectedSkuProduct?.product_info?.expiry_days) > 0
            ? !!selectedSkuProduct?.product_info?.expiry_days !== -1
            : false
        );
        setSellingPrice(
          selectedSkuProduct?.is_sp_inclusive_of_tax
            ? Number(spWithTax.toFixed(3))
            : selectedSkuProduct?.selling_price
        );
        setCostPrice(selectedSkuProduct?.cost_price);
        setMrp(selectedSkuProduct?.mrp);
        setIsSalesEnabled(!!selectedSkuProduct?.is_sales_product);
        setIsPurchaseEnabled(!!selectedSkuProduct?.is_purchase_product);
        setProductType(selectedSkuProduct?.product_type);
        setProductCategory(selectedSkuProduct?.product_category);
        setReferenceId(selectedSkuProduct?.ref_product_code);
        setShelfLifeDuration(
          selectedSkuProduct?.product_info?.expiry_days > 60 ? 'MONTHS' : 'DAYS'
        );
        setInventoryUomName(selectedSkuProduct?.uom_info?.uom_name);
        setInclusiveOfMSP(selectedSkuProduct?.is_sp_inclusive_of_tax);
        setUomList(
          selectedSkuProduct?.uom_list?.map((item) => ({
            ...item,
            key: uuidv4(),
          }))
        );
        setInventoryUom(selectedSkuProduct?.uom_info?.uom_id);
        setPurchaseUom(selectedSkuProduct?.purchase_uom_info?.uom_id);
        setSecondaryUom(selectedSkuProduct?.secondary_uom_info?.uom_id);
        setExcludeUomIds(
          selectedSkuProduct?.uom_list?.map((item) => item?.uom_id)
        );
        setBatchConsumptionMethod(
          selectedSkuProduct?.default_outwards_method || 'FIFO'
        );
        setSelectedCategory(
          selectedSkuProduct?.product_category_info?.product_category_id
        );
        setSellingPriceMarkupPer(selectedSkuProduct?.selling_price_margin || 0);
        setArNumberPrefix(selectedSkuProduct?.product_info?.ar_number_prefix);
        setArNumberCounter(selectedSkuProduct?.product_info?.ar_number_counter);
        setSelectedQCRule(selectedSkuProduct?.qcr_group_id);
        setPushToZoho(selectedSkuProduct?.push_product_to_zoho);
      }
    }

    return () => {
      getDocCFV2Success(null);
    };
  }, []);

  useEffect(() => {
    if (cloneProductSkuId) {
      getProductById(selectedTenant, cloneProductSkuId, '', (productData) => {
        if (productData) {
          const formattedCustomFieldProducts = CustomFieldHelpers.getDocumentCf(
            cfV2DocProducts?.data?.document_line_custom_fields,
            productData?.custom_fields || [],
          )?.filter((item) => item?.isActive && !['Rate'].includes(item?.fieldName)) || [];
          const spWithTax = productData?.selling_price
            * (1 + selectedSkuProduct?.tax_info?.tax_value / 100);
          setCustomFieldProductsList(formattedCustomFieldProducts);
          setProductName(productData?.product_info?.product_sku_name ?? '');
          setManufacturingDateFormat(
            productData?.product_info?.manufacturing_date_format,
          );
          setExpiryDateFormat(
            productData?.product_info?.expiry_date_format,
          );
          setIsMarketplaceProduct(
            productData?.product_info?.is_marketplace_product,
          );
          setBarcode(productData?.barcode);
          setTaxId(productData?.product_info?.tax_id);
          setTaxInfo(productData?.tax_info);
          setHsnCode(productData?.product_info?.hsn_code);
          setFileList(productData?.product_info?.assets);
          setDescription(productData?.product_info?.description);
          setProductSkuId(productData?.product_info?.product_sku_id);
          setThreshold(productData?.threshold_qty);
          setOverFlowPercentage(
            productData?.product_info?.grn_over_flow_percent,
          );
          setExpiryDays(
            productData?.product_info?.expiry_days > 60
              ? Math.round(productData?.product_info?.expiry_days / 30)
              : (productData?.product_info?.expiry_days === -1
                ? null
                : productData?.product_info?.expiry_days)
          );
          setIsPerishable(
            Number(productData?.product_info?.expiry_days) > 0
              ? !!productData?.product_info?.expiry_days !== -1
              : false,
          );
          setSellingPrice(
            productData?.is_sp_inclusive_of_tax
              ? Number(spWithTax.toFixed(3))
              : productData?.selling_price,
          );
          setCostPrice(productData?.cost_price);
          setMrp(productData?.mrp);
          setIsSalesEnabled(!!productData?.is_sales_product);
          setIsPurchaseEnabled(!!productData?.is_purchase_product);
          setProductType(productData?.product_type);
          setProductCategory(productData?.product_category);
          setReferenceId(productData?.ref_product_code);
          setShelfLifeDuration(
            productData?.product_info?.expiry_days > 60 ? 'MONTHS' : 'DAYS',
          );
          setInventoryUomName(productData?.uom_info?.uom_name);
          setInclusiveOfMSP(productData?.is_sp_inclusive_of_tax);
          setUomList(
            productData?.uom_list?.map((item) => ({
              ...item,
              key: uuidv4(),
            })),
          );
          setInventoryUom(productData?.uom_info?.uom_id);
          setPurchaseUom(productData?.purchase_uom_info?.uom_id);
          setSecondaryUom(productData?.secondary_uom_info?.uom_id);
          setExcludeUomIds(
            productData?.uom_list?.map((item) => item?.uom_id),
          );
          setBatchConsumptionMethod(
            productData?.default_outwards_method || 'FIFO',
          );
          setSelectedCategory(
            productData?.product_category_info?.product_category_id,
          );
          setSellingPriceMarkupPer(productData?.selling_price_margin || 0);
          setArNumberPrefix(productData?.product_info?.ar_number_prefix);
          setArNumberCounter(productData?.product_info?.ar_number_counter);
          setSelectedQCRule(productData?.qcr_group_id);
        }
      });
    }

    return () => {
      getDocCFV2Success(null);
    };
  }, [cloneProductSkuId]);

  useEffect(() => {
    if (searchKeyword) {
      const timer = setTimeout(() => {
        getSubCategories(searchKeyword);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [searchKeyword]);

  useEffect(() => {
    if (productName || searchCloneProduct || cloneProductSkuId) {
      const timer = setTimeout(() => {
        searchProducts({
          keyword: cloneProductSkuId ? null : (searchCloneProduct || productName),
          tenantId: selectedTenant || user?.tenant_info?.tenant_id,
          stockStatus: null,
          page: 1,
          limit: 30,
          tenantDepartmentId: Helpers.getTenantDepartmentId(user, null, null, null) || '',
          excludeOutOfStock: null,
          productType: null,
          category: null,
          isSalesProduct: null,
          isPurchaseProduct: null,
          search: true,
          filterReservedQuantity: null,
          barcode: null,
          isShopifyProduct: null,
          isMarketplaceProduct: null,
        });
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [productName, searchCloneProduct, cloneProductSkuId]);

  useEffect(() => {
    if (referenceId) {
      const timer = setTimeout(() => {
        searchByReferenceId({
          keyword: referenceId,
          tenantId: user?.tenant_info?.tenant_id,
          stockStatus: null,
          page: 1,
          limit: 30,
          tenantDepartmentId:
            Helpers.getTenantDepartmentId(user, null, null, null) || '',
          excludeOutOfStock: null,
          productType: null,
          category: null,
          isSalesProduct: null,
          isPurchaseProduct: null,
          search: true,
          filterReservedQuantity: null,
          barcode: null,
          isShopifyProduct: null,
          isMarketplaceProduct: null,
        });
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [referenceId]);

  useEffect(() => {
    getQCPoints(
      user?.tenant_info?.tenant_id,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      1,
      100,
      null
    );
  }, []);

  const handleChangeTextArea = (html) => {
    setDescription(html);
  };

  const getBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.addEventListener('load', () => resolve(reader.result));
      reader.onerror = (error) => reject(error);
    });

  const getTenantList = () => {
    const options = [];
    for (let i = 0; i < tenants?.data?.length; i++) {
      options.push({
        label: (
          <Tooltip title={tenants?.data?.[i]?.tenant_name}>
            {tenants?.data?.[i]?.tenant_name}
          </Tooltip>
        ),
        value: tenants?.data?.[i]?.tenant_id,
      });
    }
    return options;
  };

  const addNewRow = () => {
    const data = uomList;
    const copyData = JSON.parse(JSON.stringify(data));
    copyData.push({
      key: uuidv4(),
      uom_name: '',
      ratio: '',
      precision: '',
      is_editable: true,
      newRow: true,
    });
    setUomList(copyData);
  };

  const addNewProductInfo = () => {
    const spWithoutTax = sellingPrice / (1 + taxInfo?.tax_value / 100);
    const payload = {
      product_sku_name: productName,
      is_marketplace_product: isMarketplaceProduct,
      product_category_id: selectedCategory || null,
      alias_name: productName,
      uom_id: inventoryUom,
      tax_id: taxId,
      tax_info: taxInfo,
      barcode,
      is_active: true,
      uda: [],
      assets: fileList.map((item) => ({
        url: item?.url ? item?.url : item.response.response.location,
      })),
      hsn_code: hsnCode || '',
      description,
      is_perishable: !!expiryDays,
      expiry_days: expiryDays
        ? (shelfLifeDuration === 'MONTHS'
          ? Number(expiryDays) * 30
          : Number(expiryDays))
        : -1,
      selling_price: inclusiveOfMSP
        ? Number(spWithoutTax.toFixed(3))
        : (sellingPrice
          ? Number.parseFloat(sellingPrice)
          : 0),
      is_purchase_product: isPurchaseEnabled,
      cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
      mrp: mrp ? Number.parseFloat(mrp) : 0,
      product_type: productType,
      product_category: productCategory || '',
      ref_product_code: referenceId || '',
      purchase_uom_id: purchaseUom,
      secondary_uom_id: secondaryUom || null,
      uom_list: uomList,
      purchase_uom_info: uomList?.find((item) => item?.uom_id === purchaseUom),
      secondary_uom_info:
        uomList?.find((item) => item?.uom_id === secondaryUom) || null,
      uom_info: uomList?.find((item) => item?.uom_id === inventoryUom),
      default_outwards_method: batchConsumptionMethod,
      custom_fields: CustomFieldHelpers.postCfStructure(
        customFieldProductsList
      ),
      selling_price_margin: sellingPriceMarkupPer,
    };

    addProductInfo(payload, productKey);
  };

  const addProduct = () => {
    // Submit Code for Tenant Side
    try {
      setFormSubmitted(true);
      setTimeout(() => {
        setFormSubmitted(false);
      }, 10 * 1000);
      const spWithoutTax = sellingPrice / (1 + taxInfo?.tax_value / 100);
      if (!productName) throw new Error('Please provide product name');
      if (!productType) throw new Error('Please provide product type');
      if (isHsnCodeMandatory && !hsnCode)
        throw new Error('Please provide HSN Code');
      if (isInternalReferenceIdMandatory && !referenceId)
        throw new Error('Please provide Internal Reference ID');
      if (isBarcodeMandatory && !barcode)
        throw new Error('Please provide Barcode');
      if (isCategoryMandatory && !selectedCategory)
        throw new Error('Please provide Category');

      if (!taxId) throw new Error('Please select correct tax');
      if (!inventoryUom) throw new Error('Please correct UOM');
      if (
        !uomList?.filter(
          (item) =>
            Number(item?.precision || 0) < 0 || Number(item?.ratio || 0) <= 0
        ).length === 0
      )
        throw new Error('Please select UOM list');
      if (!isAdmin && !CustomFieldHelpers.isCfValid(customFieldProductsList)) {
        throw new Error('Custom Field is required');
      }

      if (productKey) {
        addNewProductInfo();
        return;
      }
      if (selectedSkuProduct && !isCloneProduct) {
        const payload = {
          product_sku_name: productName,
          is_marketplace_product: isMarketplaceProduct,
          product_category_id: selectedCategory || null,
          alias_name: productName,
          uom_id: inventoryUom,
          tax_id: taxId,
          barcode,
          is_active: true,
          uda: [],
          threshold_qty: threshold ? Number(threshold) : 0,
          grn_over_flow_percent: overFlowPercentage
            ? Number(overFlowPercentage)
            : 0,
          assets:
            fileList?.map((item) => ({
              url: item?.url ? item?.url : item.response.response.location,
            })) || [],
          hsn_code: hsnCode,
          description: description || '',
          tenant_id: user?.tenant_info?.tenant_id,
          product_sku_id: productSkuId,
          expiry_days: expiryDays
            ? (shelfLifeDuration === 'MONTHS'
              ? Number.parseInt(Number(expiryDays) * 30)
              : Number(expiryDays))
            : -1,
          is_perishable: isPerishable,
          is_sales_product: !!isSalesEnabled,
          selling_price: inclusiveOfMSP
            ? Number(spWithoutTax.toFixed(3))
            : (sellingPrice
              ? Number.parseFloat(sellingPrice)
              : 0),
          is_purchase_product:
            productType === 'BUNDLE' ? false : !!isPurchaseEnabled,
          cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
          mrp: mrp ? Number.parseFloat(mrp) : 0,
          product_type: productType,
          product_category: productCategory || '',
          ref_product_code: referenceId || '',
          uom_list: uomList,
          purchase_uom_id: purchaseUom,
          secondary_uom_id: secondaryUom || null,
          purchase_uom_info: uomList?.find(
            (item) => item?.uom_id === purchaseUom
          ),
          secondary_uom_info:
            uomList?.find((item) => item?.uom_id === secondaryUom) || null,
          uom_info: uomList?.find((item) => item?.uom_id === inventoryUom),
          default_outwards_method: batchConsumptionMethod,
          custom_fields: CustomFieldHelpers.postCfStructure(
            customFieldProductsList
          ),
          selling_price_margin: sellingPriceMarkupPer,
          ar_number_prefix: arNumberPrefix,
          ar_number_counter: arNumberCounter,
          expiry_date_format: expiryDateFormat,
          manufacturing_date_format: manufacturingDateFormat,
          update_document_reason: updateDocumentReason,
          push_product_to_zoho: pushToZoho,
        };
        const tenantProducts = [];
        for (let i = 0; i < tenants?.data?.length; i++) {
          if (tenants?.data?.[i]?.tenant_id === user?.tenant_info?.tenant_id) {
            tenantProducts.push({
              tenant_id: tenants?.data?.[i]?.tenant_id,
              alias_name: productName,
              threshold_qty: threshold ? Number(threshold) : 0,
              grn_over_flow_percent: overFlowPercentage
                ? Number(overFlowPercentage)
                : 0,
              selling_price: inclusiveOfMSP
                ? Number(spWithoutTax.toFixed(3))
                : (sellingPrice
                  ? Number.parseFloat(sellingPrice)
                  : 0),
              is_sp_inclusive_of_tax: !!inclusiveOfMSP,
              is_sales_product: !!isSalesEnabled,
              is_purchase_product:
                productType === 'BUNDLE' ? false : !!isPurchaseEnabled,
              cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
              mrp: mrp ? Number.parseFloat(mrp) : 0,
              is_active: true,
            });
          }
        }
        payload.tenant_products = tenantProducts;
        if (selectedSkuProduct?.qcr_group_id != selectedQCRule) {
          payload.qcr_group_id = selectedQCRule;
        }
        updateProductToInventory(payload, () => callback(), {});
      } else {
        const payload = {
          product_sku_name: productName,
          product_category_id: selectedCategory || null,
          is_marketplace_product: isMarketplaceProduct,
          barcode,
          alias_name: productName,
          uom_id: inventoryUom,
          tax_id: taxId,
          is_active: true,
          uda: [],
          assets: fileList?.map((item) => ({
            url: item?.url ? item?.url : item.response.response.location,
          })),
          hsn_code: hsnCode || '',
          description,
          threshold_qty: threshold ? Number(threshold) : 0,
          grn_over_flow_percent: overFlowPercentage
            ? Number(overFlowPercentage)
            : 0,
          tenant_id: user?.tenant_info?.tenant_id,
          is_perishable: !!expiryDays,
          expiry_days: expiryDays
            ? (shelfLifeDuration === 'MONTHS'
              ? Number(expiryDays) * 30
              : Number(expiryDays))
            : -1,
          is_sales_product: !!isSalesEnabled,
          selling_price: inclusiveOfMSP
            ? Number(spWithoutTax.toFixed(3))
            : (sellingPrice
              ? Number.parseFloat(sellingPrice)
              : 0),
          is_purchase_product:
            productType === 'BUNDLE' ? false : !!isPurchaseEnabled,
          cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
          mrp: mrp ? Number.parseFloat(mrp) : 0,
          product_type: productType,
          product_category: productCategory || '',
          ref_product_code: referenceId || '',
          uom_list: uomList,
          purchase_uom_id: purchaseUom,
          secondary_uom_id: secondaryUom || null,
          purchase_uom_info: uomList?.find(
            (item) => item?.uom_id === purchaseUom
          ),
          secondary_uom_info:
            uomList?.find((item) => item?.uom_id === secondaryUom) || null,
          uom_info: uomList?.find((item) => item?.uom_id === inventoryUom),
          default_outwards_method: batchConsumptionMethod,
          custom_fields: CustomFieldHelpers.postCfStructure(
            customFieldProductsList
          ),
          selling_price_margin: sellingPriceMarkupPer,
          qcr_group_id: selectedQCRule,
          expiry_date_format: expiryDateFormat,
          manufacturing_date_format: manufacturingDateFormat,
          push_product_to_zoho: pushToZoho,
        };
        const tenantProducts = [];
        for (let i = 0; i < tenants?.data?.length; i++) {
          tenantProducts.push({
            tenant_id: tenants?.data?.[i]?.tenant_id,
            alias_name: productName,
            threshold_qty: threshold ? Number(threshold) : 0,
            grn_over_flow_percent: overFlowPercentage
              ? Number(overFlowPercentage)
              : 0,
            selling_price: inclusiveOfMSP
              ? Number(spWithoutTax.toFixed(3))
              : (sellingPrice
                ? Number.parseFloat(sellingPrice)
                : 0),
            is_sp_inclusive_of_tax: !!inclusiveOfMSP,
            is_sales_product: !!isSalesEnabled,
            is_purchase_product:
              productType === 'BUNDLE' ? false : !!isPurchaseEnabled,
            cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
            mrp: mrp ? Number.parseFloat(mrp) : 0,
            is_active: true,
          });
        }
        payload.tenant_products = tenantProducts;
        if (productSkuId) {
          payload.remote_item_id_1 = productSkuId;
        }
        addProductToInventory(
          payload,
          (product) => {
            if (!isCloneProduct) {
              callback(product);
            } else {
              history.push(
                `/inventory/product/view/${product?.product_sku_id}`
              );
              globalThis.location.reload();
            }
          },
          {}
        );
      }
    } catch (error) {
      notification.warning({
        message: error.message,
        placement: 'top',
        duration: 4,
      });
    }
  };

  const addAdminProduct = () => {
    try {
      setFormSubmitted(true);
      setTimeout(() => {
        setFormSubmitted(false);
      }, 10 * 1000);
      const spWithoutTax = sellingPrice / (1 + taxInfo?.tax_value / 100);
      if (!productName) throw new Error('Please provide product name');
      if (!productType) throw new Error('Please provide product type');
      if (!taxId) throw new Error('Please provide product name');
      if (!inventoryUom) throw new Error('Please provide product name');
      if (isHsnCodeMandatory && !hsnCode)
        throw new Error('Please provide HSN Code');
      if (isInternalReferenceIdMandatory && !referenceId)
        throw new Error('Please provide Internal Reference ID');
      if (isBarcodeMandatory && !barcode)
        throw new Error('Please provide Barcode');
      if (isCategoryMandatory && !selectedCategory)
        throw new Error('Please provide Category');
      if (
        !uomList?.filter(
          (item) =>
            Number(item?.precision || 0) < 0 || Number(item?.ratio || 0) <= 0
        ).length === 0
      )
        throw new Error('Please select is UOM');
      if (!isAdmin && !CustomFieldHelpers.isCfValid(customFieldProductsList)) {
        throw new Error('Custom Field is required');
      }

      if (selectedSkuProduct) {
        const payload = {
          product_sku_name: productName,
          product_category_id: selectedCategory || null,
          is_marketplace_product: isMarketplaceProduct,
          barcode,
          alias_name: productName,
          uom_id: inventoryUom,
          tax_id: taxId,
          is_active: true,
          uda: [],
          assets:
            fileList?.map((item) => ({
              url: item?.url ? item?.url : item.response.response.location,
            })) || [],
          hsn_code: hsnCode,
          description: description || '',
          tenant_id: user?.tenant_info?.tenant_id,
          product_sku_id: productSkuId,
          expiry_days: expiryDays
            ? (shelfLifeDuration === 'MONTHS'
              ? Number.parseInt(Number(expiryDays) * 30)
              : Number(expiryDays))
            : -1,
          is_perishable: isPerishable,
          product_type: productType,
          product_category: productCategory || '',
          ref_product_code: referenceId || '',
          purchase_uom_id: purchaseUom,
          secondary_uom_id: secondaryUom || null,
          uom_list: uomList,
          purchase_uom_info: uomList?.find(
            (item) => item?.uom_id === purchaseUom
          ),
          secondary_uom_info:
            uomList?.find((item) => item?.uom_id === secondaryUom) || null,
          uom_info: uomList?.find((item) => item?.uom_id === inventoryUom),
          default_outwards_method: batchConsumptionMethod,
          expiry_date_format: expiryDateFormat,
          manufacturing_date_format: manufacturingDateFormat,
          push_product_to_zoho: pushToZoho,
        };
        const tenantProducts = [];
        for (let i = 0; i < tenants?.data?.length; i++) {
          const tenantData = selectedSkuProduct?.tenant_products?.filter(
            (item) => item?.tenant_id === tenants?.data?.[i]?.tenant_id
          )?.[0];
          tenantProducts.push({
            tenant_id: tenants?.data?.[i]?.tenant_id,
            alias_name: productName,
            threshold_qty: threshold ? Number(threshold) : 0,
            grn_over_flow_percent: overFlowPercentage
              ? Number(overFlowPercentage)
              : 0,
            selling_price: inclusiveOfMSP
              ? Number(spWithoutTax.toFixed(3))
              : (sellingPrice
                ? Number.parseFloat(sellingPrice)
                : 0),
            is_sp_inclusive_of_tax: tenantData?.is_sp_inclusive_of_tax || false,
            is_sales_product: !!tenantData?.is_sales_product || false,
            is_purchase_product:
              productType === 'BUNDLE'
                ? false
                : !!tenantData?.is_purchase_product,
            cost_price: tenantData?.cost_price || 0,
            is_active: selectedTenants.includes(tenants?.data?.[i]?.tenant_id),
          });
        }
        payload.tenant_products = tenantProducts;
        updateProductToInventory(payload, () => callback(), {});
      } else {
        const payload = {
          product_sku_name: productName,
          product_category_id: selectedCategory || null,
          is_marketplace_product: isMarketplaceProduct,
          barcode,
          alias_name: productName,
          uom_id: inventoryUom,
          tax_id: taxId,
          is_active: true,
          uda: [],
          assets: fileList.map((item) => ({
            url: item?.url ? item?.url : item.response.response.location,
          })),
          hsn_code: hsnCode || '',
          description,
          is_perishable: !!expiryDays,
          expiry_days: expiryDays
            ? (shelfLifeDuration === 'MONTHS'
              ? Number(expiryDays) * 30
              : Number(expiryDays))
            : -1,
          selling_price: inclusiveOfMSP
            ? Number(spWithoutTax.toFixed(3))
            : (sellingPrice
              ? Number.parseFloat(sellingPrice)
              : 0),
          is_purchase_product: isPurchaseEnabled,
          cost_price: costPrice ? Number.parseFloat(costPrice) : 0,
          mrp: mrp ? Number.parseFloat(mrp) : 0,
          product_type: productType,
          product_category: productCategory || '',
          ref_product_code: referenceId || '',
          purchase_uom_id: purchaseUom,
          secondary_uom_id: secondaryUom || null,
          uom_list: uomList,
          purchase_uom_info: uomList?.find(
            (item) => item?.uom_id === purchaseUom
          ),
          secondary_uom_info:
            uomList?.find((item) => item?.uom_id === secondaryUom) || null,
          uom_info: uomList?.find((item) => item?.uom_id === inventoryUom),
          default_outwards_method: batchConsumptionMethod,
          custom_fields: CustomFieldHelpers.postCfStructure(
            customFieldProductsList
          ),
          selling_price_margin: sellingPriceMarkupPer,
          expiry_date_format: expiryDateFormat,
          manufacturing_date_format: manufacturingDateFormat,
          push_product_to_zoho: pushToZoho,
        };
        const tenantProducts = [];
        for (let i = 0; i < tenants?.data?.length; i++) {
          const tenantData = selectedSkuProduct?.tenant_products?.filter(
            (item) => item?.tenant_id === tenants?.data?.[i]?.tenant_id
          )?.[0];
          tenantProducts.push({
            tenant_id: tenants?.data?.[i]?.tenant_id,
            alias_name: productName,
            threshold_qty: tenantData?.threshold_qty,
            is_active: selectedTenants.includes(tenants?.data?.[i]?.tenant_id),
            selling_price: tenantData?.selling_price || 0,
            is_sales_product: !!tenantData?.is_sales_product || false,
            is_purchase_product:
              productType === 'BUNDLE'
                ? false
                : !!tenantData?.is_purchase_product,
            cost_price: tenantData?.cost_price || 0,
            mrp: tenantData?.mrp || 0,
            ref_product_code: referenceId || '',
            is_sp_inclusive_of_tax: !!inclusiveOfMSP,
          });
        }
        payload.tenant_products = tenantProducts;
        if (productSkuId) {
          payload.remote_item_id_1 = productSkuId;
        }
        addProductToInventory(
          payload,
          (product) => {
            callback(product);
            setProductName('');
            setManufacturingDateFormat('');
            setExpiryDateFormat('');
            setTaxId('');
            setHsnCode('');
            setFileList([]);
            setAllTenantsSelected(true);
            setShowSelectedTenants(false);
            setFormSubmitted(false);
            setThreshold(null);
            setOverFlowPercentage(null);
            setSelectedTenants([]);
          },
          {}
        );
      }
    } catch (error) {
      notification.warning({
        message: error.message,
        placement: 'top',
        duration: 4,
      });
    }
  };

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(
      file.name || file.url.slice(Math.max(0, file.url.lastIndexOf('/') + 1))
    );
  };

  const customInputChange = (fieldValue, fieldNameProps) => {
    const transformedFieldValue = fieldValue.map((customField) => {
      if (customField?.fieldType === 'ATTACHMENT') {
        return {
          ...customField,
          fieldValue: customField?.fieldValue?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })),
        };
      }
      return customField;
    });

    setCustomFieldProductsList(transformedFieldValue);

    // Find only the updated field
    const updatedField = transformedFieldValue.find(
      (j) => j?.fieldName === fieldNameProps
    );
    if (updatedField) {
      switch (updatedField.fieldName) {
      case 'HSN': {
        setHsnCode(updatedField.fieldValue);
        break;
      }
      case 'MRP': {
        setMrp(updatedField.fieldValue);
        break;
      }
      case 'Internal SKU Code': {
        setReferenceId(updatedField.fieldValue);
        break;
      }
      case 'Category': {
        setSelectedCategory(updatedField.fieldValue);
        break;
      }
      default: {
        break;
      }
      }
    }
    setSearchKeyword('');
  };

  function convertToTreeData(data) {
    return data?.map((item) => ({
      value: item.product_category_id,
      title: item.product_category_name,
      children: item.sub_categories
        ? convertToTreeData(item.sub_categories)
        : [],
    }));
  }

  const highlightText = (text, highlight) => {
    if (!highlight.trim()) {
      return <span>{text}</span>;
    }
    const regex = new RegExp(`(${highlight})`, 'gi');
    const parts = text.split(regex);
    return parts.map((part) =>
      part.toLowerCase() === highlight.toLowerCase() ? (
        <span style={{ color: 'rgb(22, 119, 255)' }}>{part}</span>
      ) : (
        part
      )
    );
  };

  const highlightTreeData = (data, keyword) =>
    data?.map((item) => ({
      ...item,
      originalTitle: item.originalTitle || item.title,
      title: highlightText(item.originalTitle || item.title, keyword),
      children: item.children ? highlightTreeData(item.children, keyword) : [],
    }));

  useEffect(() => {
    setFilteredTreeData(
      highlightTreeData(convertToTreeData(subCategories), searchKeyword)
    );
  }, [subCategories, searchKeyword]);

  const getDataSource = () => {
    const data = uomList;
    return JSON.parse(JSON.stringify(data));
  };

  const uomGroups = user?.tenant_info?.uom_groups_master;
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  const excessGrnAllowed =
    user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note
      ?.settings?.flexible_qty_wrt_po;

  const barCoding =
    user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active;
  const product =
    user?.tenant_info?.inventory_config?.sub_modules?.product?.is_active;
  const automaticArNumberEnabled =
    user?.tenant_info?.inventory_config?.settings
      ?.enable_auto_generation_of_ar_number;
  const isHsnCodeMandatory =
    user?.tenant_info?.inventory_config?.settings?.is_hsn_code_mandatory;
  const isInternalReferenceIdMandatory =
    user?.tenant_info?.inventory_config?.settings
      ?.is_internal_reference_id_mandatory;
  const isBarcodeMandatory =
    user?.tenant_info?.inventory_config?.settings?.is_barcode_mandatory;
  const isCategoryMandatory =
    user?.tenant_info?.inventory_config?.settings?.is_category_mandatory;

  const isSecondaryUomEnabled =
    user?.tenant_info?.global_config?.settings?.enable_secondary_uom;

  const productTypeOptions = [
    { label: 'Storable', value: 'STORABLE' },
    { label: 'Non Storable', value: 'NON_STORABLE' },
    { label: 'Bundle', value: 'BUNDLE' },
    { label: 'Service', value: 'SERVICE' },
  ];

  const durationOptions = [
    { label: 'Days', value: 'DAYS' },
    { label: 'Months', value: 'MONTHS' },
  ];

  const productOptions = searchResults?.result?.products?.map((item) => ({
    value: item?.product_info?.product_sku_id,
    label: (
      <div className="product-filter__option">
        <H3Text
          text={`#${item?.product_info?.internal_sku_code || ''} ${item?.ref_product_code || ''} ${item?.product_info?.product_sku_name || ''}`}
          className="product-filter__option-text"
        />
      </div>
    ),
  }));

  const inventoryOptions = [
    {
      key: 'FEFO',
      value: 'FEFO',
      label: 'First Expiry First Out (FEFO)',
    },
    {
      key: 'FIFO',
      value: 'FIFO',
      label: 'First In First Out (FIFO)',
    },
  ];

  const dateFormatOptions = [
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
    { value: 'MM/YYYY', label: 'MM/YYYY' },
    { value: 'MM/YY', label: 'MM/YY' },
  ];

  const uomGroupOptions = Object.keys(uomGroups || {}).map((key) => ({
    value: key,
    label: uomGroups[key]?.group_name?.toProperCase(),
  }));

  const uomOptions = uomList?.map((uom) => ({
    value: uom?.uom_id,
    label: `${uom?.uom_name?.toProperCase()} (${uom?.uqc})`,
  }));

  return (
    <div className="new-product-form__container">
      {getTenantsLoading ? (
        <div>
          <div className="ant-row">
            {[1, 2, 3, 4, 5, 6]?.map((item, index) => (
              <div key={item} className="ant-col-md-12 ant-col-xs-24">
                <div
                  className={
                    index % 2 === 0 ? 'orgInputContainer ' : 'orgInputContainer'
                  }
                >
                  <div
                    className="loadingBlock"
                    style={{ height: '15px', marginBottom: '6px' }}
                  />
                  <div className="loadingBlock" style={{ height: '32px' }} />
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <Fragment>
          <div style={{ marginBottom: '41px' }}>
            <div className="ant-row">
              <div
                className="ant-col-md-24 ant-col-xs-24 add-product__section"
                style={{ marginTop: selectedTenant ? '0px' : '-20px' }}
              >
                {user?.tenant_info?.shopify_location_id && (
                  <div className="add-product-disclaimer-wrapper">
                    <Alert
                      message="Products created in Procuzy will not be synced with Shopify. Please create products in shopify inorder to keep procuzy in sync."
                      type="info"
                      showIcon
                      className=""
                    />
                  </div>
                )}
                {selectedTenant && (
                  <div className="ant-row">
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <div className="orgInputContainer ">
                        <label className="orgFormLabel">
                          Clone Product
                          <Tooltip title="Selecting a product allows you to duplicate its details.">
                            <InfoCircleOutlined
                              style={{
                                marginLeft: '5px',
                                color: 'grey',
                                cursor: 'pointer',
                              }}
                            />
                          </Tooltip>
                        </label>
                        <PRZSelect
                          value={cloneProductSkuId}
                          showSearch
                          filterOption={false}
                          onSearch={(value) => {
                            setSearchCloneProduct(value);
                            setCloneProductSkuId(null);
                          }}
                          allowClear
                          onSelect={(value) => {
                            setCloneProductSkuId(value);
                            setSearchCloneProduct(null);
                            searchProducts({
                              keyword: null,
                              tenantId: selectedTenant || user?.tenant_info?.tenant_id,
                              stockStatus: null,
                              page: 1,
                              limit: 30,
                              tenantDepartmentId: Helpers.getTenantDepartmentId(user, null, null, null) || '',
                              excludeOutOfStock: null,
                              productType: null,
                              category: null,
                              isSalesProduct: null,
                              isPurchaseProduct: null,
                              search: true,
                              filterReservedQuantity: null,
                              barcode: null,
                              isShopifyProduct: null,
                              isMarketplaceProduct: null,
                            });
                          }}
                          disabled={
                            addProductToInventoryLoading
                            || updateProductToInventoryLoading
                            || getTenantsLoading
                          }
                          placeholder="Search Product"
                          onClear={() => {
                            setCloneProductSkuId(null);
                            setArNumberPrefix('');
                            setArNumberCounter('');
                            setSelectedQCRule(null);
                            setUomList([]);
                            setInventoryUom(null);
                            setPurchaseUom(null);
                            setSecondaryUom(null);
                            setProductType(null);
                            setProductCategory(null);
                            setProductName('');
                            setHsnCode('');
                            setTaxId('');
                            setFileList([]);
                            setSellingPrice('');
                            setCostPrice('');
                            setMrp('');
                            setExpiryDays('');
                            setShelfLifeDuration('DAYS');
                            setIsPerishable(false);
                            setIsSalesEnabled(false);
                            setIsPurchaseEnabled(false);
                            setCustomFieldProductsList([]);
                            setSellingPriceMarkupPer('');
                            setBatchConsumptionMethod('FIFO');
                            setThreshold('');
                            setOverFlowPercentage('');
                            setReferenceId('');
                            setBarcode('');
                            setDescription('');
                            setManufacturingDateFormat('');
                            setUpdateDocumentReason('');
                          }}
                          options={productOptions}
                        >
                        </PRZSelect>
                      </div>
                    </div>
                  </div>
                )}
                <H3Text
                  text="Basic Information"
                  className="add-product__section-title"
                  style={{
                    margin: selectedTenant ? '0px 0px 15px 0px' : '20px 0px 15px 0px',
                  }}
                />
                <div className="ant-row">
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer ">
                      <label className="orgFormLabel">
                        Product Name
                        <span style={{ color: 'red' }}>{' *'}</span>
                      </label>
                      <AutoComplete
                        value={productName}
                        filterOption={false}
                        showSearch
                        onSearch={(value) => {
                          setProductName(value);
                          setCloneProductSkuId(null);
                        }}
                        style={{
                          border: '1px solid rgba(68, 130, 218, 0.2)',
                          borderRadius: '2px',
                          height: '28px',
                          width: '100%',
                          fontSize: '13px',
                          padding: '0px',
                        }}
                        bordered={false}
                        disabled={
                          addProductToInventoryLoading
                          || updateProductToInventoryLoading
                          || getTenantsLoading
                        }
                      >
                        {productName
                          && productName?.length > 2
                          && searchResults?.result?.products?.map((item) => (
                            <Option
                              key={item?.product_info?.product_sku_id}
                              value={item?.product_info?.product_sku_name}
                            // loading={searchProductsLoading}
                            >
                              <div className="product-filter__option">
                                <H3Text
                                  text={`#${item?.product_info?.internal_sku_code || ''
                                    } ${item?.ref_product_code || ''} ${item?.product_info?.product_sku_name || ''
                                    }`}
                                  className="product-filter__option-text"
                                />
                              </div>
                            </Option>
                          ))}
                      </AutoComplete>
                      {formSubmitted && !productName && (
                        <div className="input-error">
                          *Please enter product name
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer">
                      <label className="orgFormLabel">
                        Product Type
                        <span style={{ color: 'red' }}>{'  *'}</span>
                        <Tooltip title={productTypeToolTipContent}>
                          <InfoCircleOutlined
                            style={{
                              marginLeft: '5px',
                              color: 'grey',
                              cursor: 'pointer',
                            }}
                          />
                        </Tooltip>
                      </label>
                      <PRZSelect
                        value={productType}
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading ||
                          selectedSkuProduct?.product_type === 'BUNDLE' ||
                          poType === 'SERVICE_ORDER'
                        }
                        onChange={(value) => {
                          if (value === 'BUNDLE') {
                            setProductType(value);
                            setUomList(
                              Object.entries(
                                user?.tenant_info?.uom_groups_master
                              )
                                ?.filter(
                                  (item) => item[1]?.group_name === 'MEASURE'
                                )?.[0]?.[1]
                                ?.uoms?.map((item) => ({
                                  ...item,
                                  key: uuidv4(),
                                }))
                            );
                            setInventoryUom(
                              Object.entries(
                                user?.tenant_info?.uom_groups_master
                              )
                                ?.filter(
                                  (item) => item[1]?.group_name === 'MEASURE'
                                )?.[0]?.[1]
                                ?.uoms?.filter(
                                  (subItem) => subItem?.uom_name === 'NUMBERS'
                                )[0]?.uom_id
                            );
                            setPurchaseUom(
                              Object.entries(
                                user?.tenant_info?.uom_groups_master
                              )
                                ?.filter(
                                  (item) => item[1]?.group_name === 'MEASURE'
                                )?.[0]?.[1]
                                ?.uoms?.filter(
                                  (subItem) => subItem?.uom_name === 'NUMBERS'
                                )[0]?.uom_id
                            );
                          } else {
                            setProductType(value);
                          }
                        }}
                        options={productTypeOptions}
                        showError={formSubmitted && !productType}
                        errorName={'product type'}
                        errorClassName={'input-error'}
                      />
                    </div>
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <SelectTAX
                      title="Select Tax Percentage"
                      onChange={(selectedTAX) => {
                        setTaxId(selectedTAX?.tax_id);
                        setTaxInfo(selectedTAX);
                      }}
                      containerClassName="orgInputContainer "
                      showError={formSubmitted && !taxId}
                      selectedGST={taxId}
                      disabled={
                        addProductToInventoryLoading ||
                        updateProductToInventoryLoading ||
                        getTenantsLoading
                      }
                    />
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <H3FormInput
                      name="hsn"
                      type="text"
                      containerClassName="orgInputContainer"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      placeholder=""
                      onChange={(e) => {
                        CustomFieldHelpers.updateCustomColumnValue(
                          e.target.value,
                          customFieldProductsList?.find(
                            (i) => i?.fieldName === 'HSN'
                          ),
                          customFieldProductsList,
                          (value) => customInputChange(value, 'HSN')
                        );
                      }}
                      value={hsnCode}
                      label="SAC / HSN Code"
                      maxlength="50"
                      infoMessage="The SAC (Service Accounting Code) or HSN (Harmonized System of Nomenclature) Code is a numerical code used to classify goods or services for taxation purposes in India."
                      disabled={
                        addProductToInventoryLoading ||
                        updateProductToInventoryLoading ||
                        getTenantsLoading
                      }
                      required={isHsnCodeMandatory}
                    />
                    {formSubmitted && !hsnCode && isHsnCodeMandatory && (
                      <div className="input-error">
                        *Please enter hsn code/number
                      </div>
                    )}
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer product-form__right-input">
                      <label className="orgFormLabel">
                        Internal Reference Id
                        {isInternalReferenceIdMandatory && (
                          <span style={{ color: 'red' }}>{'  *'}</span>
                        )}
                      </label>
                      <AutoComplete
                        value={referenceId}
                        filterOption={false}
                        showSearch
                        onSearch={(e) => {
                          CustomFieldHelpers.updateCustomColumnValue(
                            e,
                            customFieldProductsList?.find(
                              (i) => i?.fieldName === 'Internal SKU Code'
                            ),
                            customFieldProductsList,
                            (value) => customInputChange(value, 'Internal SKU Code')
                          );
                        }}
                        style={{
                          border: '1px solid rgba(68, 130, 218, 0.2)',
                          borderRadius: '2px',
                          height: '28px',
                          width: '100%',
                          fontSize: '13px',
                        }}
                        bordered={false}
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading
                        }
                      >
                        {referenceId &&
                          referenceId?.length > 2 &&
                          searchByReferenceIdResults?.result?.products?.map(
                            (item) => (
                              <Option
                                key={item?.product_info?.product_sku_id}
                                value={item?.product_info?.internal_sku_code}
                              // loading={searchByReferenceIdLoading}
                              >
                                <div className="product-filter__option">
                                  <H3Text
                                    text={`#${item?.product_info?.internal_sku_code ||
                                      ''
                                      } ${item?.ref_product_code || ''} ${item?.product_info?.product_sku_name || ''
                                      }`}
                                    className="product-filter__option-text"
                                  />
                                </div>
                              </Option>
                            )
                          )}
                      </AutoComplete>
                    </div>
                    {formSubmitted &&
                      !referenceId &&
                      isInternalReferenceIdMandatory && (
                      <div className="input-error">
                          *Please enter Internal Reference Id
                      </div>
                    )}
                  </div>
                  {productType !== 'BUNDLE' && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <div className="orgInputContainer ">
                        <div className="orgFormLabel">Shelf Life</div>
                        <div className="product-form__shelf-life orgFormInput">
                          <input
                            min="0"
                            onWheel={(event) => event.target.blur()}
                            name="expiry days"
                            type="number"
                            className="product-form__shelf-life-input"
                            onChange={(event) =>
                              setExpiryDays(event.target.value)
                            }
                            value={expiryDays}
                            disabled={
                              addProductToInventoryLoading ||
                              updateProductToInventoryLoading ||
                              getTenantsLoading
                            }
                          />
                          <PRZSelect
                            filterOption={false}
                            className=""
                            defaultValue="DAYS"
                            value={shelfLifeDuration}
                            onChange={(value) => setShelfLifeDuration(value)}
                            style={{
                              border: 'none',
                              width: '100px',
                            }}
                            options={durationOptions}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  {selectedSkuProduct && automaticArNumberEnabled && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="AR Number Prefix"
                        type="text"
                        containerClassName="orgInputContainer"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(event) =>
                          setArNumberPrefix(event.target.value)
                        }
                        value={arNumberPrefix}
                        label="AR Number Prefix"
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading
                        }
                      />
                    </div>
                  )}
                  {selectedSkuProduct && automaticArNumberEnabled && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="AR Number Counter"
                        type="number"
                        containerClassName="orgInputContainer"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(event) =>
                          setArNumberCounter(event.target.value)
                        }
                        value={arNumberCounter}
                        label="AR Number Counter"
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading
                        }
                      />
                    </div>
                  )}
                  {productType !== 'BUNDLE' &&
                    !(selectedSkuProduct && isAdmin) && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="minimum floor quantity"
                        type="number"
                        containerClassName="orgInputContainer"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(event) => setThreshold(event.target.value)}
                        value={threshold}
                        infoMessage="The Minimum Floor Quantity is the lowest number of items or units that should be maintained in stock or displayed on the sales floor at all times."
                        label={`Minimum Floor Quantity ${inventoryUomName
                          ? `(in ${inventoryUomName?.toProperCase()})`
                          : ''
                            }`}
                        disabled={
                          addProductToInventoryLoading ||
                            updateProductToInventoryLoading ||
                            getTenantsLoading
                        }
                      />
                    </div>
                  )}
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <H3FormInput
                      name="barcode"
                      type="text"
                      maxlength={20}
                      containerClassName="orgInputContainer product-form__right-input"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(event) => setBarcode(event.target.value)}
                      value={barcode}
                      label="Barcode"
                      infoMessage="The Product Barcode is a unique series of parallel lines and numbers used to identify a product electronically at checkout and in inventory systems"
                      disabled={
                        addProductToInventoryLoading ||
                        updateProductToInventoryLoading ||
                        !barCoding
                      }
                      restrictInput={!barCoding}
                      required={isBarcodeMandatory}
                      showError={formSubmitted && !barcode && isBarcodeMandatory}
                    />
                  </div>

                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div
                      className={`orgInputContainer ${user?.tenant_info?.global_config?.sub_modules?.barcoding
                        ?.is_active
                        ? 'product-form__right-input'
                        : 'product-form__right-input'
                        }`}
                    >
                      <label className="orgFormLabel">
                        Batch Consumption Method
                      </label>
                      <PRZSelect
                        value={batchConsumptionMethod}
                        onChange={(event) => setBatchConsumptionMethod(event)}
                        options={inventoryOptions}
                      />
                    </div>
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer  product-form__right-input">
                      <label className="orgFormLabel">
                        Select Category
                        {isCategoryMandatory && (
                          <span style={{ color: 'red' }}>{'  *'}</span>
                        )}
                      </label>
                      <TreeSelect
                        showSearch
                        dropdownStyle={{
                          maxHeight: 400,
                          overflow: 'auto',
                          minWidth: 300,
                        }}
                        switcherIcon={<PlusCircleOutlined />}
                        placeholder="Please select"
                        dropdownMatchSelectWidth={false}
                        allowClear
                        treeData={filteredTreeData}
                        value={selectedCategory}
                        onChange={(e) => {
                          CustomFieldHelpers.updateCustomColumnValue(
                            e,
                            customFieldProductsList?.find(
                              (i) => i?.fieldName === 'Category'
                            ),
                            customFieldProductsList,
                            (value) => customInputChange(value, 'Category')
                          );
                        }}

                        onSearch={(value) => setSearchKeyword(value)}
                        style={{
                          border: '1px solid rgba(68, 130, 218, 0.2)',
                          borderRadius: '2px',
                          height: '28px',
                          width: '100%',
                          fontSize: '13px',
                        }}
                        bordered={false}
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading
                        }
                        loading={getSubCategoriesLoading || getTenantsLoading}
                        filterTreeNode={(input, treeNode) => {
                          const title =
                            treeNode.originalTitle || treeNode.title;
                          if (typeof title === 'string') {
                            return title
                              .toLowerCase()
                              .includes(input.toLowerCase());
                          }
                          return false;
                        }}
                        onDropdownVisibleChange={(open) => {
                          if (!open) {
                            getSubCategories();
                            setFilteredTreeData(
                              convertToTreeData(subCategories)
                            );
                          }
                        }}
                      />
                    </div>
                    {formSubmitted &&
                      !selectedCategory &&
                      isCategoryMandatory && (
                      <div className="input-error">
                          *Please select category
                      </div>
                    )}
                  </div>
                  {excessGrnAllowed && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="over flow Percentage"
                        type="number"
                        noDecimal
                        containerClassName="orgInputContainer"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(event) =>
                          setOverFlowPercentage(
                            Math.min(event.target.value, 99)
                          )
                        }
                        value={overFlowPercentage}
                        label="Percentage of excess GRN allowed"
                        infoMessage="The Percentage of Excess Goods Received Note (GRN) Allowed specifies the permissible percentage by which the received quantity of goods can exceed the ordered quantity."
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading
                        }
                      />
                    </div>
                  )}
                  {(!isAdmin || isCloneProduct) && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3Text
                        text="Add to Existing Quality Rule"
                        className="orgFormLabel"
                      />
                      <SelectQualityRule
                        selectedQCRule={selectedQCRule}
                        onChange={(value) => {
                          setSelectedQCRule(value);
                        }}
                      />
                    </div>
                  )}
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer  product-form__right-input">
                      <label className="orgFormLabel">Expiry Date Format</label>
                      <PRZSelect
                        onChange={(e) => setExpiryDateFormat(e)}
                        disabled={!expiryDays}
                        value={expiryDateFormat}
                        options={dateFormatOptions}
                      />
                    </div>
                  </div>
                  <div className="ant-col-md-12 ant-col-xs-24">
                    <div className="orgInputContainer  product-form__right-input">
                      <label className="orgFormLabel">
                        Manufacturing Date Format
                      </label>
                      <PRZSelect
                        onChange={(e) => setManufacturingDateFormat(e)}
                        value={manufacturingDateFormat}
                        options={dateFormatOptions}
                      />
                    </div>
                  </div>
                  {user?.tenant_info?.global_config?.settings
                    ?.marketplace_enabled && (
                    <div className="ant-col-md-24 ant-col-xs-24">
                      <div className="orgInputContainer ">
                        <Checkbox
                          disabled={
                            addProductToInventoryLoading ||
                              updateProductToInventoryLoading ||
                              getTenantsLoading
                          }
                          checked={isMarketplaceProduct}
                          onChange={() =>
                            setIsMarketplaceProduct(!isMarketplaceProduct)
                          }
                        >
                          <div style={{ fontWeight: '600', fontSize: '12px' }}>
                              Marketplace Product
                          </div>
                        </Checkbox>
                      </div>
                    </div>
                  )}
                  {selectedSkuProduct && isMarketplaceProduct && (
                    <div className="">
                      <Alert
                        message="Warning: Upgrading to a marketplace product will delete all previously created vendor Prices in the particular product "
                        type="info"
                        showIcon
                        className=""
                      />
                    </div>
                  )}
                  {user?.tenant_info?.zoho_branch_id && !isAdmin && (
                    <div className="ant-col-md-24 ant-col-xs-24">
                      <div className="orgInputContainer ">
                        <Checkbox
                          disabled={
                            addProductToInventoryLoading ||
                            updateProductToInventoryLoading ||
                            getTenantsLoading
                          }
                          checked={pushToZoho}
                          onChange={() =>
                            setPushToZoho(!pushToZoho)
                          }
                        >
                          <div style={{ fontWeight: '600', fontSize: '12px' }}>
                            {selectedSkuProduct ? 'Push Changes to Zoho' : 'Push to Zoho'}
                          </div>
                        </Checkbox>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {productType !== 'BUNDLE' && (
                <div className="ant-col-md-24 ant-col-xs-24 add-product__section">
                  <H3Text
                    text="Unit of Measure Setup"
                    className="add-product__section-title"
                  />
                  <div className="ant-row">
                    {!selectedSkuProduct && (
                      <div className="ant-col-md-12 ant-col-xs-24">
                        <div className="orgInputContainer ">
                          <label className="orgFormLabel">
                            UOM Category
                            <span style={{ color: 'red' }}>{'  *'}</span>
                          </label>
                          <PRZSelect
                            filterOption={false}
                            className=""
                            value={uomCategory}
                            onChange={(value) => {
                              setUomCategory(value);
                              setUomList(
                                uomGroups[value]?.uoms?.map((item) => ({
                                  ...item,
                                  key: uuidv4(),
                                }))
                              );
                              setPurchaseUom(
                                uomGroups[value]?.uoms?.find(
                                  (item) => item.is_base_uom
                                )?.uom_id
                              );
                              setInventoryUom(
                                uomGroups[value]?.uoms?.find(
                                  (item) => item.is_base_uom
                                )?.uom_id
                              );
                              setExcludeUomIds(
                                uomGroups[value]?.uoms?.map(
                                  (item) => item?.uom_id
                                )
                              );
                            }}
                            disabled={
                              addProductToInventoryLoading ||
                              updateProductToInventoryLoading ||
                              getTenantsLoading
                            }
                            options={uomGroupOptions}
                            showError={formSubmitted && !uomCategory}
                            errorName="uom category"
                            errorClassName="input-error"
                          />
                        </div>
                      </div>
                    )}
                    {!selectedSkuProduct && !uomList && (
                      <div className="ant-col-md-12 ant-col-xs-24" />
                    )}
                    {uomList && (
                      <div className="ant-col-md-24 ant-col-xs-12 product-uom-list">
                        <Table
                          bordered
                          showHeader
                          size="small"
                          loading={
                            addProductToInventoryLoading ||
                            updateProductToInventoryLoading ||
                            getTenantsLoading
                          }
                          columns={uomColumns}
                          dataSource={getDataSource()}
                          currentPage
                          pagination={false}
                        />
                        {!selectedSkuProduct && !uomList ? (
                          <div className="ant-col-md-12 ant-col-xs-24" />
                        ) : (
                          <div className="ant-col-md-12 ant-col-xs-24">
                            <div className="orgInputContainer">
                              <div
                                className="new-row-button"
                                onClick={() => addNewRow()}
                              >
                                <span className="new-row-button__icon">
                                  <PlusCircleFilled />
                                </span>
                                <div>Add Unit</div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    <div className="ant-col-md-12 ant-col-xs-12">
                      <div className="orgInputContainer ">
                        <label className="orgFormLabel">
                          Inventory UOM
                          <span style={{ color: 'red' }}>{'  *'}</span>
                        </label>
                        <PRZSelect
                          filterOption={false}
                          className=""
                          value={inventoryUom}
                          onChange={(value) => setInventoryUom(value)}
                          disabled={
                            (selectedSkuProduct &&
                              !isCloneProduct &&
                              !selectedSkuProduct.is_uom_editable) ||
                            (!selectedSkuProduct && !uomCategory) ||
                            addProductToInventoryLoading ||
                            updateProductToInventoryLoading ||
                            getTenantsLoading
                          }
                          options={uomOptions}
                          showError={formSubmitted && !inventoryUom}
                          errorName="inventory uom"
                          errorClassName="input-error"
                        />
                      </div>
                    </div>
                    <div className="ant-col-md-12 ant-col-xs-12">
                      <div className="orgInputContainer">
                        <label className="orgFormLabel">
                          Purchase UOM
                          <span style={{ color: 'red' }}>{'  *'}</span>
                        </label>
                        <PRZSelect
                          filterOption={false}
                          className=""
                          value={purchaseUom}
                          onChange={(value) => setPurchaseUom(value)}
                          disabled={
                            (!selectedSkuProduct && !uomCategory) ||
                            addProductToInventoryLoading ||
                            updateProductToInventoryLoading ||
                            getTenantsLoading
                          }
                          options={uomOptions}
                          showError={formSubmitted && !purchaseUom}
                          errorName="purchase uom"
                          errorClassName="input-error"
                        />
                      </div>
                    </div>
                    {isSecondaryUomEnabled && (
                      <div className="ant-col-md-12 ant-col-xs-12">
                        <div className="orgInputContainer">
                          <label className="orgFormLabel">Secondary UOM</label>
                          <PRZSelect
                            filterOption={false}
                            className=""
                            value={secondaryUom}
                            onChange={(value) => setSecondaryUom(value)}
                            disabled={
                              (!selectedSkuProduct && !uomCategory) ||
                              Number(
                                selectedSkuProduct?.secondary_available_qty > 0
                              ) ||
                              addProductToInventoryLoading ||
                              updateProductToInventoryLoading ||
                              getTenantsLoading
                            }
                            options={uomOptions}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
              {!(selectedSkuProduct && isAdmin) && (
                <div className="ant-col-md-24 ant-col-xs-24 add-product__section">
                  <H3Text
                    text="Pricing Information"
                    className="add-product__section-title"
                  />
                  <div className="ant-row">
                    {!(selectedSkuProduct && isAdmin) && (
                      <React.Fragment>
                        {' '}
                        <div className="ant-col-md-12 ant-col-xs-12">
                          <div className="orgInputContainer ">
                            <div className="orgFormLabel">
                              Enable Product for Sales
                            </div>{' '}
                            <Switch
                              checked={isSalesEnabled}
                              onChange={() =>
                                setIsSalesEnabled(!isSalesEnabled)
                              }
                              disabled={
                                addProductToInventoryLoading ||
                                updateProductToInventoryLoading ||
                                getTenantsLoading
                              }
                              style={{ marginTop: '12px' }}
                            />
                          </div>
                        </div>
                        {productType !== 'BUNDLE' && (
                          <div className="ant-col-md-12">
                            <div className="orgInputContainer">
                              <div className="orgFormLabel">
                                Enable Product for Purchase
                              </div>{' '}
                              <Switch
                                checked={isPurchaseEnabled}
                                onChange={() =>
                                  setIsPurchaseEnabled(!isPurchaseEnabled)
                                }
                                disabled={
                                  addProductToInventoryLoading ||
                                  updateProductToInventoryLoading ||
                                  getTenantsLoading
                                }
                                style={{ marginTop: '12px' }}
                              />
                            </div>
                          </div>
                        )}
                      </React.Fragment>
                    )}
                    {!(selectedSkuProduct && isAdmin) && (
                      <Fragment>
                        <div className="ant-col-md-12 ant-col-xs-24">
                          <div className="orgInputContainer ">
                            <div
                              className="orgFormLabel"
                              style={{
                                justifyContent: 'space-between',
                                display: 'flex',
                              }}
                            >
                              Selling Price
                              <div style={{ marginTop: '-10px' }}>
                                <Checkbox
                                  onChange={() =>
                                    setInclusiveOfMSP(!inclusiveOfMSP)
                                  }
                                  checked={inclusiveOfMSP}
                                  disabled={
                                    addProductToInventoryLoading ||
                                    updateProductToInventoryLoading ||
                                    getTenantsLoading ||
                                    !taxId
                                  }
                                >
                                  <span className="product-form__checkbox-label">
                                    Inclusive of Tax
                                  </span>
                                </Checkbox>
                              </div>
                            </div>
                            <H3FormInput
                              name="selling price"
                              type="number"
                              inputClassName="gst__input-add-product orgFormInput"
                              onChange={(event) =>
                                setSellingPrice(event.target.value)
                              }
                              value={sellingPrice}
                              infoMessage="The Selling Price (SP) is the amount at which a product or service is offered for sale to customers."
                              disabled={
                                addProductToInventoryLoading ||
                                updateProductToInventoryLoading ||
                                getTenantsLoading
                              }
                            />
                          </div>
                        </div>
                        {productType !== 'BUNDLE' && (
                          <div className="ant-col-md-12 ant-col-xs-24">
                            <H3FormInput
                              name="cost price"
                              type="number"
                              containerClassName="orgInputContainer"
                              labelClassName="orgFormLabel  product-form-label"
                              inputClassName="gst__input-add-product orgFormInput"
                              onChange={(event) =>
                                setCostPrice(event.target.value)
                              }
                              value={costPrice}
                              label="Cost Price"
                              infoMessage="The Cost Price (CP) is the original price of a product before any additional expenses, taxes, or markup are applied."
                              disabled={
                                addProductToInventoryLoading ||
                                updateProductToInventoryLoading ||
                                getTenantsLoading
                              }
                            />
                          </div>
                        )}
                        <div className="ant-col-md-12 ant-col-xs-24">
                          <div className="orgInputContainer ">
                            <H3FormInput
                              name="MRP"
                              type="number"
                              labelClassName="orgFormLabel"
                              inputClassName="gst__input-add-product orgFormInput"
                              onChange={(e) => {
                                CustomFieldHelpers.updateCustomColumnValue(
                                  Number(e.target.value || 0),
                                  customFieldProductsList?.find(
                                    (i) => i?.fieldName === 'MRP'
                                  ),
                                  customFieldProductsList,
                                  (value) => customInputChange(value, 'MRP')
                                );
                              }}
                              value={mrp}
                              label="Maximum Retail Price"
                              required={customFieldProductsList?.find((item) => item?.fieldName === 'MRP')?.isRequired}
                              infoMessage="The Maximum Retail Price (MRP) is the highest price at which a product can be sold to consumers, inclusive of all taxes and charges."
                              disabled={
                                addProductToInventoryLoading ||
                                updateProductToInventoryLoading ||
                                getTenantsLoading
                              }
                              showError={formSubmitted && !mrp && customFieldProductsList?.find((item) => item?.fieldName === 'MRP')?.isRequired}
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-12 ant-col-xs-24">
                          <div className="orgInputContainer ">
                            <H3FormInput
                              name="Markup % for Batch Selling Price"
                              type="number"
                              labelClassName="orgFormLabel"
                              inputClassName="gst__input-add-product orgFormInput"
                              onChange={(event) =>
                                setSellingPriceMarkupPer(event.target.value)
                              }
                              value={sellingPriceMarkupPer}
                              label="Markup % for Batch Selling Price"
                              infoMessage="The selling price of Batch Value will be automatically calculated based on the margin percentage."
                              disabled={
                                addProductToInventoryLoading ||
                                updateProductToInventoryLoading ||
                                getTenantsLoading
                              }
                            />
                          </div>
                        </div>
                      </Fragment>
                    )}
                  </div>
                </div>
              )}
              <div className="ant-col-md-24 ant-col-xs-24 add-product__section">
                <H3Text
                  text="Other Details"
                  className="add-product__section-title"
                />
                <div className="ant-row">
                  <div
                    className="ant-col-md-24 ant-col-xs-24"
                    style={{ marginBottom: '20px' }}
                  >
                    <label className="orgFormLabel">Product Description</label>
                    <RichTextEditor
                      onChange={(value) => handleChangeTextArea(value)}
                      disabled={
                        addProductToInventoryLoading ||
                        updateProductToInventoryLoading ||
                        getTenantsLoading
                      }
                      value={description}
                      placeholder="enter product description.."
                    />
                  </div>
                  <div className="ant-col-md-24 ant-col-xs-24">
                    <div className="product-form__upload">
                      <label className="orgFormLabel">Product Image(s)</label>
                      <Upload
                        action={Constants.UPLOAD_FILE}
                        listType="picture-card"
                        fileList={fileList}
                        onPreview={handlePreview}
                        accept="image/*"
                        onChange={(fileListData) =>
                          setFileList(fileListData.fileList)
                        }
                        multiple
                        disabled={
                          addProductToInventoryLoading ||
                          updateProductToInventoryLoading ||
                          getTenantsLoading
                        }
                      >
                        {fileList?.length >= 10 ? null : uploadButton}
                      </Upload>
                      <Modal
                        open={previewVisible}
                        title={previewTitle}
                        footer={null}
                        onCancel={() => setPreviewVisible(false)}
                      >
                        <img
                          alt="example"
                          style={{ width: '100%' }}
                          src={previewImage}
                        />
                      </Modal>
                    </div>
                  </div>
                  {isAdmin && selectedSkuProduct && (
                    <div style={{ margin: '10px 0px' }}>
                      <Alert
                        message={
                          <div>
                            <span style={{ fontWeight: '500' }}>
                              &nbsp;Cost Price, Sales Price, Is Sales Product,
                              Is Purchase Product and Minimum Floor Quantity
                            </span>
                            &nbsp;can only updated at bushiness unit level.
                          </div>
                        }
                        showIcon
                        type="info"
                        style={{ fontSize: '12px' }}
                      />
                    </div>
                  )}
                  {!isAdmin && (
                    <CustomFieldV2
                      customFields={customFieldProductsList?.filter(
                        (field) =>
                          !field.isSystemField ||
                          (field.isSystemField && field.fieldName === 'MRP')
                      )}
                      formSubmitted={formSubmitted}
                      customInputChange={(value, cfId) =>
                        customInputChange(value, cfId)
                      }
                      disableCase={
                        addProductToInventoryLoading ||
                        updateProductToInventoryLoading ||
                        getTenantsLoading
                      }
                      hideTitle
                      isProductForm
                      isUpdate={!!selectedSkuProduct}
                    />
                  )}
                </div>
              </div>
              {isAdmin && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-form__tenant-list-wrapper">
                    <div className="product-form__tenant-list-title">
                      <Checkbox
                        onChange={() => {
                          if (!allTenantsSelected) {
                            const defaultSelectedTenants = [];
                            for (let i = 0; i < tenants?.data?.length; i++) {
                              defaultSelectedTenants.push(
                                tenants?.data?.[i]?.tenant_id
                              );
                            }
                            setAllTenantsSelected(!allTenantsSelected);
                            setSelectedTenants(defaultSelectedTenants);
                          } else {
                            setSelectedTenants([]);
                            setAllTenantsSelected(!allTenantsSelected);
                          }
                        }}
                        checked={allTenantsSelected}
                      />
                      <div
                        className="product-form__tenant-list-title-text"
                      >
                        All Business Units
                        <span
                          onClick={() =>
                            setShowSelectedTenants(!showSelectedTenants)
                          }
                        >
                          {showSelectedTenants ? 'Show less' : 'Show all'}
                        </span>
                      </div>
                    </div>
                    {(showSelectedTenants) && (
                      <Checkbox.Group
                        options={getTenantList()}
                        onChange={(value) => {
                          setSelectedTenants(value);
                          setAllTenantsSelected(
                            value?.length === tenants?.data?.length
                          );
                        }}
                        // defaultValue={selectedTenants}
                        value={selectedTenants}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="custom-drawer__footer">
            <div className="ant-col-md-6" style={{ marginTop: '0.5rem' }}>
              <div
                className="indent-barcode__wrapper"
                style={{ display: 'flex' }}
              >
                <PRZButton
                  onClick={() => (isAdmin ? addAdminProduct() : addProduct())}
                  isLoading={addProductToInventoryLoading || updateProductToInventory || getTenantsLoading || updateProductToInventoryLoading}
                  disabled={!product || addProductToInventoryLoading || getTenantsLoading || updateProductToInventoryLoading || (isUpdateCase ? !selectedSkuProduct : false)}
                  buttonStyle={{ width: '150px' }}
                >
                  {selectedSkuProduct && !isCloneProduct ? 'Update Product' : 'Add Product'}
                </PRZButton>
                {!product && (
                  <Popconfirm
                    placement="topRight"
                    title="This feature is not accessible within your current plan to use this feature contact us."
                    onConfirm={() => globalThis.Intercom('showNewMessage')}
                    okText="Contact Us"
                    cancelText="Cancel"
                  >
                    <img
                      className="barcode-restrict"
                      src={cdnUrl('crown2.png', 'images')}
                      alt="premium"
                      style={{
                        marginLeft: '8px',
                        marginTop: '5px',
                      }}
                    />
                  </Popconfirm>
                )}
              </div>
            </div>
          </div>
        </Fragment>
      )}
    </div>
  );
};

const mapStateToProps = ({
  UserReducers,
  ProductReducers,
  CFV2Reducers,
  TenantReducers,
  CategoryReducers,
}) => ({
  user: UserReducers.user,
  addProductToInventoryLoading: ProductReducers.addProductToInventoryLoading,
  updateProductToInventoryLoading:
    ProductReducers.updateProductToInventoryLoading,
  products: ProductReducers.products,
  searchResults: ProductReducers.searchResults,
  cfV2DocProducts: CFV2Reducers.cfV2DocProducts,
  getCFV2Loading: CFV2Reducers.getCFV2Loading,
  tenants: TenantReducers.tenants,
  getTenantsLoading: TenantReducers.getTenantsLoading,
  subCategories: CategoryReducers.subCategories,
  getSubCategoriesLoading: CategoryReducers.getSubCategoriesLoading,
  searchByReferenceIdResults: ProductReducers.searchByReferenceIdResults,
});

const mapDispatchToProps = (dispatch) => ({
  getProductById: (tenantId, productSkuId, tenantDepartmentId, callback) => dispatch(ProductActions.getProductById(tenantId, productSkuId, tenantDepartmentId, callback)),
  addProductToInventory: (payload, callback) =>
    dispatch(ProductActions.addProductToInventory(payload, callback)),
  searchProducts: (params, callback) =>
    dispatch(ProductActions.searchProducts(params, callback)),
  getDocCFV2: (paylaod, callback) =>
    dispatch(CFV2Actions.getDocCFV2(paylaod, callback)),
  updateProductToInventory: (payload, callback) =>
    dispatch(ProductActions.updateProductToInventory(payload, callback)),
  getDocCFV2Success: (customFields) =>
    dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getTenants: (page, keyword, isVerified, orgId, limit, callback) =>
    dispatch(
      TenantActions.getTenants(
        page,
        keyword,
        isVerified,
        orgId,
        limit,
        callback
      )
    ),
  getSubCategories: (searchKeyword, page, limit) =>
    dispatch(CategoryActions.getSubCategories(searchKeyword, page, limit)),
  searchByReferenceId: (params, callback) =>
    dispatch(ProductActions.searchByReferenceId(params, callback)),
  getQCPoints: (
    tenantId,
    groupId,
    operation,
    productSkuId,
    accessControl,
    departmentId,
    sellerId,
    category,
    page,
    limit,
    searchKeyword
  ) =>
    dispatch(
      QCPointActions.getQCPoints(
        tenantId,
        groupId,
        operation,
        productSkuId,
        accessControl,
        departmentId,
        sellerId,
        category,
        page,
        limit,
        searchKeyword
      )
    ),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(ProductForm));
