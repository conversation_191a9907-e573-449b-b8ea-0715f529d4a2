const fs = require('fs');
const path = require('path');

const buildDir = path.join(__dirname, 'build');
const headersFile = path.join(buildDir, '_headers');

const headersContent = `/*
  # Apply headers to all pages

  # X-Frame-Options
  X-Frame-Options: SAMEORIGIN

  # Content Security Policy
  # Content-Security-Policy: default-src 'self' https://jam.dev blob: i.imgur.com data:; script-src 'self' https://jam.dev blob: i.imgur.com data: 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; frame-src 'self';

  # Cache-Control Headers
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

  # Permissions-Policy
  Permissions-Policy: camera=(), microphone=()

  # Strict Transport Security
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
`;

if (fs.existsSync(buildDir)) {
  fs.writeFileSync(headersFile, headersContent);
  console.log('_headers file has been created in', buildDir);
} else {
  console.error('Build directory does not exist. Please ensure you have run the build process first.');
  process.exit(1);
}
