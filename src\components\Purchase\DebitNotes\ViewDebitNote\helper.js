import React, { Fragment } from 'react';
import Constants, { entityNameEnum } from '@Apis/constants';
import { Menu } from 'antd';
import H3Text from '@Uilib/h3Text';
import { v4 as uuidv4 } from 'uuid';

export const handleFileChangeHelper = ({ fileListData, currentDN, updateState, updateAttachment, getAttachmentById, }) => {
  const fileList = fileListData?.fileList?.map((item) => ({
    ...item,
    url: item?.response?.response?.location || item?.url,
  }));

  const attachments = fileList?.map((attachment) => ({
    url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
  }));

  if (fileListData?.file.status === 'done') {
    const payload = {
      entity_id: currentDN?.dn_id,
      entity_name: 'debit_note',
      attachments,
    };
    updateAttachment(payload, () => {
      getAttachmentById(currentDN?.dn_id, 'debit_note', () => {
        updateState({
          isGetAttachment: true,
        });
      });
    });
  }
  if (fileListData?.file.status === 'removed') {
    const payload = {
      entity_id: currentDN?.dn_id,
      entity_name: 'debit_note',
      attachments,
    };
    updateAttachment(payload, () => {
      getAttachmentById(currentDN?.dn_id, 'debit_note', () => {
        updateState({
          isGetAttachment: true,
        });
      });
    });
  }

  updateState({
    fileList: attachments.length > 0 ? attachments : [],
  });
};

export const getLinkedDocumentInfoHelper = ({ currentDN, state, updateState, priceMasking, }) => {

  const { showViewPayout, } = state;
  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
  const linkedDocumentsInfo = [];

  if (currentDN?.applied_grns?.length > 0) {

    const documentDataInfo = currentDN?.applied_grns?.map(item => ({
      documentId: item?.grn_id,
      documentNumber: item?.grn_number,
      documentTotal: item?.amount,
    }));

    linkedDocumentsInfo.push({
      entityName: entityNameEnum.GOODS_RECEIVING,
      title: 'APPLIED GRNS',
      entityNumberPrefix: '',
      documentData: documentDataInfo,
      isRedirect: true,
      redirectPath: '/purchase/goods-receiving/view/',
      popOverMessage: "You don't have access to view amount",
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      entityVisible: documentDataInfo.length > 0,
      showEntityTotal: true,
    });
  }

  if (currentDN?.dn_payments?.length > 0) {
    const documentDataInfo = currentDN?.dn_payments?.map(item => ({
      documentId: item?.payment_id,
      documentNumber: `${item?.applied_payment_type?.split('_').join(' ')}# ${item?.debit_note_number || item?.payment_id}`,
      documentTotal: item?.amount,
      appliedPaymentType:'PAYMENT'
    }));

    linkedDocumentsInfo.push({
      entityName: entityNameEnum.OUTGOING_PAYMENTS,
      title: 'LINKED PAYMENTS',
      documentData: documentDataInfo,
      entityNumberPrefix: '',
      isDrawer: true,
      drawerState: showViewPayout,
      drawerCallback: (payment, isOpen) => updateState({ selectedPayment: payment, showViewPayout: isOpen }),
      entityVisible: documentDataInfo?.length > 0 && !(isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)),
      showEntityTotal: true,
    });
  }

  return linkedDocumentsInfo;
};

export const menu = ({ currentDN, downloadDocument, }) => (
  <Menu>
    {currentDN?.org_currency_info?.org_currency_id && (
      <Menu.Item>
        <H3Text
          text={`Debit Note (${currentDN?.org_currency_info?.currency_code})`}
          onClick={() => downloadDocument({
            url: `${Constants.DN}/download?dn_id=${currentDN?.dn_id}&tenant_id=${currentDN?.tenant_info?.tenant_id}&download_document_in_base_currency=false`,
            document_type: 'DEBIT_NOTE',
            document_number: currentDN?.debit_note_number,
            key: uuidv4(),
          })}
          className="hide__in-mobile"
        />
      </Menu.Item>
    )}
    {(currentDN?.base_currency_info?.org_currency_id && currentDN?.org_currency_info?.org_currency_id !== currentDN?.base_currency_info?.org_currency_id) && (
      <Menu.Item>
        <H3Text
          text={`Debit Note (${currentDN?.base_currency_info?.currency_code})`}
          onClick={() => downloadDocument({
            url: `${Constants.DN}/download?dn_id=${currentDN?.dn_id}&tenant_id=${currentDN?.tenant_info?.tenant_id}&download_document_in_base_currency=true`,
            document_type: 'DEBIT_NOTE',
            document_number: currentDN?.debit_note_number,
            key: uuidv4(),
          })}
          className="hide__in-mobile"
        />
      </Menu.Item>
    )}
  </Menu>
);

export const renderFreightHelper = ({ position, currentDN, isVendorOverseas, MONEY }) => {

  if ((currentDN?.charge_1_value > 0)) {
    if (position === 'top' && currentDN?.freight_tax_id) {
      return (
        <div className="view-document__totals-field">
          <H3Text
            text={(
              <Fragment>
                {currentDN?.charge_1_name}
                &nbsp;
                {!isVendorOverseas && (<span className="table-subscript">{`tax@${currentDN?.freight_tax_info?.tax_value}%`}</span>)}
              </Fragment>
            )}
            className="view-document__totals-field-name"
          />
          <H3Text text={MONEY((currentDN?.charge_1_value), currentDN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
      );
    }
    if (position === 'bottom' && !currentDN?.freight_tax_id) {
      return (
        <div className="view-document__totals-field">
          <H3Text text={currentDN?.charge_1_name} className="view-document__totals-field-name" />
          <H3Text text={MONEY((currentDN?.charge_1_value), currentDN?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
      );
    }
  }
  return '';
}

export const splitChargesDataHelper = ({ charge }) => {
  const chargeWithTaxName = charge?.filter((line) => line?.tax_info?.tax_id) || [];
  const chargeWithoutTaxName = charge?.filter((line) => !line?.tax_info?.tax_id) || [];
  return { chargeWithTaxName, chargeWithoutTaxName };
};

export const renderChargesHelper = ({ charge, MONEY, currentDN, isVendorOverseas }) => !!charge?.length && charge?.map((otherCharge, i) => {

  return (
    <div key={i} className="view-document__totals-field">
      <H3Text
        text={(
          <Fragment>
            {otherCharge?.charge_name?.toProperCase()}
            &nbsp;
            {otherCharge?.tax_info?.tax_value && !isVendorOverseas ? (
              <span className="table-subscript">{`tax@${otherCharge.tax_info.tax_value}%`}</span>
            ) : null}
          </Fragment>
        )}
        className="view-document__totals-field-name"
      />
      {otherCharge?.charge_amount < 0
        ? <H3Text text={`(-)${MONEY((Math.abs(otherCharge?.charge_amount)), currentDN?.org_currency_info?.currency_code)}`} className="view-document__totals-field-value danger-text" />
        : <H3Text text={MONEY((Math.abs(otherCharge?.charge_amount)), currentDN?.org_currency_info?.currency_code)} className="view-document__totals-field-value " />}
    </div>
  )
});