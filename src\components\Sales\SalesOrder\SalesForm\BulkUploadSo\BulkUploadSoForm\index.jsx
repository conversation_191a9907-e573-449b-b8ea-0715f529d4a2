/* eslint-disable no-nested-ternary */
import React, { Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import { v4 as uuidv4 } from 'uuid';
import {
  DownloadOutlined, LoadingOutlined,
} from '@ant-design/icons';
import {
  Timeline, notification,
} from 'antd';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import './style.scss';
import Constants from '@Apis/constants';
import ProductActions from '@Actions/productActions';
import WorkflowActions from '@Actions/workflowActions';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';

/**
 * @param e
 */
const BulkUploadSoForm = ({
  user, callback, userDepartmentId, selectedSeller, getSellerCodes,
  getProductBySkusLoading,
  setData, filteredData, setFilteredData, adjustmentType, getProductBySkus,
}) => {

  const onFileSelect = (e) => {
    const [file] = e.target.files;
    const reader = new FileReader();
    reader.onload = (evt) => {
      const bstr = evt.target.result;
      const wb = XLSXRead(bstr, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSXUtils.sheet_to_json(ws, { header: 1 });

      const columnsToFind = ['sku_id', 'ref_product_code', 'sku_name', 'quantity', 'unit_price', 'discount_percentage', 'free_quantity'];

      const columnIndices = {};
      data[0].forEach((columnName, index) => {
        if (columnsToFind.includes(columnName)) {
          columnIndices[columnName] = index;
        }
      });

      const copyFileData = data?.map((item) => ({
        sku_id: item?.[columnIndices['sku_id']],
        sku_name: item?.[columnIndices['sku_name']],
        quantity: item?.[columnIndices['quantity']],
        unit_price: item?.[columnIndices['unit_price']],
        line_discount_percentage: item?.[columnIndices['discount_percentage']],
        free_quantity: item?.[columnIndices['free_quantity']],
        key: uuidv4(),
      }))?.slice(1);

      setFilteredData(copyFileData?.filter((item) => item?.sku_id));
    };
    reader.readAsBinaryString(file);
  };


  const downloadSampleFile = async (adjType) => {
    const url = `${Constants.GET_BULK_PRODUCT_SHEET}?tenant_id=${user?.tenant_info?.tenant_id}&type=${adjType}`;
    const accessToken = localStorage.getItem('h3m-procuzy-access-token');
  
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
  
      if (!response.ok) {
        throw new Error('Failed to download file');
      }
  
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = 'sample_file.xlsx';
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download error:', error);
    }
  };

  // const downloadSampleFile = (adjType) => {
  //   const a = document.createElement('a');
  //   const url = `${Constants.GET_BULK_PRODUCT_SHEET}?tenant_id=${user?.tenant_info?.tenant_id}&type=${adjType}`;
  //   a.href = url;
  //   a.download = url.split('/').pop();
  //   document.body.appendChild(a);
  //   a.click();
  //   document.body.removeChild(a);
  // };

  const addData = () => {
    if (filteredData?.length > 500) {
      notification.error({
        message: 'You can only upload upto 500 products at once.',
        placement: 'top',
        duration: 4,
      });
    } else {
      const skusData = filteredData
        ?.filter((item) => item?.sku_id)
        ?.map((item) => ({ key: item?.key, sku_id: item?.sku_id }));
      const uniqueSkuIds = [...new Set(filteredData?.filter((item) => item?.sku_id)?.map((item) => item?.sku_id))];

      const payload = {
        tenant_ids: String(user?.tenant_info?.tenant_id),
        sku_codes: uniqueSkuIds,
        ...(selectedSeller && { tenant_seller_ids: String(selectedSeller) }),
        org_id: String(user?.tenant_info?.org_id),
        tenant_department_ids: String(userDepartmentId || Helpers.getTenantDepartmentId(user, null, null, null)),
        sku_data: skusData,
      };
      getProductBySkus(payload, ((updatedData) => {
        setData(updatedData?.data);
        callback();
      }));
      // getSellerCodes(
      //   filteredData?.filter((item) => item?.sku_id)?.map((item) => item?.seller_code)?.join(','),
      //   user?.tenant_info?.org_id,
      // );
    }
  };

  return (
    <Fragment>
      <div className="ant-row" style={{ paddingBottom: '60px' }}>
        <div className="ant-col-md-24">
          <div className="view__timeline-workflow">
            <Timeline>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 1
                    </div>
                    <div className="timeline-item-person">
                      Download the list of products for this Business Unit
                      <div className="timeline-item-download">
                        <div
                          className="timeline-item-download-button"
                          onClick={() => {
                            downloadSampleFile(adjustmentType);
                          }}
                        >
                          <DownloadOutlined />
                          <H3Text text="Download products" className="" />
                          <div className="timeline-item-download-button__loading">
                            {getProductBySkusLoading && <LoadingOutlined />}
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 2
                    </div>
                    <div className="timeline-item-person">
                      Add the quantity and unit price against each product without changing SKU code.
                    </div>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 3
                    </div>
                    <div className="timeline-item-person">
                      Upload the sheet and verify the list of products.
                    </div>

                  </div>
                </div>
              </Timeline.Item>
            </Timeline>
            <div className="view__timeline-upload">
              <input
                type="file"
                onChange={(e) => onFileSelect(e)}
                className="view__timeline-upload-input"
                accept=".xlsx,.xls"
              />
            </div>
          </div>
        </div>
        <div className="custom-drawer__footer">
          <H3Button
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            text="+ Add Products"
            isLoading={getProductBySkusLoading}
            onClick={() => addData()}
            disabled={getProductBySkusLoading}
            style={{
              width: '150px',
              borderRadius: '5px',
              padding: '7px 0px',
              marginLeft: '0',
            }}
          />
        </div>
      </div>
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, ProductReducers,
}) => ({
  user: UserReducers.user,
  getProductBySkusLoading: ProductReducers.getProductBySkusLoading,
  getSellerCodesLoading: ProductReducers.getSellerCodesLoading,

});

const mapDispatchToProps = (dispatch) => ({
  getPurchaseWorkflowSample: (tenantId, sampleType) => dispatch(WorkflowActions.getPurchaseWorkflowSample(tenantId, sampleType)),
  getSellerCodes: (sellerCode, orgId, callback) => dispatch(ProductActions.getSellerCodes(sellerCode, orgId, callback)),
  getProductBySkus: (payload, callback) => dispatch(ProductActions.getProductBySkus(payload, callback)),
});

BulkUploadSoForm.propTypes = {
  callback: PropTypes.func,
  getProductBySkusLoading: PropTypes.any,
  user: PropTypes.any,
  getSellerCodes: PropTypes.func,
  userDepartmentId: PropTypes.any,
  selectedSeller: PropTypes.any,
  setData: PropTypes.func,
  filteredData: PropTypes.any,
  setFilteredData: PropTypes.func,
  adjustmentType: PropTypes.string,
  getProductBySkus: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkUploadSoForm));
