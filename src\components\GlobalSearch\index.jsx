import React, { useRef, useState, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

// Ant Design Components
import { Modal, Input, List, Avatar, Tooltip, AutoComplete, Tag, Divider, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

// FontAwesome
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUpLong,
  faChevronUp,
  faArrowTurnDown,
  faArrowUp,
  faArrowDown,
  faQuestion,
  faXmark,
  faUpRightFromSquare,
  faAtom,
} from '@fortawesome/free-solid-svg-icons';

// Utilities & Helpers
import { v4 as uuidv4 } from 'uuid';
import InfiniteScroll from 'react-infinite-scroll-component';
import { ISTDateFormat } from '@Apis/constants';
import { GetGlobalSearch } from '../../modules/globalSearch';
import useCheckPortrait from '../../hooks/useCheckPortrait';

// Helper functions
import { allEntityNames, entityNameBgColors, filterDataBasedOnPermission, getAvatarBgColor, getStatusColor, TruncatedText } from './helpers';

// UI Components
import H3Image from '@Uilib/h3Image';

// Images
import { cdnUrl } from '@Utils/cdnHelper';
// Styles
import './style.scss';

const faIcons = {
  faUpLong,
  faChevronUp,
  faArrowTurnDown,
  faArrowUp,
  faArrowDown,
  faQuestion,
  faXmark,
  faUpRightFromSquare,
  faAtom,
};

const GlobalSearch = ({
  user, MONEY, getGlobalSearch, getGlobalSearchLoading,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const searchInputRef = useRef(null);

  const scrollableDivRef = useRef(null);
  const getDataRef = useRef(null);
  const [searchText, setSearchText] = useState('');
  const [dropDownOptions, setDropDownOptions] = useState([]);
  const [selectedValue, setSelectedValue] = useState([]);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [responseData, setResponseData] = useState([]);
  const [page, setPage] = useState(1);
  const [dataCount, setDataCount] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const listRefs = useRef([]);
  const { isMobile, isMobileOrPortrait } = useCheckPortrait();

  const entityNameOptions = useMemo(() => filterDataBasedOnPermission({
    dataToBeFiltered: [
      { value: 'PRODUCTS', label: 'Products' },
      { value: 'VENDORS', label: 'Vendors' },
      { value: 'CUSTOMERS', label: 'Customers' },
      { value: 'PURCHASE_ORDERS', label: 'Purchase Order' },
      { value: 'SALES_ORDER', label: 'Sales Order' },
      { value: 'GOOD_RECEIVING_NOTES', label: 'Good Receiving Notes' },
      { value: 'INVOICE', label: 'Invoice' },
      { value: 'MANUFACTURING_ORDER', label: 'Manufacturing Order' },
    ],
    entityNames: allEntityNames,
    user,
  }), []);

  const getDataFromApi = ({ pageNo, callback }) => {
    getGlobalSearch({
      query: {
        tenant_id: user?.tenant_info?.tenant_id,
        entity_name: selectedValue.join(','),
        search_keyword: searchText,
        page: pageNo ?? page,
        limit: 100,
      },
    }, (response) => {
      callback(response);
    });
  };

  useEffect(() => {
    if (selectedValue.length || (searchText && searchText !== '/')) {
      getDataRef.current = setTimeout(() => {
        getDataFromApi({
          pageNo: 1,
          callback: (response) => {
            if (response?.data) {
              const newResponseData = filterDataBasedOnPermission({ dataToBeFiltered: response?.data, entityNames: allEntityNames, user });
              const newData = newResponseData.splice(0, 10);
              setPage(1);
              setResponseData(newResponseData);
              setData(newData);
              setDataCount(response.count);
              setSelectedIndex(-1);
              listRefs.current = [];
              scrollableDivRef.current.el.scrollTop = 0;
            }
          },
        });
      }, 300);
    }
    return () => clearTimeout(getDataRef.current);
  }, [searchText, selectedValue]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (isModalOpen) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 0);
    }
  }, [isModalOpen]);

  const faIconComponent = (iconName) => <FontAwesomeIcon icon={iconName && Object.prototype.hasOwnProperty.call(faIcons, iconName) ? faIcons[iconName] : faIcons.faAtom} />;

  const handleChange = (value) => {
    setSearchText(value);

    // Check if input starts with '/' when it's empty
    if (value === '/') {
      setDropDownOptions(entityNameOptions);
    } else {
      setDropDownOptions([]);
    }
  };

  const handleSelect = (value) => {
    setSelectedValue((prevItems) => [...prevItems, value]);
    setDropDownOptions([]);
    setSearchText('');// Reset the input text after selection
  };

  const renderKeyPartIcon = ({ keyPart }) => {
    switch (keyPart.toLowerCase()) {
    case 'shift': { return <FontAwesomeIcon icon={faUpLong} flip="horizontal" size="xs" fontSize="08.3em" />;
    }
    case 'ctrl': { return <FontAwesomeIcon icon={faChevronUp} />;
    }
    case 'enter': { return <FontAwesomeIcon icon={faArrowTurnDown} rotation={90} />;
    }
    case 'up': { return <FontAwesomeIcon icon={faArrowUp} />;
    }
    case 'down': { return <FontAwesomeIcon icon={faArrowDown} />;
    }
    default: { return keyPart;
    }
    }
  };

  const renderKeyPart = (keyPart) => {
    if (['shift', 'ctrl', 'enter', 'up', 'down'].includes(keyPart.toLowerCase())) {
      return (
        <Tooltip
          overlay={(
            <div
              style={{
                fontSize: '12px',
              }}
              className="tooltip__content-shortcut"
            >
              <span style={{ color: 'gray', fontWeight: 500 }}>{keyPart}</span>
            </div>
          )}
          color="#F1F1F1"
          border="1px solid rgb(170, 170, 170)"
        >
          {renderKeyPartIcon({ keyPart })}
        </Tooltip>
      );
    }

    if (keyPart.toLowerCase() === '?') {
      return <FontAwesomeIcon icon={faQuestion} />;
    }

    return keyPart;
  };

  const renderAutoCompleteSelectedTag = (selectedValueData) => (
    <span key={selectedValueData}>
      <Tag color={entityNameBgColors(selectedValueData)} style={{ border: 'none', cursor: 'default' }}>
        <span style={{
          display: 'inline-flex', alignItems: 'center', justifyContent: 'space-between', gap: '5px', paddingBottom: '2px',
        }}
        >
          <span style={{ display: 'inline-block' }}>
            {`${selectedValueData.replaceAll('_', ' ').toProperCase()} `}
          </span>
          <span
            onClick={(e) => {
              e.stopPropagation();
              setSelectedValue((prevItems) => prevItems.filter((item) => selectedValueData !== item));
            }}
            style={{ display: 'inline-block', marginTop: '2px', cursor: 'pointer' }}
          >
            <FontAwesomeIcon size="sm" icon={faXmark} />
          </span>
        </span>
      </Tag>
    </span>
  );

  const loadMoreData = async () => {
    if (loading) {
      return;
    }

    setLoading(true);

    if (responseData.length > 0) {
      setData((prevData) => [...prevData, ...responseData.slice(0, 10)]);
      setResponseData(responseData.slice(10));
      setLoading(false);
    } else if (data.length < dataCount) {
      getDataFromApi({
        pageNo: page + 1,
        callback: (response) => {
          if (response?.data) {
            const newResponseData = filterDataBasedOnPermission({ dataToBeFiltered: response.data, entityNames: allEntityNames, user });
            setData((prevData) => {
              const newData = newResponseData.slice(0, 10);
              return [...prevData, ...newData];
            });
            setResponseData(newResponseData.slice(10));
            setPage((prevPage) => prevPage + 1);
          }
          setLoading(false);
        },
      });
    }
  };

  const scrollToSelectedItem = (index) => {
    if (listRefs.current[index]) {
      listRefs.current[index].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Backspace' && selectedValue.length && !searchText) {
      e.preventDefault();
      setSelectedValue((prevItems) => prevItems.slice(0, -1));
    } else if (!dropDownOptions.length) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex((prev) => {
          const newIndex = prev < data.length - 1 ? prev + 1 : prev;
          scrollToSelectedItem(newIndex);
          return newIndex;
        });
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex((prev) => {
          const newIndex = prev > 0 ? prev - 1 : 0;
          scrollToSelectedItem(newIndex);
          return newIndex;
        });
      } else if (e.key === 'Enter' && selectedIndex !== -1) {
        window.open(data[selectedIndex].redirect_url, '_blank');
      }
    }
  };

  const rightSideDetailsRenderer = (record) => {
    const item = record;
    return (
      <React.Fragment>
        {item.right_side_details.filter((rightSideData) => rightSideData?.label_value).map((rightSideData, rightSideDataIndex) => (
          <React.Fragment key={rightSideData?.label_name + rightSideData.label_value}>
            <div style={{ color: 'lightgray' }}>
              {rightSideDataIndex !== 0 ? <span>|&nbsp;</span> : ''}
            </div>
            <Tooltip
              title={(
                <span style={{ color: 'gray', fontSize: '12px' }}>
                  <span style={{ fontWeight: '500' }}>
                    {rightSideData?.label_name}
                  </span>
                  {': '}
                  {rightSideData.label_type && rightSideData.label_type === 'price'
                    ? MONEY(rightSideData.label_value, user?.tenant_info?.org_default_currency_info?.currency_code)
                    : rightSideData.label_type && rightSideData.label_type === 'status'
                      ? rightSideData.label_value?.replaceAll('_', ' ').toProperCase()
                      : rightSideData.label_value?.replaceAll('_', ' ')}
                </span>
              )}
              color="#ffffff"
            >
              <span style={{ fontSize: '12px' }}>
                <span style={{
                  display: 'inline-block', width: 'fit-content', lineHeight: '15px', textAlign: 'right', cursor: 'default',
                }}
                >
                  <div {...rightSideData.label_type && rightSideData.label_type === 'status' ? getStatusColor(rightSideData.label_value) : { style: { color: '#808080' } }}>
                    {rightSideData.label_type && rightSideData.label_type === 'price'
                      ? MONEY(rightSideData.label_value, user?.tenant_info?.org_default_currency_info?.currency_code)
                      : rightSideData.label_type && rightSideData.label_type === 'status'
                        ? rightSideData.label_value?.replaceAll('_', ' ').toProperCase()
                        : <TruncatedText text={rightSideData.label_value?.replaceAll('_', ' ')} maxLength={25} />}
                  </div>
                </span>
              </span>
            </Tooltip>
            &nbsp;
          </React.Fragment>
        ))}
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      {/* Button to open the search */}
      <div
        id="global-search-button"
        className="global-search-button"
        onClick={isModalOpen === true ? closeModal : showModal}
        onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#f2f2f2'; }}
        onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = '#FFFFFF'; }}
      >
        <div
          className="global-search-button__beta__wrapper hide__in-mobile"
        >
          <Tooltip
            title={(
              <span style={{ color: 'gray', fontSize: '12px' }}>
                This feature is currently under BETA testing.
              </span>
            )}
            color="#ffffff"
          >
            {/* <span style={{
              fontWeight: 'bolder', fontSize: '10px', textAlign: 'right', color: 'gray',
            }}
            >
              BETA
            </span> */}
          </Tooltip>
        </div>
        {/* <div style={{
          position: 'absolute', top: '-11px', left: '-9px', width: '23px', height: '23px', pointerEvents: 'none',
        }}
        >
          <H3Image src={newIcon} className="new_icon" />
        </div> */}
        {/* <div className="circles" style={{ pointerEvents: 'none' }}>
          <div className="circle1" />
          <div className="circle2" />
          <div className="circle3" />
        </div> */}
        <H3Image
          src={cdnUrl('global-search.png', 'icons')}
          className="global-search-magnifier-icon"
          alt="search"
        />
        <span className="global-search-button_placeholder-text">{isMobileOrPortrait ? 'Search' : 'Press / to Search'}</span>
      </div>
      {/* Search Modal */}
      <Modal
        open={isModalOpen}
        onCancel={closeModal}
        footer={null}
        width={900}
        className="global-search-modal"
        centered
        motion={null} // Disable animation
      >
        <div style={{
          padding: '14px',
          borderBottom: '1px solid rgb(233, 233, 233)',
        }}
        >
          <AutoComplete
            options={dropDownOptions?.filter((option) => !selectedValue.includes(option.value))}
            onSelect={handleSelect}
            onSearch={handleChange}
            value={searchText}
            onKeyDown={handleKeyDown}
          >
            <Input
              ref={searchInputRef}
              placeholder="Enter command, search, or type '/' for options"
              prefix={(
                <span style={{
                  display: 'inline-flex', height: '30px', boxSizing: 'border-box', alignItems: 'center', gap: '5px',
                }}
                >
                  <SearchOutlined />
                  {selectedValue.length > 0 ? (
                    <React.Fragment>
                      <span className="global-search-modal__input-tag__desktop">
                        {selectedValue.slice(0, 3).map((selectedValueData) => renderAutoCompleteSelectedTag(selectedValueData))}

                        {selectedValue.length > 3 && (
                          <Tooltip
                            color="#ffffff"
                            title={(
                              <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                                {selectedValue.slice(3, selectedValue.length).map((selectedValueData) => renderAutoCompleteSelectedTag(selectedValueData))}
                              </div>
                            )}
                          >
                            <Tag style={{ border: 'none', cursor: 'default' }}>
                              +
                              {selectedValue.length - 3}
                            </Tag>
                          </Tooltip>
                        )}
                      </span>
                      <span className="global-search-modal__input-tag__mobile">
                        <Tooltip
                          color="#ffffff"
                          title={(
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                              {selectedValue.map((selectedValueData) => renderAutoCompleteSelectedTag(selectedValueData))}
                            </div>
                          )}
                        >
                          <Tag style={{ border: 'none', cursor: 'default' }}>
                            +
                            {selectedValue.length}
                          </Tag>
                        </Tooltip>
                      </span>
                    </React.Fragment>
                  ) : <span>&nbsp;</span>}

                </span>

              )}
              size="large"
              autoFocus
              className="global-search-modal__input"
            />
          </AutoComplete>

          <div
            className="global-search-modal__tag"
          >
            {entityNameOptions.map((entityName) => (
              <Tag
                color={selectedValue.includes(entityName.value) ? entityNameBgColors(entityName.value) : ''}
                style={{
                  cursor: 'pointer', boxSizing: 'border-box', height: '25px', margin: '0px 8px 8px 0px',
                }}
                onClick={() => {
                  if (!selectedValue.includes(entityName.value)) {
                    setSelectedValue((prevItems) => [...prevItems, entityName.value]);
                  }
                }}
                key={entityName.value}
              >
                <span style={{
                  display: 'inline-flex', alignItems: 'center', justifyContent: 'space-between', gap: '5px', paddingBottom: '2px', boxSizing: 'border-box', height: '100%',
                }}
                >
                  <span style={{ display: 'inline-block' }}>
                    {entityName.label.toProperCase()}
                  </span>
                  {selectedValue.includes(entityName.value) ? (
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedValue((prevItems) => prevItems.filter((item) => entityName.value !== item));
                      }}
                      style={{ display: 'inline-block', paddingTop: '2px' }}
                    >
                      {' '}
                      <FontAwesomeIcon size="sm" icon={faXmark} />
                    </span>
                  ) : ''}
                </span>
              </Tag>
            ))}
          </div>
        </div>

        <div
          id="scrollableDiv"
        >
          <InfiniteScroll
            ref={scrollableDivRef}
            dataLength={data.length}
            next={loadMoreData}
            hasMore={data.length < dataCount}
            loader={<Spin key="loading" style={{ display: 'block', textAlign: 'center', marginTop: 4 }} />}
            endMessage={data.length ? <Divider plain>It is all, nothing more</Divider> : ''}
            height={isMobile ? 300 : 400}
            scrollThreshold={0.95}
            scrollableTarget="scrollableDiv"
          >
            <div className="global-search-list">
              <List
                dataSource={data || []}
                loading={getGlobalSearchLoading || loading}
                renderItem={(searchData, index) => {
                  const item = searchData;
                  return (
                    <List.Item
                      key={item.entity_id + uuidv4()}
                      ref={(el) => (listRefs.current[index] = el)} // Store ref for scrolling
                      style={{
                        backgroundColor: index === selectedIndex ? '#f0f8ff' : 'transparent',
                        cursor: 'default',
                      }}
                    >
                      <List.Item.Meta
                        style={{ paddingLeft: '3px' }}
                        avatar={<Avatar style={getAvatarBgColor(item.entity_name)} icon={faIconComponent(item.icon)} />}
                        title={item.title.length > 28
                          ? (
                            <Tooltip
                              title={(
                                <span style={{ color: 'gray', fontSize: '12px' }}>
                                  {item.title}
                                </span>
                              )}
                              color="#ffffff"
                            >
                              <a
                                style={{
                                  display: 'inline-flex',
                                  alignItems: 'baseline',
                                  color: '#2d98ff',
                                  gap: '3px',
                                  width: 'fit-content',
                                }}
                                href={item.redirect_url}
                                target="_blank"
                                rel="noreferrer"
                              >
                                <TruncatedText text={item.title} maxLength={28} />
                                <FontAwesomeIcon icon={faUpRightFromSquare} size="2xs" />
                              </a>
                            </Tooltip>
                          ) : (
                            <a
                              style={{
                                display: 'inline-flex',
                                alignItems: 'baseline',
                                color: '#2d98ff',
                                gap: '3px',
                                width: 'fit-content',
                              }}
                              href={item.redirect_url}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <TruncatedText text={item.title} maxLength={28} />
                              <FontAwesomeIcon icon={faUpRightFromSquare} size="2xs" />
                            </a>
                          )}
                        description={(
                          <React.Fragment>
                            <span style={{ display: 'inline-block' }} className="global-search-list__left-side-details">
                              {item.left_side_details.filter((leftSideData) => leftSideData?.label_value).map((leftSideData, leftSideDataIndex) => (
                                <Tooltip
                                  title={(
                                    <span style={{ color: 'gray', fontSize: '12px' }}>
                                      <span style={{ fontWeight: '500' }}>
                                        {leftSideData?.label_name}
                                      </span>
                                      {': '}
                                      {leftSideData.label_type === 'date' ? ISTDateFormat(leftSideData.label_value, 'DD/MM/YYYY') : leftSideData.label_value}
                                    </span>
                                  )}
                                  color="#ffffff"
                                  key={leftSideData?.label_name + leftSideData.label_value}
                                >
                                  <span style={{ fontSize: '12px' }}>
                                    {leftSideDataIndex !== 0 ? <span style={{ color: 'lightgray' }}>&nbsp;|&nbsp;</span> : ''}
                                    <span style={{ display: 'inline-block', cursor: 'default' }}>
                                      <span style={{ width: 'fit-content' }}>
                                        {leftSideData.label_type === 'date' ? ISTDateFormat(leftSideData.label_value, 'DD/MM/YYYY') : <TruncatedText text={leftSideData.label_value} maxLength={20} />}
                                      </span>
                                    </span>
                                  </span>
                                </Tooltip>
                              ))}
                            </span>
                            <span className="global-search-list__right-side-details__mobile">
                              {
                                rightSideDetailsRenderer(item)
                              }
                            </span>
                          </React.Fragment>
                        )}
                      />
                      <div style={{ width: '15%' }} className="global-search-list__middle_divider hide__in-mobile" />
                      <div
                        className="global-search-list__right-side-details hide__in-mobile"
                      >
                        {
                          rightSideDetailsRenderer(item)
                        }
                      </div>
                    </List.Item>
                  );
                }}
              />
            </div>

          </InfiniteScroll>
        </div>

        <div
          className="global-search-modal__footer_wrapper"
        >
          <div
            className="global-search-modal__footer_left hide__in-mobile"
          >
            {[
              { text: 'Exit', keys: 'Esc' },
              { text: 'Navigate', keys: 'UP' },
              { text: '', keys: 'Down' },
              { text: 'Open', keys: 'Enter' },
            ].map((item, index) => (
              <div key={JSON.stringify(item)}>
                {item.text ? (
                  <span style={{ fontWeight: 'bolder', color: 'gray' }}>
                    {index !== 0 ? <span style={{ color: 'lightgray' }}>| &nbsp;</span> : ''}
                    {item.text}
                    {' '}
                  </span>
                ) : ''}
                <span>
                  {item.keys.split('+').map((keyPart, index) => (
                    <span key={keyPart}>
                      {index !== 0 ? '+' : ''}
                      <span
                        key={keyPart}
                        style={{
                          padding: `${keyPart === 'Ctrl' ? '6px 8px' : '6px 12px'}`,
                        }}
                        className="shortcut__keys"
                      >
                        {renderKeyPart(keyPart)}
                      </span>
                    </span>
                  ))}
                </span>
              </div>
            ))}
          </div>

          <div
            className="global-search-modal__footer_right"
          >
            <Tooltip
              title={(
                <span style={{ color: 'gray', fontSize: '12px' }}>
                  This feature is currently under BETA testing.
                </span>
              )}
              color="#ffffff"
            >
              <div style={{
                display: 'flex', flexWrap: 'nowrap', alignItems: 'end', gap: '3px',
              }}
              >
                <span style={{
                  fontWeight: 'bold', fontSize: '12px', textAlign: 'right', color: 'gray',
                }}
                >
                  BETA
                </span>
                <span style={{ fontSize: '9px', textAlign: 'right', paddingBottom: '1.5px' }}>version</span>
              </div>
            </Tooltip>
          </div>

        </div>

      </Modal>

      {/* Dimming Effect */}
      <style jsx>
        {`
        .global-search-modal .ant-modal-content {
          overflow: hidden;
          padding: 0px 0px 0px 0px;
        }
        body.ant-scrolling-effect {
          overflow: hidden;
        }
        body.ant-scrolling-effect::after {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.4);
          z-index: 999;
        }
        .global-search-magnifier-icon {
          width: 22px;
          margin: 0px 6px 0px 0px;
          padding: 0px;
          margin-top: 0px;

          &:hover {
            cursor: pointer;
          }
        }
        .ant-modal {
          transition: none !important;
        }
        .ant-modal-content {
          animation-duration: node !important;
        }
        .ant-modal-mask {
          animation-duration: 0.3s !important;
        }
      `}
      </style>
    </React.Fragment>
  );
};

const mapStateToProps = ({ UserReducers, GetGlobalSearch }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  getGlobalSearchLoading: GetGlobalSearch.loading,
  getGlobalSearchData: GetGlobalSearch.data,
});

const mapDispatchToProps = (dispatch) => ({
  getGlobalSearch: (payload, callback) => dispatch(GetGlobalSearch.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(GlobalSearch));
