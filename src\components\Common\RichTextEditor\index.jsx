import React, { Suspense, useEffect } from 'react';
import PropTypes from 'prop-types';
import './style.scss';

const ReactQuill = React.lazy(() => import('react-quill'));

const modules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', { color: [] }, { background: [] }],
    ['clean'],
  ],
};

const formats = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'align',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'color',
  'background'];

const RichTextEditor = ({ value, disabled, onChange, placeholder }) => {
  useEffect(() => {
    import('react-quill/dist/quill.snow.css');
  }, []);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ReactQuill
        readOnly={disabled}
        theme="snow"
        value={value}
        modules={modules}
        formats={formats}
        onChange={onChange}
        placeholder={placeholder || 'enter terms and conditions here..'}
        className="rich-text-editor__wrapper"
      />
    </Suspense>
  );
};

RichTextEditor.propTypes = { value: PropTypes.any, onChange: PropTypes.func, disabled: PropTypes.bool };
export default RichTextEditor;
