/* eslint-disable array-callback-return */
/* eslint-disable no-use-before-define */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
// React & Libraries
import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';

// Ant Design
import {
  Steps,
  Alert,
  Upload,
  notification,
} from 'antd';
import {
  InboxOutlined,
  LoadingOutlined,
} from '@ant-design/icons';

// Helpers & Constants
import Constants from '@Apis/constants';

// Components
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import PRZSelect from '../../../../../Common/UI/PRZSelect';
import { BulkUploadPO } from '../../../../../../modules/purchase/purchaseOrder';

// Styles
import './style.scss';

const { Dragger } = Upload;
/**
 *
 */
function BulkImportPO({
  user, history, bulkUploadPOResponse, bulkUploadPOError, bulkUploadPOLoading, uploadBulkPO,
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [sheetHeaders, setSheetHeaders] = useState(null);
  const [sheetData, setSheetData] = useState(null);
  const [readingSheet, setReadingSheet] = useState(false);
  const [poType, setPoType] = useState('PURCHASE_ORDER');

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setReadingSheet(true);
      setFileList([file]);
      return false;
    },
    fileList,
    disabled: readingSheet,
    loading: readingSheet,
  };

  const onSubmit = () => {

    const data = new FormData();
    data.append('file', fileList[0]);
    const payload = {
      file: data,
    };
    uploadBulkPO(payload);
  };

  const getHeaders = (sheet) => {
    const range = XLSXUtils.decode_range(sheet['!ref']);
    const headers = [];

    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = sheet[XLSXUtils.encode_cell({ r: range.s.r, c: C })];
      headers.push(headerCell ? headerCell.v : 'undefined');
    }
    return headers;
  };

  useEffect(() => {
    if (fileList?.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();
      reader.onload = (evt) => {
        const bstr = evt.target.result;
        const wb = XLSXRead(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSXUtils.sheet_to_json(ws, { header: 1 });
        const headers = getHeaders(ws);
        setSheetHeaders(headers?.filter((i) => i !== 'undefined'));
        const structuredData = data.slice(1).map((row) => {
          const rowData = {};
          headers?.filter((i) => i !== 'undefined').forEach((header, index) => {
            rowData[header] = row[index];
          });
          return rowData;
        });
        const filteredData = structuredData.filter(row => {
          return Object.values(row)?.some(value => value !== undefined && value !== null && (typeof value === 'string' ? value.trim() !== '' : true));
        });

        if (filteredData.length > 2500) {
          notification.error({
            message: 'You can only upload up to 2500 POs at once.',
            placement: 'top',
            duration: 4,
          });
          setFileList([]);
          setReadingSheet(false);
          return;
        }
        setSheetData(filteredData);

        setReadingSheet(false);
      };
      reader.readAsBinaryString(file);
    }
  }, [fileList]);

  const poOptions = [
    { value: 'PURCHASE_ORDER', label: 'Vendor PO' },
    { value: 'INTER_BUSINESS_UNIT_PO', label: 'Inter Business Unit PO' },
  ];

  return (
    <Fragment>
      <div className="bulk-import__wrapper">
        <div style={{ width: '30%' }}>
          <Steps
            current={currentStep}
            size="small"
            items={[
              {
                title: 'Upload File',
              },
              {
                title: 'Upload Status',
              },
            ]}
          />
        </div>
        {currentStep === 0 ? (
          <div className="bulk-import__step">
            <div className="bulk-import__step-header">
              <div className="bulk-import__form-label">
                Please upload Purchase Orders in CSV/XLSX format
                <span style={{ color: 'red' }}>*</span>
              </div>
              <PRZSelect
                value={poType}
                onChange={(val) => setPoType(val)}
                showSearch={false}
                style={{ width: '180px' }}
                options={poOptions}
              />
            </div>
            <Dragger {...uploadProps}>
              <Fragment>
                <p className="ant-upload-drag-icon">
                  {readingSheet ? <LoadingOutlined /> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text" style={{ fontSize: '13px !important' }}>Click or drag file to this area to upload</p>
                <p className="ant-upload-hint">
                  Please ensure to fill PO NUMBER, BUSINESS UNIT, DEPARTMENT, PURCHASE ORDER DATE,
                  {
                    poType === 'INTER_BUSINESS_UNIT_PO' ? (
                      <React.Fragment>
                        DESTINATION BUSINESS UNIT (BRANCH CODE), DESTINATION DEPARTMENT,
                      </React.Fragment>
                    ) : (
                      <React.Fragment>VENDOR CODE,</React.Fragment>
                    )
                  } PROCUZY SKU CODE, QUANTITY and RATE before uploading
                  <span style={{ color: 'red' }}>*</span>
                </p>

                <p className="ant-upload-hint">
                  (Supported formats .csv,.xlsx; max file size 5 MB)
                </p>
              </Fragment>
            </Dragger>
            <Alert
              showIcon
              message={(
                <Fragment>
                  <div>
                    <a
                      href={`${Constants.BULK_PO_UPLOAD_SHEET}?org_id=${user?.tenant_info?.org_id}&tenant_id=${user?.tenant_info?.tenant_id}&type=${poType}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the data template.
                  </div>
                </Fragment>
              )}
              type="warning"
              style={{ borderRadius: '4px', marginTop: '5px' }}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Upload"
                onClick={() => {
                  onSubmit();
                  setCurrentStep(1);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '9px 15px',
                  width: '100px',
                }}
                disabled={readingSheet || !fileList?.length}
              />
            </div>
          </div>
        ) : ''}
        {currentStep === 1 ? (
          <div className="bulk-import__step">
            {bulkUploadPOLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    Bulk upload of Purchase Order is in progress. Please wait!
                  </div>
                )}
                type="warning"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {(bulkUploadPOResponse?.success) && !bulkUploadPOLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadPOResponse?.success_count} records have been validated and the upload is in progress. Click 'Continue' to refer to Bulk Uploads screen to check the status.`}
                  </div>
                )}
                type="success"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUploadPOError?.error_count && !bulkUploadPOLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadPOError?.error_count} Records Failed.`}
                    <br />
                    <a
                      href={bulkUploadPOError?.records_errored_file}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the records with error.
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {!!bulkUploadPOResponse && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Continue"
                  onClick={() => {
                    history.push('/analytics/bulkUpload');
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={bulkUploadPOLoading}
                  disabled={bulkUploadPOLoading}
                />
              </div>
            )}
          </div>
        ) : ''}
      </div>
    </Fragment>
  );
}

const mapStateToProps = ({
  UserReducers, BulkUploadPO,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  bulkUploadPOResponse: BulkUploadPO.data,
  bulkUploadPOLoading: BulkUploadPO.loading,
  bulkUploadPOError: BulkUploadPO.error,
});

const mapDispatchToProps = (dispatch) => ({
  uploadBulkPO: (payload, callback) => dispatch(BulkUploadPO.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkImportPO));
