import Decimal from 'decimal.js';

export const getTotalRmUsage = (bomRawMaterialLines, quantity) => {
  const rawMaterialLines = [];
  const getBomRawMaterials = (bomLines, parentQuantity, actualQuantity) => {
    if (!Array.isArray(bomLines) || bomLines.length === 0) return;

    for (const item of bomLines) {

      const bomLineQty = new Decimal(item?.bom_line_quantity ?? 0);
      const parentQty = new Decimal(parentQuantity ?? 0);
      const actualQty = new Decimal(actualQuantity ?? 0);

      const parentQuantityPerUnit =
        parentQty.isZero() ? new Decimal(0) : bomLineQty.div(parentQty);

      const estimatedUsage = parentQuantityPerUnit.mul(actualQty);

      const estimatedWastage = item?.wastage_quantity != null
        ? new Decimal(item.wastage_quantity)
        : estimatedUsage.mul(
          new Decimal(item?.wastage_percentage || 0).div(100)
        );

      const totalQuantity = estimatedUsage.add(estimatedWastage);

      rawMaterialLines.push({
        productUomInfo: item?.tenant_product_info?.uom_info,
        quantityPerUnit: item?.bom_quantity_per_unit ??
          (item?.bom_quantity ? item.bom_line_quantity / item.bom_quantity : 0),
        parentQuantityPerUnit: item?.parent_quantity_per_unit ?? parentQuantityPerUnit.toNumber(),
        bom_line_id: item?.bom_line_id,
        quantity: totalQuantity.toNumber(),
        wastage: estimatedWastage.toNumber(),
        uom_info: item?.uom_info,
        product_sku_id: item?.tenant_product_info?.product_sku_id ?? item?.product_sku_id,
        internal_sku_code: item?.tenant_product_info?.internal_sku_code ?? item?.product_info?.internal_sku_code,
        ref_product_code: item?.tenant_product_info?.ref_product_code ?? item?.product_info?.ref_product_code,
        product_sku_name: item?.tenant_product_info?.product_sku_name ?? item?.product_info?.product_sku_name,
      });

      getBomRawMaterials(item?.child_bom_lines, item?.bom_line_quantity, totalQuantity);
    }
  };

  getBomRawMaterials(bomRawMaterialLines, new Decimal(quantity ?? 0), new Decimal(quantity ?? 0));

  return rawMaterialLines;
};