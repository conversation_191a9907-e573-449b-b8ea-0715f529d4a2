import React, { useEffect, useMemo, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, withRouter, RouteComponentProps } from 'react-router-dom';

import PRZImageT from '@Components/Common/UI/PRZImageT';
import PRZTextT from '@Components/Common/UI/PRZTextT';

import UserActions from '../../../../actions/userActions';
import Helpers from '@Apis/helpers';
import documentUINamesConstant from '../../../../apis/documentUINamesConstant';

import { cdnUrl } from '@Utils/cdnHelper';
import PendingSkeleton from './PendingSkeleton';

import './style.scss';

import { RootState } from '@Reducers/index';
import helpers from '@Apis/helpers';

// ---------------------- Types ----------------------
interface ActionItem {
  key: keyof PendingActionsState;
  permission: string;
  link: string;
  label: string | ((docNames: Record<string, string>) => string);
  color: 'blue' | 'purple' | 'orange' | 'green';
  check?: (user: RootState['UserReducers']['user']) => boolean;
}

interface PendingActionsState {
  pr_approval_count?: number;
  pi_approval_count?: number;
  expense_approval_count?: number;
  po_approval_count?: number;
  grn_approval_count?: number;
  api_approval_count?: number;
  payment_request_approval_count?: number;
  se_approval_count?: number;
  so_approval_count?: number;
  inv_approval_count?: number;
  sa_approval_count?: number;
  st_approval_count?: number;
  pending_qc_count?: number;
  bom_approval_count?: number;
}

// ---------------------- Color Map ----------------------
const colorMap = {
  blue: { bg: '#E9F7FF', border: '#c1e8ff' },
  purple: { bg: '#F0E7FF', border: '#decfff' },
  orange: { bg: '#FFE6BF', border: '#ffdeac' },
  green: { bg: '#cefad0', border: '#abf7b1' },
};

// ---------------------- Config ----------------------
const ACTION_CONFIG: ActionItem[] = [
  {
    key: 'pr_approval_count',
    permission: Helpers.permissionEntities.PURCHASE_REQUEST,
    link: '/actions/pending-purchase-requests',
    label: 'Material Requests',
    color: 'blue',
  },
  {
    key: 'pi_approval_count',
    permission: Helpers.permissionEntities.PURCHASE_INDENT,
    link: '/actions/pending-purchase-indents',
    label: 'Purchase Indents',
    color: 'blue',
  },
  {
    key: 'expense_approval_count',
    permission: Helpers.permissionEntities.EXPENSES,
    link: '/actions/pending-expenses',
    label: 'Expenses',
    color: 'blue',
  },
  {
    key: 'po_approval_count',
    permission: Helpers.permissionEntities.PURCHASE_ORDER,
    link: '/actions/pending-purchase-orders',
    label: 'Purchase Orders',
    color: 'blue',
  },
  {
    key: 'grn_approval_count',
    permission: Helpers.permissionEntities.GOOD_RECEIVING,
    link: '/actions/pending-good-receiving',
    label: 'Goods Receiving Notes',
    color: 'purple',
  },
  {
    key: 'api_approval_count',
    permission: Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE,
    link: '/actions/pending-account-payable-invoices',
    label: 'Account Payable Invoice',
    color: 'purple',
  },
  {
    key: 'payment_request_approval_count',
    permission: Helpers.permissionEntities.VENDOR_PAYOUT,
    link: '/actions/pending-payment-approvals',
    label: 'Payment Requests',
    color: 'purple',
  },
  {
    key: 'se_approval_count',
    permission: Helpers.permissionEntities.SALES_ESTIMATE,
    link: '/actions/pending-estimates',
    label: (docNames) => `${docNames.estimateUIName}s`,
    color: 'orange',
  },
  {
    key: 'so_approval_count',
    permission: Helpers.permissionEntities.SALES_ORDER,
    link: '/actions/pending-sales-orders',
    label: (docNames) => `${docNames.salesOrderUIName || 'Sales Order'}s`,
    color: 'orange',
  },
  {
    key: 'inv_approval_count',
    permission: Helpers.permissionEntities.INVOICE,
    link: '/actions/pending-invoices',
    label: 'Invoices',
    color: 'orange',
  },
  {
    key: 'sa_approval_count',
    permission: Helpers.permissionEntities.INVENTORY_INDENT,
    link: '/actions/pending-stock-adjustment',
    label: 'Stock Adjustments',
    color: 'green',
  },
  {
    key: 'st_approval_count',
    permission: Helpers.permissionEntities.STOCK_TRANSFER,
    link: '/actions/pending-stock-transfer',
    label: 'Stock Transfers',
    color: 'green',
  },
  {
    key: 'pending_qc_count',
    permission: Helpers.permissionEntities.QUALITY_CHECKS,
    link: helpers.appendSystemDefaultFilterIdInURL('/quality/quality-checks?page=1&limit=10&status=PENDING'),
    label: 'Quality Checks',
    color: 'green',
    check: (user) =>
      Helpers.getTenantEntityPermission(
        user?.user_tenants,
        Helpers.permissionEntities.QUALITY_CHECKS,
        Helpers.permissionTypes.CREATE
      )?.length > 0,
  },
  {
    key: 'bom_approval_count',
    permission: Helpers.permissionEntities.BOM,
    link: '/actions/pending-bom-approvals',
    label: 'Bill of Materials',
    color: 'green',
    check: (user) =>
      Helpers.getTenantEntityPermission(
        user?.user_tenants,
        Helpers.permissionEntities.BOM,
        Helpers.permissionTypes.CREATE
      )?.length > 0,
  },
];

// ---------------------- Component ----------------------
const PendingActions: React.FC<RouteComponentProps> = () => {
  const dispatch = useDispatch();

  const user = useSelector((state: RootState) => state.UserReducers.user);
  const getPendingActionsLoading = useSelector(
    (state: RootState) => state.UserReducers.getPendingActionsLoading
  );
  const pendingActions = useSelector(
    (state: RootState) => state.UserReducers.pendingActions
  ) as PendingActionsState;

  const [documentUINames, setDocumentUINames] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!user?.user_id) return;
    setDocumentUINames(documentUINamesConstant(user));
    dispatch(UserActions.getPendingActions(null, null, null, () => {}));
  }, [user?.user_id, dispatch]);

  const selectedApproverIsEnable =
    user?.tenant_info?.quality_control_config?.sub_modules?.quality_check
      ?.settings?.only_assignees_can_approve_quality_check;

  const actionItems = useMemo(() => {
    return ACTION_CONFIG.map((item) => {
      const { bg, border } = colorMap[item.color];
      const count = pendingActions?.[item.key] ?? 0;

      let link = item.link;
      if (item.key === 'pending_qc_count' && user) {
        const tenant = Helpers.getTenantEntityPermission(
          user?.user_tenants,
          Helpers.permissionEntities.QUALITY_CHECKS,
          Helpers.permissionTypes.CREATE
        )?.join(',');
        link = `${item.link}&tenant=${tenant}&onlyApprover=${selectedApproverIsEnable}`;
      }

      return {
        ...item,
        count,
        bg,
        border,
        link,
        label: typeof item.label === 'function' ? item.label(documentUINames) : item.label,
      };
    });
  }, [pendingActions, documentUINames, user, selectedApproverIsEnable]);

  const hasActions = useMemo(
    () =>
      actionItems.some(
        (item) =>
          item.count > 0 &&
          (item.check ? item.check(user) : Helpers.getPermission(item.permission, Helpers.permissionTypes.READ, user))
      ),
    [actionItems, user]
  );

  if (!hasActions && !getPendingActionsLoading) {
    return (
      <div className="pending-actions__loader">
        <PRZImageT src={cdnUrl('confetti.png', 'icons')} loading="lazy" className="pending-actions__loader-image" />
        <PRZTextT text="There are no pending actions right now" className="pending-actions__loader-text" />
      </div>
    );
  }

  return (
    <div className="pending-actions__wrapper doc-list__wrapper doc-list__tab-content">
      {getPendingActionsLoading ? (
        <PendingSkeleton />
      ) : (
        <div className="ant-row">
          {actionItems.map((item) => {
            const hasPermission = item.check
              ? item.check(user)
              : Helpers.getPermission(item.permission, Helpers.permissionTypes.READ, user);
            if (!hasPermission || item.count <= 0) return null;

            return (
              <div className="ant-col-md-6 ant-col-xs-12" key={item.key}>
                <Link to={item.link}>
                  <div
                    className="pending-actions__item"
                    style={{ backgroundColor: item.bg, borderColor: item.border }}
                  >
                    <PRZTextT
                      className={`pending-actions__item-stat ${getPendingActionsLoading ? 'lcp-skeleton' : ''}`}
                      text={getPendingActionsLoading ? 'Loading...' : `${item.count} ${item.label}`}
                    />
                    <PRZTextT text="Pending on your approval" className="pending-actions__item-text" />
                  </div>
                </Link>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default withRouter(PendingActions);
