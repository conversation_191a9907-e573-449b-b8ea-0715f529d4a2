import React from 'react';
import {
  InboxOutlined,
  DollarOutlined,
  ShoppingOutlined,
  ShopOutlined,
  FileProtectOutlined,
  ExportOutlined,
  SearchOutlined,
} from '@ant-design/icons';

const IconSwitcher = (iconName) => {
  switch (iconName) {
  case 'InboxOutlined': {
    return <InboxOutlined />;
  }
  case 'DollarOutlined': {
    return <DollarOutlined />;
  }
  case 'ShoppingOutlined': {
    return <ShoppingOutlined />;
  }
  case 'ShopOutlined': {
    return <ShopOutlined />;
  }
  case 'FileProtectOutlined': {
    return <FileProtectOutlined />;
  }
  case 'ExportOutlined': {
    return <ExportOutlined />;
  }
  case 'SearchOutlined': {
    return <SearchOutlined />;
  }
  default: {
    return null;
  }
  }
};

export default IconSwitcher;
