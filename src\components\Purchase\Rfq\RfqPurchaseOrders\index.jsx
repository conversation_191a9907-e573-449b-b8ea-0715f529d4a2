import React, {
  useState, useEffect, Fragment,
} from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Button, Checkbox, notification, Popconfirm,
  Select, Table,
} from 'antd';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowDown, faArrowUp } from '@fortawesome/free-solid-svg-icons';
import {
  CloseOutlined, EditOutlined, LoadingOutlined, ShoppingFilled,
} from '@ant-design/icons';
import RfqActions from '@Actions/purchase/rfq/rfqActions';
import { cdnUrl } from "@Utils/cdnHelper";
import './style.scss';
import H3FormInput from '@Uilib/h3FormInput';
import Helpers from '@Apis/helpers';
import RecentTransactions from '@Components/Inventory/ManageProducts/ViewProduct/RecentTransactions';
import H3Text from '@Uilib/h3Text';
import SelectTax from '@Components/Admin/Common/SelectTax';
import { DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import ProductCategoryLabel from '../../../Common/ProductCategoryLabel';
import HideValue from '../../../Common/RestrictedAccess/HideValue';
import PRZSelect from '../../../Common/UI/PRZSelect';
import TaxActions from '@Actions/taxActions';

const { Option } = Select;

const RfqPurchaseOrders = ({
  MONEY, user, acceptedOffers, getAcceptedOffers, getAcceptedOffersLoading, rfqId, selection, allOffers, selectedRfq, createPurchaseOrder, createPurchaseOrderLoading, history, callback, priceMasking, taxesGroup, getTaxes,
}) => {
  const [isUserReady, setIsUserReady] = useState(false);
  const [vendorSelection, setVendorSelection] = useState('');
  const [actionType, setActionType] = useState('DRAFT');
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [tenantLinesMapping, setTenantLinesMapping] = useState([]);
  const [selectedSeller, setSelectedSeller] = useState(null);
  const [sendWhatsappNotification, setSendWhatsappNotification] = useState(false);
  const [checkedRecipients, setCheckedRecipients] = useState(false);
  const [toRecipients, setToRecipients] = useState([]);
  useEffect(() => {
    getAcceptedOffers({
      rfq_id: rfqId,
    }, () => {
    });
    getTaxes(user?.tenant_info?.org_id, 1, 1000, null, true);
  }, [rfqId]);

  /**
   *
   */

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const zeroTax = taxesGroup?.data?.find((tax) => tax?.tax_value === 0);

  useEffect(() => {
    if (acceptedOffers?.length > 0 && allOffers?.length > 0) {
      setVendorSelection(selection);
      const lowestBidSeller = allOffers?.filter((item) => item?.rfq_status === 'ACCEPTED')?.[0]?.seller_info?.seller_id;
      setSelectedSeller(lowestBidSeller);
      const acceptedOffersCopy = JSON.parse(JSON.stringify(acceptedOffers));
      for (let i = 0; i < acceptedOffersCopy?.length; i++) {
        for (let j = 0; j < acceptedOffersCopy[i]?.tenant_rfq_lines?.length; j++) {
          const currentRfqLine = acceptedOffersCopy[i]?.tenant_rfq_lines[j];
          if (selection === 'SINGLE_VENDOR') {
            acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === lowestBidSeller)?.tenant_seller_id;
            acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === lowestBidSeller)?.offer_price;
            acceptedOffersCopy[i].tenant_rfq_lines[j].negotiation_line_id = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === lowestBidSeller)?.negotiation_line_id;
            acceptedOffersCopy[i].tenant_rfq_lines[j].tax_info = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === lowestBidSeller)?.tax_group_info;
          } else {
            acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = currentRfqLine?.accepted_negotiations?.[0]?.tenant_seller_id;
            acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = currentRfqLine?.accepted_negotiations?.[0]?.offer_price;
            acceptedOffersCopy[i].tenant_rfq_lines[j].negotiation_line_id = currentRfqLine?.accepted_negotiations?.[0]?.negotiation_line_id;
            acceptedOffersCopy[i].tenant_rfq_lines[j].tax_info = currentRfqLine?.accepted_negotiations?.[0]?.tax_group_info;
          }
        }
      }
      setTenantLinesMapping(acceptedOffersCopy);
    }
  }, [acceptedOffers, allOffers]);

  const isVendorOverseas = (seller_id) => {

    const currentSeller = allOffers?.find((item) => item?.seller_info?.seller_id === seller_id);

    return currentSeller?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
  };

  const isDataValid = () => {
    let isValid = true;
    tenantLinesMapping?.forEach((item) => {
      item?.tenant_rfq_lines?.forEach((rfqLine) => {
        if (!rfqLine?.quantity || !rfqLine?.offer_price) {
          isValid = false;
        }
      });
    });
    return isValid;
  };

  const handleCreatePo = (withApproval) => {
    setFormSubmitted(true);
    if (isDataValid()) {
      const tenantSellerMapping = [];
      for (let tenantIdx = 0; tenantIdx < tenantLinesMapping?.length; tenantIdx++) {
        const tenantInfo = tenantLinesMapping[tenantIdx]?.tenant_info;
        const sellerMapping = {};
        if (checkedRecipients && toRecipients?.length === 0) {
          notification.error({
            placement: 'top',
            message: 'Please enter email id of recipients',
            duration: 4,
          });
          return;
        }
        for (let rfqIdx = 0; rfqIdx < tenantLinesMapping[tenantIdx]?.tenant_rfq_lines?.length; rfqIdx++) {
          const currentRfqLine = tenantLinesMapping[tenantIdx]?.tenant_rfq_lines[rfqIdx];
          const selectedSellerId = currentRfqLine?.accepted_negotiations?.find((i) => i?.tenant_seller_id == currentRfqLine?.selected_seller)?.seller_id;
          if (sellerMapping.hasOwnProperty(currentRfqLine?.selected_seller)) {
            sellerMapping[currentRfqLine?.selected_seller].push({
              rfqLine: {
                ...currentRfqLine,
                tax_id: isVendorOverseas(selectedSellerId) ? zeroTax?.tax_id : currentRfqLine?.tax_id,
                tax_group_info: isVendorOverseas(selectedSellerId) ? zeroTax : currentRfqLine?.tax_group_info,
              },
              sellerInfo: allOffers?.find((item) => item?.seller_info?.seller_id == selectedSellerId),
            });
          } else {
            sellerMapping[currentRfqLine?.selected_seller] = [{
              rfqLine: {
                ...currentRfqLine,
                tax_id: isVendorOverseas(selectedSellerId) ? zeroTax?.tax_id : currentRfqLine?.tax_id,
                tax_group_info: isVendorOverseas(selectedSellerId) ? zeroTax : currentRfqLine?.tax_group_info,
              },
              sellerInfo: allOffers?.find((item) => item?.seller_info?.seller_id == selectedSellerId),
            }];
          }
        }
        tenantSellerMapping.push({
          tenantInfo,
          sellerMapping,
        });
      }

      const purchaseOrderPayload = [];
      // Iterate over the JSON data
      tenantSellerMapping.forEach((tenantData) => {
        const { tenantInfo } = tenantData;
        // Iterate over each seller mapping for the current tenant
        Object.entries(tenantData.sellerMapping).forEach(([sellerId, rfqLines]) => {
          const sellerInfo = rfqLines[0]?.sellerInfo?.seller_info;
          // Initialize the purchase order lines array
          const purchaseOrderLines = [];
          // Iterate over RFQ lines for the current seller
          rfqLines.forEach((rfqLine) => {
            const {
              product_sku_id,
              product_sku_name,
              offer_price,
              tax_id,
              uom_id,
              quantity,
              rfq_line_id,
              negotiation_line_id,
            } = rfqLine.rfqLine;
            const tax_group_info = rfqLine.rfqLine.tax_group_info;
            const uom_info = [{
              uom_id: rfqLine.rfqLine.uom_info.uom_id,
              uom_name: rfqLine.rfqLine.uom_info.uom_name,
              ratio: rfqLine.rfqLine.uom_info.ratio,
              uqc: rfqLine.rfqLine.uom_info.uqc,
              precision: rfqLine.rfqLine.uom_info.precision,
            }];

            purchaseOrderLines.push({
              product_sku_id,
              product_sku_name,
              offer_price,
              tax_id,
              uom_id,
              tax_group_info,
              uom_info,
              quantity,
              rfq_line_id,
              negotiation_line_id,
            });
          });

          // Add the transformed payload for the current tenant and seller
          purchaseOrderPayload.push({
            tenant_seller_id: sellerId,
            tenant_seller_info: {
              tenant_seller_id: sellerId,
              seller_id: sellerInfo?.seller_id,
              seller_name: sellerInfo?.seller_name,
              gst_number: sellerInfo?.seller_name?.gst_number,
            },
            tenant_id: tenantInfo?.tenant_id,
            purchase_order_lines: purchaseOrderLines,
            remark: `Purchase Order for RFQ#${selectedRfq?.rfq_number}`,
            attachments: [],
            shipping_address_id: tenantInfo.shipping_address_info?.address_id,
            billing_address_id: tenantInfo.billing_address_info?.address_id,
            tenant_department_id: tenantInfo?.default_store_id,
            seller_address_id: sellerInfo?.office_address_id || null,
            payment_terms: [sellerInfo?.default_payment_terms],
            t_and_c: '',
            po_date: dayjs().format('YYYY/MM/DD'),
            status: withApproval ? 'ISSUED' : 'DRAFT',
            rfq_id: selectedRfq?.rfq_id,
            by_pass_tenant_product_validation: true,
            notification_recipients: toRecipients,
            is_automatic_notification_enabled: checkedRecipients,
            is_po_automatic_notification_enabled: sendWhatsappNotification,
          });
        });
      });
      createPurchaseOrder(purchaseOrderPayload, () => {
        setActionType('');
        setFormSubmitted(false);
        callback();
      }, withApproval);
    } else {
      notification.open({
        type: 'error',
        message: 'Please provide input for all mandatory fields',
        duration: 4,
        placement: 'top',
      });
    }
  };

  /**
   *
   * @param tenantId
   * @param rfqLineId
   * @param fieldName
   * @param fieldValue
   */
  const updateField = (tenantId, rfqLineId, fieldName, fieldValue) => {
    const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
    for (let tenantIdx = 0; tenantIdx < acceptedOffersCopy?.length; tenantIdx++) {
      if (acceptedOffersCopy[tenantIdx]?.tenant_info?.tenant_id === tenantId) {
        for (let rfqIdx = 0; rfqIdx < acceptedOffersCopy[tenantIdx]?.tenant_rfq_lines?.length; rfqIdx++) {
          if (acceptedOffersCopy[tenantIdx]?.tenant_rfq_lines[rfqIdx]?.rfq_line_id === rfqLineId) {
            const line = acceptedOffersCopy[tenantIdx]?.tenant_rfq_lines[rfqIdx];
            const taxableValue = line.quantity * line.offer_price;
            acceptedOffersCopy[tenantIdx].tenant_rfq_lines[rfqIdx][fieldName] = fieldValue;
            acceptedOffersCopy[tenantIdx].tenant_rfq_lines[rfqIdx].child_taxes = Helpers.computeTaxation(taxableValue, line.tax_info, line?.delivery_tenant_info?.billing_address_info?.state, line?.tenant_seller_info?.office_address_details?.state)?.tax_info?.child_taxes;
          }
        }
      }
    }
    setTenantLinesMapping(acceptedOffersCopy);
  };

  const getColumns = () => [
    {
      title: 'PRODUCT',
      width: '220px',
      render: (record) => (
        <div className="flex-display" style={{ flexDirection: 'column' }}>
          {record?.product_sku_name}
          <div className="flex-align-center-justify-between" style={{ gap: '10px' }}>
            {record?.product_sku_info?.product_category_info?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={record?.product_sku_info?.product_category_info?.category_path}
                categoryName={record?.product_sku_info?.product_category_info?.category_path.at(
                  -1,
                )}
                containerStyle={{
                  width: 'fit-content',
                }}
              />
            )}
            {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_ORDER, Helpers.permissionTypes.CREATE, user) && (
              <RecentTransactions
                tenantId={record?.tenant_id}
                productSkuId={record?.product_sku_info?.product_sku_id}
                internalSkuCode={record?.product_sku_info?.internal_sku_code}
                refProductCode={record?.product_sku_info?.ref_product_code}
                productSkuName={record?.product_sku_name}
              />
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'UNIT',
      render: (record) => record?.uom_info?.uqc?.toProperCase() || '',
    },
    {
      title: 'QUANTITY',
      width: '100px',
      render: (text, record) => (
        <H3FormInput
          value={record.quantity}
          type="number"
          containerClassName={(formSubmitted && !Number(record.quantity)) ? 'rfq-po__input-error' : ''}
          labelClassName="orgFormLabel"
          inputClassName="orgFormInput"
          onChange={(e) => updateField(record?.delivery_tenant_id, record?.rfq_line_id, 'quantity', e.target.value)}
          required
        />
      ),
    },
    {
      title: 'VENDOR',
      dataIndex: 'sku_offers',
      render: (item, record) => {
        const getPriceDiff = (seller) => {
          if (record?.accepted_negotiations[0]?.offer_price) {
            const diff = (record?.accepted_negotiations[0]?.offer_price / seller?.offer_price) * 100;
            return diff?.toFixed(2);
          }
          return null;
        };
        return (
          <div className="rfq-po__line-line-seller-dropdown__wrapper">
            <PRZSelect
              value={record?.selected_seller}
              filterOption={false}
              showSearch
              placeholder="Select Vendor"
              className="rfq-po__line-line-seller-dropdown"
              onChange={(slr) => {
                const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
                for (let i = 0; i < acceptedOffersCopy?.length; i++) {
                  if (acceptedOffersCopy[i]?.tenant_info?.tenant_id == record?.delivery_tenant_id) {
                    for (let j = 0; j < acceptedOffersCopy[i]?.tenant_rfq_lines?.length; j++) {
                      if (acceptedOffersCopy[i]?.tenant_rfq_lines[j]?.rfq_line_id === record?.rfq_line_id) {
                        acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = slr;
                        acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = record?.accepted_negotiations?.find((k) => (k?.tenant_seller_id == slr))?.offer_price;
                        acceptedOffersCopy[i].tenant_rfq_lines[j].negotiation_line_id = record?.accepted_negotiations?.find((k) => (k?.tenant_seller_id == slr))?.negotiation_line_id;
                      }
                    }
                  }
                }
                setTenantLinesMapping(acceptedOffersCopy);
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              disabled={vendorSelection === 'SINGLE_VENDOR'}
            >
              {record?.accepted_negotiations
                ?.map((seller, index) => (
                  <Option
                    key={seller.tenant_seller_info?.tenant_seller_id}
                    value={seller?.tenant_seller_info?.tenant_seller_id}
                  >
                    <div className="rfq-po__line-line-seller-name-wrapper">
                      <div className="rfq-po__line-line-seller-name">
                        {seller?.tenant_seller_info?.seller_name}
                      </div>
                      {index > 0 ? (
                        <div className="rfq-po__line-line-seller-percentage" style={{ backgroundColor: getPriceDiff(seller) > 0 ? '#EE404C' : '#1AC05D' }}>
                          {getPriceDiff(seller) > 0 ? <FontAwesomeIcon icon={faArrowUp} /> : <FontAwesomeIcon icon={faArrowDown} />}
                          &nbsp;
                          {`${getPriceDiff(seller)}%   ${MONEY(seller?.offer_price)}`}
                        </div>
                      ) : <H3Text text="Best Offer" className="rfq-po__line-line-seller-label" />}
                    </div>
                  </Option>
                ))}
            </PRZSelect>
          </div>
        );
      },
    },
    {
      title: 'TAX',
      width: '120px',
      render: (item, record) => (
        <div style={{ width: '114px' }}>
          {!isVendorOverseas(record?.accepted_negotiations?.find((k) => (k?.tenant_seller_id == record?.selected_seller))?.seller_id) ? (
            <SelectTax
              onChange={(value) => {
                const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
                for (let tenantIdx = 0; tenantIdx < acceptedOffersCopy?.length; tenantIdx++) {
                  if (acceptedOffersCopy[tenantIdx]?.tenant_info?.tenant_id === record?.delivery_tenant_id) {
                    for (let rfqIdx = 0; rfqIdx < acceptedOffersCopy[tenantIdx]?.tenant_rfq_lines?.length; rfqIdx++) {
                      if (acceptedOffersCopy[tenantIdx]?.tenant_rfq_lines[rfqIdx]?.rfq_line_id === record?.rfq_line_id) {
                        acceptedOffersCopy[tenantIdx].tenant_rfq_lines[rfqIdx].tax_id = value?.tax_id;
                        acceptedOffersCopy[tenantIdx].tenant_rfq_lines[rfqIdx].tax_info = value;
                        acceptedOffersCopy[tenantIdx].tenant_rfq_lines[rfqIdx].tax_group_info = value;
                      }
                    }
                  }
                }
                setTenantLinesMapping(acceptedOffersCopy);
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              selectedGST={record.tax_id}
              customStyle={{
                // border: (formSubmitted && !record.tax_id) ? ' 1px solid #ff4d4f' : '1px solid rgba(68, 130, 218, 0.2)',
                borderRadius: '2px',
                height: '28px',
                width: '114px',
                fontSize: '13px',
              }}
            />
          ) : '-'}
        </div>
      ),
      hidden: vendorSelection === 'SINGLE_VENDOR' && isVendorOverseas(selectedSeller),
    },
    {
      title: 'UNIT PRICE',
      render: (text, record) => (
        <H3FormInput
          value={record.offer_price}
          type="number"
          containerClassName={(formSubmitted && !Number(record.offer_price)) ? 'rfq-po__input-error' : ''}
          labelClassName="orgFormLabel"
          inputClassName="orgFormInput"
          onChange={(e) => updateField(record?.delivery_tenant_id, record?.rfq_line_id, 'offer_price', e.target.value)}
          required
          disabled={!record?.selected_seller && !record.offer_price}
          hideInput={isDataMaskingPolicyEnable && isHideCostPrice}
          popOverMessage={"You don't have access to view/edit unit price"}
        />
      ),
    },
    {
      title: 'TOTAL',
      render: (record) => ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view total amount"} /> : MONEY(Number((record?.offer_price || 0) * record?.quantity * (1 + (record?.tax_group_info?.tax_value / 100))))),
    },
    {
      title: '',
      render: (text, record) => (
        <Popconfirm
          placement="topRight"
          title="Are you sure exclude this item from purchase orders? You will not be able to add it back."
          onConfirm={() => {
            const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
            for (let i = 0; i < acceptedOffersCopy?.length; i++) {
              if (acceptedOffersCopy[i]?.tenant_info?.tenant_id === record?.delivery_tenant_id) {
                acceptedOffersCopy[i].tenant_rfq_lines = acceptedOffersCopy[i].tenant_rfq_lines?.filter((item) => item?.rfq_line_id != record?.rfq_line_id);
              }
            }
            setTenantLinesMapping(acceptedOffersCopy);
          }}
          okText="Yes"
          cancelText="No"
        >
          <div className="table__delete-button">
            <CloseOutlined />
          </div>
        </Popconfirm>

      ),
    },
  ];

  /**
   *
   * @param lines
   * @returns {number}
   */
  const getTenantPosTotal = (lines) => {
    let total = 0;

    lines?.forEach((record) => {
      const offerPrice = Number(record?.offer_price || 0);
      const quantity = Number(record?.quantity || 0);
      const taxValue = Number(record?.tax_info?.tax_value || 0);
      total += offerPrice * quantity * (1 + (taxValue / 100));
    });

    return total || 0;
  };

  /**
   *
   * @returns {number}
   */
  const getAllPosTotal = () => {
    let total = 0;
    tenantLinesMapping?.filter((item) => item?.tenant_rfq_lines?.length > 0)?.forEach((item) => {
      total += getTenantPosTotal(item?.tenant_rfq_lines);
    });
    return total;
  };

  const getPriceDiff = (seller) => {
    const bestSeller = allOffers?.filter((item) => item?.rfq_status === 'ACCEPTED')[0];
    const bestSellerPrice = bestSeller?.negotiation_status === 'COUNTER_ACCEPTED' ? bestSeller?.negotiation_counter_total : bestSeller?.negotiation_total;
    const sellerPrice = seller?.negotiation_status === 'COUNTER_ACCEPTED' ? seller?.negotiation_counter_total : seller?.negotiation_total;
    const diff = (sellerPrice / bestSellerPrice) * 100;
    return diff.toFixed(2);
  };

  const requestForQuotation = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.is_active;

  return (
    <Fragment>
      {
        getAcceptedOffersLoading || !tenantLinesMapping?.length ? (
          <div>
            <div className="rfq-po__loading">
              <div className="loadingBlock rfq-po__loading-select" />
              <div className="loadingBlock rfq-po__loading-select" />
              <div className="loadingBlock rfq-po__loading-table" />
            </div>
          </div>
        ) : (
          <div className="rfq-po__wrapper">
            <div className="ant-row">
              <div className="ant-col-md-12">
                <H3Text text="Do you want to send entire order to single vendor or multiple vendors?" className="rfq-po__input-label" />
                <PRZSelect
                  value={vendorSelection}
                  onChange={(val) => {
                    setVendorSelection(val);
                    const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
                    for (let i = 0; i < acceptedOffersCopy?.length; i++) {
                      for (let j = 0; j < acceptedOffersCopy[i]?.tenant_rfq_lines?.length; j++) {
                        const currentRfqLine = acceptedOffersCopy[i]?.tenant_rfq_lines[j];
                        if (val === 'SINGLE_VENDOR') {
                          acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === selectedSeller)?.tenant_seller_id;
                          acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id === selectedSeller)?.offer_price;
                        } else {
                          acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = currentRfqLine?.accepted_negotiations?.[0]?.tenant_seller_id;
                          acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = currentRfqLine?.accepted_negotiations?.[0]?.offer_price;
                        }
                      }
                    }
                    setTenantLinesMapping(acceptedOffersCopy);
                  }}
                  bordered
                  options={[
                    { value: 'SINGLE_VENDOR', label: 'Single Vendor' },
                    { value: 'MULTIPLE_VENDOR', label: 'Multiple Vendors' },
                  ]}
                />
              </div>
              <div className="ant-col-md-12" />
              {vendorSelection === 'SINGLE_VENDOR' && (
                <Fragment>
                  <div className="ant-col-md-12">
                    <H3Text text="Please select a vendor" className="rfq-po__input-label" />
                    <PRZSelect
                      filterOption={false}
                      showSearch
                      value={selectedSeller}
                      placeholder="Select Vendor"
                      onChange={(slr) => {
                        setSelectedSeller(slr);
                        const acceptedOffersCopy = JSON.parse(JSON.stringify(tenantLinesMapping));
                        for (let i = 0; i < acceptedOffersCopy?.length; i++) {
                          for (let j = 0; j < acceptedOffersCopy[i]?.tenant_rfq_lines?.length; j++) {
                            const currentRfqLine = acceptedOffersCopy[i]?.tenant_rfq_lines[j];
                            acceptedOffersCopy[i].tenant_rfq_lines[j].selected_seller = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id == slr)?.tenant_seller_id;
                            acceptedOffersCopy[i].tenant_rfq_lines[j].offer_price = currentRfqLine?.accepted_negotiations?.find((item) => item?.tenant_seller_info?.seller_id == slr)?.offer_price;
                          }
                        }
                        setTenantLinesMapping(acceptedOffersCopy);
                      }}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      className="rfq-po__line-seller-dropdown"
                    >
                      {allOffers?.filter((item) => item?.rfq_status === 'ACCEPTED')
                        ?.map((seller, index) => (
                          <Option
                            key={seller?.seller_info.seller_id}
                            value={seller?.seller_info.seller_id}
                          >
                            <div className="rfq-po__line-seller-name-wrapper flex-display flex-align-c">
                              <div className="rfq-po__line-seller-name">
                                {seller?.seller_info?.seller_name}
                              </div>
                              {index > 0 ? (
                                <div className="rfq-po__line-seller-percentage" style={{ backgroundColor: getPriceDiff(seller) > 0 ? '#EE404C' : '#1AC05D' }}>
                                  {getPriceDiff(seller) > 0 ? <FontAwesomeIcon icon={faArrowUp} /> : <FontAwesomeIcon icon={faArrowDown} />}
                                  &nbsp;
                                  {getPriceDiff(seller)}
                                  &nbsp;
                                  {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver /> : MONEY(seller?.negotiation_status === 'COUNTER_ACCEPTED' ? seller?.negotiation_grand_counter_total : seller?.negotiation_grand_total)}
                                </div>
                              ) : (
                                <H3Text
                                  text={(
                                    <React.Fragment>
                                      <span>
                                        Best Offer
                                      </span>
                                      &nbsp;
                                      <span>
                                        {
                                          (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver /> : MONEY(seller?.negotiation_status === 'COUNTER_ACCEPTED' ? seller?.negotiation_grand_counter_total : seller?.negotiation_grand_total)
                                        }
                                      </span>
                                    </React.Fragment>
                                  )}
                                  className="rfq-po__line-seller-label" />
                              )}
                            </div>
                          </Option>
                        ))}
                    </PRZSelect>
                  </div>
                  <div className="ant-col-md-12" />
                </Fragment>
              )}
              <div className="ant-col-md-24">
                {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && (
                  <div className="form__input-row" >
                    <Checkbox
                      disabled={getAcceptedOffersLoading || createPurchaseOrderLoading || !requestForQuotation}
                      checked={sendWhatsappNotification}
                      onChange={() => {
                        setSendWhatsappNotification(!sendWhatsappNotification);
                      }}
                    />
                    <span
                      style={{
                        fontWeight: '500',
                        fontSize: '12px',
                        marginLeft: '5px',
                      }}
                    >
                      Send automatic whatsapp message when order is Issued.
                    </span>
                  </div>
                )}
                <div className="form__input-row">
                  <Checkbox
                    disabled={getAcceptedOffersLoading || createPurchaseOrderLoading || !requestForQuotation}
                    checked={checkedRecipients}
                    onChange={() => {
                      setCheckedRecipients(!checkedRecipients);
                    }}
                  />
                  <span
                    style={{
                      fontWeight: '500',
                      fontSize: '12px',
                      marginLeft: '5px',
                    }}
                  >
                    Send automatic email when order is issued.
                  </span>
                </div>
              </div>
              {checkedRecipients && (
                <div className="ant-col-md-8">
                  <div className="form__input-row__input">
                    <PRZSelect
                      className={(formSubmitted && checkedRecipients && toRecipients?.length === 0) ? 'form__recipients__input-error' : ''}
                      mode="tags"
                      value={toRecipients}
                      filterOption={false}
                      maxTagCount="responsive"
                      onChange={(value) => {
                        const recipients = [];
                        for (let i = 0; i < value?.length; i++) {
                          if (Helpers.validateEmail(value[i])) {
                            recipients.push(value[i]);
                          }
                        }
                        setToRecipients(recipients);
                      }}
                      disabled={getAcceptedOffersLoading || createPurchaseOrderLoading || !requestForQuotation}
                    />
                  </div>
                </div>
              )}
            </div>
            {
              tenantLinesMapping?.filter((item) => item?.tenant_rfq_lines?.length > 0)?.map((item) => (
                <Fragment key={item}>
                  <Table
                    title={() => (
                      <div className="flex-display flex-align-c rfq-po__table-header">
                        <div className="flex-display flex-align-c">
                          <ShoppingFilled />
                          &nbsp;
                          {`${item?.tenant_rfq_lines?.length} Items for ${item?.tenant_info?.tenant_name}`}
                        </div>
                        <div className="rfq-po__table-header-total">
                          {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver /> : MONEY(getTenantPosTotal(item?.tenant_rfq_lines))}
                        </div>
                      </div>
                    )}
                    loading={getAcceptedOffersLoading}
                    columns={getColumns()?.filter((item) => !item.hidden)}
                    size="small"
                    pagination={false}
                    dataSource={item?.tenant_rfq_lines}
                  />
                </Fragment>
              ))
            }
            <div className="custom-drawer__footer">
              <Button
                type="primary"
                onClick={() => {
                  setActionType('DRAFT');
                  handleCreatePo(false);
                }}
                style={{ width: '150px' }}
                disabled={getAcceptedOffersLoading || createPurchaseOrderLoading || !requestForQuotation}
                loading={createPurchaseOrderLoading && actionType === 'DRAFT'}
              >
                Save as Draft
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setActionType('ISSUED');
                  handleCreatePo(true);
                }}
                style={{ width: '150px', marginLeft: '10px' }}
                disabled={getAcceptedOffersLoading || createPurchaseOrderLoading || !requestForQuotation}
                loading={createPurchaseOrderLoading && actionType === 'ISSUED'}
              >
                Save and Issue
              </Button>

              {!requestForQuotation && (
                <Popconfirm
                  placement="topRight"
                  title="This feature is not accessible within your current plan to use this feature contact us."
                  onConfirm={() => window.Intercom('showNewMessage')}
                  okText="Contact Us"
                  cancelText="Cancel"
                >
                  <img
                    className="barcode-restrict"
                    src={cdnUrl("crown2.png", "images")}
                    alt="premium"
                    style={{
                      marginLeft: '8px',
                      marginTop: '5px',
                    }}
                  />
                </Popconfirm>
              )}
              <div className="rfq-po__footer-total">
                {MONEY(getAllPosTotal())}
              </div>
            </div>
          </div>
        )
      }
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, RfqReducers, PurchaseOrderReducers, TaxReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  getAcceptedOffersLoading: RfqReducers.getAcceptedOffersLoading,
  acceptedOffers: RfqReducers.acceptedOffers,
  createPurchaseOrderLoading: PurchaseOrderReducers.createPurchaseOrderLoading,
  priceMasking: UserReducers.priceMasking,
  taxesGroup: TaxReducers.taxesGroup,
});

const mapDispatchToProps = (dispatch) => ({
  getAcceptedOffers: (payload, callback) => dispatch(RfqActions.getAcceptedOffers(payload, callback)),
  createPurchaseOrder: (payload, callback, withApproval) => dispatch(PurchaseOrderActions.createPurchaseOrder(payload, callback, withApproval)),
  getTaxes: (orgId, page, limit, isActive, isGroup) => dispatch(TaxActions.getTaxes(orgId, page, limit, isActive, isGroup)),
});

RfqPurchaseOrders.propTypes = {
  user: PropTypes.any,
  history: PropTypes.any,
  match: PropTypes.any,
  selectedRfq: PropTypes.any,
  getAcceptedOffers: PropTypes.func,
  createPurchaseOrder: PropTypes.func,
  createPurchaseOrderLoading: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(RfqPurchaseOrders));
