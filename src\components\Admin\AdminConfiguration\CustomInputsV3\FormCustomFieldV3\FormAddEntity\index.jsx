import React, {
  Fragment, useEffect, useRef, useState,
} from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Alert,
  Button,
  Checkbox, notification, Select, Tag,
  Tooltip,
} from 'antd';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import FormulaInput from '@Components/Common/FormulaInput';
import H3FormInput from '@Uilib/h3FormInput';
import { InfoCircleOutlined } from '@ant-design/icons';
import PRZSelect from '../../../../../Common/UI/PRZSelect';
import './style.scss';

const { Option } = Select;

const FormAddEntity = (props) => {
  const {
    selectedCFV2, createCFV2Loading, updateCFV2Loading, availableEntities, enabledEntities, tags, onSubmit,
    isDocField, cfGlobal, entity, cfCurrent, user, callback, updateCFV2, selectedEntityCf, cfData, fieldType,
  } = props;

  const [selectedEntity, setSelectedEntity] = useState(null);
  const [selectedField, setSelectedField] = useState(null);
  const [isPrintable, setIsPrintable] = useState(true);
  const [isEditableInCarryForward, setIsEditableInCarryForward] = useState(true);
  const [isEditableInUpdateDocument, setIsEditableInUpdateDocument] = useState(true);
  const [isRequired, setIsRequired] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [defaultExpression, setDefaultExpression] = useState('');
  const [defaultExpressionInitValue, setDefaultExpressionInitValue] = useState('');
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [selectedLineField, setSelectedLineField] = useState('');
  const [printOrder, setPrintOrder] = useState(1);
  const [printPosition, setPrintPosition] = useState('');
  const inputElmRef = useRef();

  const restoreVariables = (formula, idToJsonMap) => {
    try {
      // Define the pattern to match the {{cf_<id>}} placeholders
      const pattern = /{{cf_(\d+)}}/g;

      // Function to replace matched placeholders with the corresponding JSON format
      const replaceMatch = (match, id) => {
        const jsonString = idToJsonMap[id];
        if (!jsonString) {
          throw new Error(`No JSON found for id: ${id}`);
        }
        return `[[${JSON.stringify(jsonString)}]]`;
      };

      // Use replace method to replace all occurrences of the pattern in the formula
      const resultFormula = formula?.replace(pattern, replaceMatch);

      return resultFormula?.replace(/\\"/g, "'");
    } catch (e) {
      console.log(e);
    }
  };

  const getExpression = () => {
    const replaceVariables = (formula) => {
      // Define the pattern to match the variable information blocks
      const pattern = /\[\[{"id":\d+,"value":"[^"]*","title":"[^"]*","text":"[^"]*","field_type":"[^"]*","prefix":"[^"]*"}\]\]/g;
      // Function to replace matched blocks with the desired format
      function replaceMatch(match) {
        const variableInfo = JSON.parse(match)?.[0];
        const variableId = variableInfo?.[0]?.id;
        return `{{cf_${variableId}}}`;
      }

      // Use replace method to replace all occurrences of the pattern in the formula
      const resultFormula = formula?.replace(pattern, replaceMatch);

      return resultFormula;
    };
    const cleanString = (inputString) => {
      // Define a regex pattern to match all valid keyboard characters
      const pattern = /[a-zA-Z0-9`~!@#$%^&*()\-=+[\]{}\\|;:'",<.>\/? ]/g;

      // Use the regex pattern to filter out invalid characters
      const cleanedString = inputString?.match(pattern)?.join('');

      return cleanedString;
    };
    const refExpression = inputElmRef?.current?.value;
    const convertedFormula = replaceVariables(refExpression)?.replaceAll(' ', '');
    const sampleString = cleanString(convertedFormula.replace(/\n/g, '').replace(/{{cf_(\d+)}}|\n/g, (_, id) => 1));
    return {
      convertedFormula: cleanString(convertedFormula)?.replaceAll('cf', 'cf_'),
      sampleString,
    };
  };

  const replacements = {
    BILL_OF_MATERIAL_LINE: 'BOM_RAW_MATERIAL',
    BILL_OF_MATERIAL: 'BOM_FINISHED_GOODS',
    MANUFACTURING_ORDER_LINE: 'MANUFACTURING_ORDER_RAW_MATERIAL',
  };

  const entityOptions = availableEntities
    ?.map((item) => ({
      value: item,
      name: replacements[item] || item,
    }))
    ?.filter((item) => !enabledEntities?.find((i) => i?.entity_name === item?.name)?.cf_id)
    ?.map((item) => ({
      label: item.name.replaceAll('_', ' ').toProperCase?.() || item.name,
      value: item.value,
    }));

  useEffect(() => {
    if (!selectedEntityCf) {
      let newEntity = '';
      if (entity === 'MANUFACTURING_ORDER_LINE') {
        newEntity = 'MANUFACTURING_ORDER_FINISHED_GOOD';
      } else {
        newEntity = 'BILL_OF_MATERIAL';
      }
      setSelectedLineField(newEntity);
    }
  }, []);
  useEffect(() => {
    if (selectedEntityCf) {
      const isRestrictedField = ['Rate', 'Quantity'].includes(selectedCFV2?.field_name);
      setIsPrintable(isRestrictedField ? false : selectedEntityCf?.is_printable);
      setPrintOrder(selectedEntityCf?.print_order);
      setPrintPosition(selectedEntityCf?.print_position);
      setIsRequired(isRestrictedField ? true : selectedEntityCf?.is_required);
      setIsEditableInCarryForward(isRestrictedField ? true : selectedEntityCf?.is_editable_while_carrying_forward);
      setIsEditableInUpdateDocument(isRestrictedField ? true : selectedEntityCf?.is_editable_on_update);
      setIsActive(isRestrictedField ? true : selectedEntityCf?.is_active);
      setSelectedEntity(selectedEntityCf?.entity_name?.replaceAll('_', ' ')?.toProperCase());
      if (selectedEntityCf?.default_expression) {
        const idToJsonMap = {};
        tags?.forEach((tag) => {
          idToJsonMap[tag?.id] = { ...tag, prefix: '@', field_type: tag?.field_type || tag?.fieldtype };
        });
        setDefaultExpression(selectedEntityCf?.default_expression);
        setDefaultExpressionInitValue(restoreVariables(selectedEntityCf?.default_expression, idToJsonMap));
      } else {
        setDefaultExpression('');
      }
    }
  }, [selectedEntityCf]);

  const isFormulaAvailable = () => {
    if (entity === 'Batch' || selectedEntity === 'Batch') return false; // Explicitly return false for batch

    if (entity) {
      const fields = isDocField ? cfGlobal?.data?.document_custom_fields : cfGlobal?.data?.document_line_custom_fields;
      const selectedCF = fields?.find((item) => item?.cf_id === selectedField);
      const selectedFieldType = selectedCF?.field_type;
      if (selectedCF?.is_system_field && ['Rate', 'Quantity'].includes(selectedCF?.field_name)) {
        return true;
      }
      if (!selectedCF?.is_system_field && ['NUMBERS'].includes(selectedFieldType)) { // removed TEXT, TEXTAREA
        return true;
      }
    } else if (selectedCFV2) {
      if (selectedCFV2?.is_system_field && ['Rate', 'Quantity'].includes(selectedCFV2?.field_name)) {
        return true;
      }
      if (!selectedCFV2?.is_system_field && ['NUMBERS']?.includes(selectedCFV2?.field_type)) { // removed TEXT, TEXTAREA
        return true;
      }
    }
    return false;
  };

  const handleAddCfEntity = () => {
    setFormSubmitted(true);
    const isValid = (isPrintable && isDocField) ? printPosition && printOrder : true;
    if (selectedEntity && isValid) {
      try {
        const payload = {
          entity_name: selectedEntity?.replaceAll(' ', '_')?.toUpperCase(),
          cf_id: selectedCFV2?.cf_id,
          is_printable: isPrintable,
          is_required: isRequired,
          is_editable_while_carrying_forward: isEditableInCarryForward,
          is_editable_on_update: isEditableInUpdateDocument,
          is_active: isActive,
          print_order: printOrder || 0,
          print_position: printPosition || 'SECTION-A',
          cf_entity_id: selectedEntityCf?.cf_entity_id,
          default_expression: '',
        };
        if (isFormulaAvailable() && !isDocField) {
          const fieldExpression = getExpression();
          const mathCheckPattern = /[+\-*/^=]/;
          if (fieldExpression?.sampleString?.trim()) {
            if (!mathCheckPattern.test(fieldExpression?.sampleString)) {
              throw {
                customMessage: 'The entered formula does not have any operators.',
              };
            }
            eval(fieldExpression?.sampleString);
            payload.default_expression = fieldExpression?.convertedFormula;
          }
        }
        onSubmit(payload);
      } catch (error) {
        console.log(error);
        notification.open({
          type: 'error',
          message: error?.customMessage || 'The entered formula is not valid. Please try again.',
          duration: 4,
          placement: 'top',
        });
      }
    }
  };
  const handleLinkCfEntity = () => {
    setFormSubmitted(true);
    const isValid = (isPrintable && isDocField) ? printPosition && printOrder : true;
    if (selectedField && isValid) {
      const fields = isDocField ? cfGlobal?.data?.document_custom_fields : cfGlobal?.data?.document_line_custom_fields;
      const selectedCF = fields?.find((item) => item?.cf_id === selectedField);
      try {
        let fieldExpression;
        if (isFormulaAvailable() && !isDocField) {
          if (fieldExpression?.sampleString?.trim()) {
            const mathCheckPattern = /[+\-*/^=]/;
            if (!mathCheckPattern.test(fieldExpression?.sampleString?.trim())) {
              throw {
                customMessage: 'The entered formula does not have any operators.',
              };
            }
            fieldExpression = getExpression();
            eval(fieldExpression?.sampleString);
          }
        }
        const payload = {
          ...selectedCF,
          applicable_entities: [
            ...(selectedCF?.applicable_entities || []),
            {
              entity_name: ['MANUFACTURING_ORDER_LINE', 'BILL_OF_MATERIAL_LINE', 'BILL_OF_MATERIAL'].includes(entity) ? selectedLineField : ['PRODUCTS', 'PRODUCTS_LINE'].includes(entity) ? 'PRODUCTS' : ['BATCH', 'BATCH_LINE'].includes(entity) ? 'BATCH' : entity,
              cf_id: selectedCF?.cf_id,
              is_printable: isPrintable,
              is_required: isRequired,
              is_editable_while_carrying_forward: isEditableInCarryForward,
              is_editable_on_update: isEditableInUpdateDocument,
              is_active: isActive,
              print_order: printOrder || 0,
              print_position: printPosition || 'SECTION-A',
              default_expression: fieldExpression?.convertedFormula,
            },
          ],
        };
        updateCFV2(payload, callback);
      } catch (error) {
        console.log(error);
        notification.open({
          type: 'error',
          message: error?.customMessage || 'The entered formula is not valid. Please try again.',
          duration: 4,
          placement: 'top',
        });
      }
    }
  };

  return (
    <div className="cf-add-entity-form">
      <div className="ant-row">
        {entity ? (
          <Fragment>
            <div className="ant-col-md-24">
              <div className="orgInputContainer">
                <label className="orgFormLabel">
                  Select field
                  <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                </label>
                <PRZSelect
                  value={selectedField}
                  onChange={(value) => setSelectedField(value)}
                  disabled={createCFV2Loading || updateCFV2Loading}
                  filterOption={(input, option) => {
                    const { label } = option.props;
                    return label?.toLowerCase()?.includes(input.toLowerCase());
                  }}
                  showSearch
                  showError={formSubmitted && !selectedField}
                  errorName="a field"
                  errorClassName="formError"
                >
                  {
                    (isDocField ? cfGlobal?.data?.document_custom_fields : cfGlobal?.data?.document_line_custom_fields)
                      ?.filter((item) => !cfCurrent?.find((i) => i?.cf_id === item?.cf_id)?.cf_id)
                      ?.map((item) => (
                        <Option key={item?.cf_id} value={item?.cf_id} label={item?.field_name}>
                          <div className="flex-display flex-align-c cf-add-entity-form__option">
                            {item?.field_name}
                            &nbsp;
                            {' '}
                            {item?.is_system_field && <Tag color="volcano">System</Tag>}
                            <span className="margin-left-auto">{item?.field_type}</span>
                          </div>
                        </Option>
                      ))
                  }
                </PRZSelect>
              </div>
            </div>
            {['BILL_OF_MATERIAL_LINE', 'BILL_OF_MATERIAL'].includes(entity) && (
              <div className="ant-col-md-24">
                <div className="orgInputContainer">
                  <label className="orgFormLabel">
                    Select Line
                    <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                  </label>
                  <PRZSelect
                    value={selectedLineField}
                    onChange={(value) => setSelectedLineField(value)}
                    disabled={createCFV2Loading || updateCFV2Loading}
                  >
                    <Option key={1} value="BILL_OF_MATERIAL">
                      <div className="flex-display flex-align-c cf-add-entity-form__option">
                        Finished Good Line
                      </div>
                    </Option>
                    <Option key={2} value="BILL_OF_MATERIAL_LINE">
                      <div className="flex-display flex-align-c cf-add-entity-form__option">
                        Raw material Line
                      </div>
                    </Option>
                  </PRZSelect>
                  {formSubmitted && !selectedLineField && <div className="formError">*Please select a line field</div>}
                </div>
              </div>
            )}
            {['MANUFACTURING_ORDER_LINE'].includes(entity) && (
              <div className="ant-col-md-24">
                <div className="orgInputContainer">
                  <label className="orgFormLabel">
                    Select Line
                    <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                  </label>
                  <PRZSelect
                    value={selectedLineField}
                    onChange={(value) => setSelectedLineField(value)}
                    disabled={createCFV2Loading || updateCFV2Loading}
                    showError={formSubmitted && !selectedLineField}
                    errorName={"a line field"}
                    errorClassName={"formError"}
                  >
                    <Option key={1} value="MANUFACTURING_ORDER_FINISHED_GOOD">
                      <div className="flex-display flex-align-c cf-add-entity-form__option">
                        Finished Good Line
                      </div>
                    </Option>
                    <Option key={2} value="MANUFACTURING_ORDER_LINE">
                      <div className="flex-display flex-align-c cf-add-entity-form__option">
                        Raw material Line
                      </div>
                    </Option>
                  </PRZSelect>
                  {formSubmitted && !selectedLineField && <div className="formError">*Please select a line field</div>}
                </div>
              </div>
            )}
          </Fragment>
        )
          : (
            <div className="ant-col-md-24">
              <div className="orgInputContainer">
                <label className="orgFormLabel">
                  Select document
                  <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                </label>
                <PRZSelect
                  value={selectedEntity}
                  filterOption={false}
                  onChange={(value) => setSelectedEntity(value)}
                  disabled={createCFV2Loading || updateCFV2Loading || selectedEntityCf}
                  options={entityOptions}
                  showError={formSubmitted && !selectedEntity}
                  errorName="an entity"
                  errorClassName="formError"
                />
              </div>
            </div>
          )}
        {isFormulaAvailable() && !isDocField && (selectedEntityCf?.entity_name || entity || selectedEntity) !== 'BATCH' && (
          <div className="ant-col-md-24">
            <div className="orgInputContainer">
              <label className="orgFormLabel">
                Formula for calculation
              </label>
              <FormulaInput
                tags={tags
                  ?.filter((item) => cfData?.find((i) => i?.cf_id === item?.id)?.applicable_entities?.find((k) => k?.entity_name === (selectedEntityCf?.entity_name || entity)))
                  ?.filter((item) => ['NUMBERS'].includes(item?.field_type) && item?.id !== selectedCFV2?.cf_id)}
                defaultExpression={defaultExpression}
                setDefaultExpression={(value) => setDefaultExpression(value)}
                inputElmRef={inputElmRef}
                initValue={defaultExpressionInitValue}
              />
            </div>
          </div>
        )}
        <div className="ant-col-md-24">
          <div className="orgInputContainer">
            <Checkbox
              checked={isRequired}
              disabled={createCFV2Loading || updateCFV2Loading || selectedCFV2?.is_system_field}
              onChange={() => setIsRequired(!isRequired)}
            >
              Make Field Mandatory
            </Checkbox>
            <Tooltip title="Ensure this field is required to complete the form. Users must fill it before proceeding.">
              <InfoCircleOutlined
                style={{ color: '#2d7df7', cursor: 'pointer' }}
              />
            </Tooltip>
          </div>
        </div>
        <div className="ant-col-md-24">
          <div className="orgInputContainer">
            <Checkbox
              checked={isActive}
              disabled={createCFV2Loading || updateCFV2Loading || selectedCFV2?.is_system_field}
              onChange={() => setIsActive(!isActive)}
            >
              Enable Custom Field
            </Checkbox>
            <Tooltip title="Activate this custom field so it can be used in the selected document.">
              <InfoCircleOutlined
                style={{ color: '#2d7df7', cursor: 'pointer' }}
              />
            </Tooltip>
          </div>
        </div>
        <div className="ant-col-md-24">
          <div className="orgInputContainer">
            <Checkbox
              checked={isPrintable}
              disabled={createCFV2Loading || updateCFV2Loading || ['Rate', 'Quantity'].includes(selectedCFV2?.field_name)}
              onChange={() => setIsPrintable(!isPrintable)}
            >
              Show in Printouts
            </Checkbox>
            <Tooltip title="Include this field's data in the printed version of the document.">
              <InfoCircleOutlined
                style={{ color: '#2d7df7', cursor: 'pointer' }}
              />
            </Tooltip>
          </div>
        </div>
        <div className="ant-col-md-24">
          <div className="orgInputContainer">
            <Checkbox
              checked={isEditableInUpdateDocument}
              disabled={createCFV2Loading || updateCFV2Loading || ['Rate', 'Quantity'].includes(selectedCFV2?.field_name)}
              onChange={() => setIsEditableInUpdateDocument(!isEditableInUpdateDocument)}
            >
              Editable During Transaction Updates
            </Checkbox>
            <Tooltip title="Allow this field to be modified when the transaction is updated or edited.">
              <InfoCircleOutlined
                style={{ color: '#2d7df7', cursor: 'pointer' }}
              />
            </Tooltip>
          </div>
        </div>
        {!['PRODUCTS', 'VENDORS', 'STOCK_ADJUSTMENT', "CUSTOMERS"].includes(selectedEntityCf?.entity_name) && (
          <div className="ant-col-md-24">
            <div className="orgInputContainer">
              <Checkbox
                checked={isEditableInCarryForward}
                disabled={createCFV2Loading || updateCFV2Loading || ['Rate', 'Quantity'].includes(selectedCFV2?.field_name)}
                onChange={() => setIsEditableInCarryForward(!isEditableInCarryForward)}
              >
                Editable When Carrying Forward
              </Checkbox>
              <Tooltip title="Prevent this field from being editable in carry-forward transactions.">
                <InfoCircleOutlined
                  style={{ color: '#2d7df7', cursor: 'pointer' }}
                />
              </Tooltip>
            </div>
          </div>
        )}
        {isPrintable && isDocField && (
          <React.Fragment>
            <div className="ant-col-md-24">
              <div className="orgInputContainer">
                <label className="orgFormLabel">
                  Print Position
                  <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                </label>
                <PRZSelect
                  value={printPosition}
                  filterOption={false}
                  onChange={(value) => setPrintPosition(value)}
                  disabled={createCFV2Loading || updateCFV2Loading}
                  showError={formSubmitted && !printPosition}
                  errorName={"a printable position"}
                  errorClassName={"formError"}
                >
                  {/* <Option key="HIDE" value="HIDE">Hide</Option> */}
                  <Option key="SECTION-A" value="SECTION-A">Section A</Option>
                  <Option key="SECTION-B" value="SECTION-B">Section B</Option>
                </PRZSelect>
              </div>
            </div>
            <div className="ant-col-md-24">
              <div className="orgInputContainer">
                <label className="orgFormLabel">
                  Print Order
                  <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                </label>
                <H3FormInput
                  name="field placeholder"
                  type="number"
                  inputClassName="orgFormInput"
                  placeholder=""
                  onChange={(e) => setPrintOrder(e.target.value)}
                  disabled={createCFV2Loading || updateCFV2Loading}
                  value={printOrder}
                  maxlength="100"
                />
                {formSubmitted && !printOrder && <div className="formError">*Please enter a print order</div>}
              </div>
            </div>
          </React.Fragment>
        )}
        <div className="custom-drawer__footer">
          <div className="ant-col-md-6">
            <Button
              type="primary"
              onClick={() => (entity ? handleLinkCfEntity() : handleAddCfEntity())}
              loading={createCFV2Loading || updateCFV2Loading}
              disabled={createCFV2Loading || updateCFV2Loading}
            >
              {!selectedCFV2 ? 'Add Custom Field' : 'Update Custom Field'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = ({ UserReducers, CFV2Reducers }) => ({
  user: UserReducers.user,
  createCFV2Loading: CFV2Reducers.createCFV2Loading,
  updateCFV2Loading: CFV2Reducers.updateCFV2Loading,
  cfGlobal: CFV2Reducers.cfGlobal,
});

const mapDispatchToProps = (dispatch) => ({
  createCFV2: (payload, callback) => dispatch(CFV2Actions.createCFV2(payload, callback)),
  updateCFV2: (payload, callback) => dispatch(CFV2Actions.updateCFV2(payload, callback)),
});

FormAddEntity.propTypes = {
  user: PropTypes.any,
  createCFV2Loading: PropTypes.any,
  updateCFV2Loading: PropTypes.any,
  createCFV2: PropTypes.func,
  updateCFV2: PropTypes.func,
  callback: PropTypes.func,
  selectedCFV2: PropTypes.any,
  entity: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(FormAddEntity));
