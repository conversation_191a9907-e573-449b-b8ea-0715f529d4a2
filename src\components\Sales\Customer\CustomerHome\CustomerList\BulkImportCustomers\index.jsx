/* eslint-disable array-callback-return */
/* eslint-disable no-use-before-define */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Steps, Alert, Upload, Checkbox, Table, Select, notification
} from 'antd';
import H3Text from '@Uilib/h3Text';
import CustomerActions from '@Actions/customerActions';
import TenantActions from '@Actions/tenantActions';
import Constants from '@Apis/constants';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import { InboxOutlined, LoadingOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCircleCheck, faTriangleExclamation,
} from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import './style.scss';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import Helpers from '@Apis/helpers';
import TenantCheckboxGroup from '../../../../../Common/TenantCheckboxGroup';
import PRZSelect from '../../../../../Common/UI/PRZSelect';

const { Option } = Select;
const { Dragger } = Upload;
/**
 *
 */
function BulkImportCustomers({
  user, history, getBulkUploadCustomerMetadataLoading, getBulkUploadCustomerMetadata, bulkUploadCustomerMetadata, bulkUploadCustomers, bulkUploadCustomersLoading, callback, bulkUploadError, bulkUploadResponse, isBulkUploadCustomers, bulkUpdateCustomers, bulkUpdateError, bulkUpdateResponse, bulkUpdateCustomersLoading, tenants, getTenants, getTenantsLoading, bulkUploadCustomersError, bulkUploadCustomersSuccess, bulkUpdateCustomersError, bulkUpdateCustomersSuccess
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [searchXlsxField, setSearchXlsxField] = useState('');
  const [fileList, setFileList] = useState([]);
  const [sheetHeaders, setSheetHeaders] = useState(null);
  const [sheetData, setSheetData] = useState(null);
  const [readingSheet, setReadingSheet] = useState(false);
  const [columnMappingData, setColumnMappingData] = useState([]);
  const [inCompletedColumnMapping, setInCompletedColumnMapping] = useState(false);
  const [allTenantsSelected, setAllTenantsSelected] = useState(true);
  const [selectedTenants, setSelectedTenants] = useState([]);
  const [showSelectedTenants, setShowSelectedTenants] = useState(false);


  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setReadingSheet(true);
      setFileList([file]);
      return false;
    },
    fileList,
    disabled: getBulkUploadCustomerMetadataLoading || readingSheet,
    loading: getBulkUploadCustomerMetadataLoading || readingSheet,
  };

  const onSubmit = () => {
    const columnMapping = {};
    for (let i = 0; i < columnMappingData?.length; i++) {
      columnMapping[columnMappingData[i]?.procuzy_field_key] = columnMappingData[i]?.xlsx_column;
    }

    const selectedTenantsList = user?.user_tenants.filter((data) => selectedTenants.includes(data.tenant_id));
    const payload = {
      file: fileList[0],
      columnMapping,
      tenants: Helpers.getTenantEntityPermission(selectedTenantsList, Helpers.permissionEntities.CUSTOMER, isBulkUploadCustomers ? Helpers.permissionTypes.CREATE : Helpers.permissionTypes.UPDATE).join(','),
      current_tenant_id: user?.tenant_info?.tenant_id,
    };

    if (isBulkUploadCustomers) {
      bulkUploadCustomers(payload, () => {
        callback();
        history.push('/analytics/bulkUpload');
      });
    } else {
      bulkUpdateCustomers(payload, () => {
        callback();
        history.push('/analytics/bulkUpload');
      });
    }
  };

  const getHeaders = (sheet) => {
    const range = XLSXUtils.decode_range(sheet['!ref']);
    const headers = [];

    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = sheet[XLSXUtils.encode_cell({ r: range.s.r, c: C })];
      headers.push(headerCell ? headerCell.v : 'undefined');
    }
    return headers;
  };

  useEffect(() => {
    getTenants(1, '', true, user?.tenant_info?.org_id, 100, (tenantsData) => {
      const selectedTenantsList = [];
      if (tenantsData?.data?.length > 0) {
        for (let i = 0; i < tenantsData?.data?.length; i++) {
          selectedTenantsList.push(tenantsData?.data?.[i]?.tenant_id);
        }
      }

      setSelectedTenants(selectedTenantsList);
    });
  }, []);

  useEffect(() => {
    if (tenants && !selectedTenants?.length && allTenantsSelected) {
      const newSelectedTenants = tenants?.data?.map((tenant) => tenant.tenant_id);
      setSelectedTenants(newSelectedTenants);
    }
  }, [selectedTenants, allTenantsSelected]);

  useEffect(() => {
    if (fileList?.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();
      reader.onload = (evt) => {
        const bstr = evt.target.result;
        const wb = XLSXRead(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSXUtils.sheet_to_json(ws, { header: 1 });
        const headers = getHeaders(ws);
        setSheetHeaders(headers?.filter((i) => i !== 'undefined'));
        const structuredData = data.slice(1).map((row) => {
          const rowData = {};
          headers?.filter((i) => i !== 'undefined').forEach((header, index) => {
            rowData[header] = row[index];
          });
          return rowData;
        });
        
        const filteredData = structuredData.filter(row => {
          return Object.values(row)?.some(value => value !== undefined && value !== null && (typeof value === 'string' ? value.trim() !== '' : true));
        });

        if (filteredData.length > 2500) {
          notification.error({
              message: 'You can only upload up to 2500 customers at once.',
              placement: 'top',
              duration: 4,
          });
          setFileList([]);
          setReadingSheet(false);
          return;
        }
        setSheetData(filteredData);
        if(selectedTenants?.length>0){
            setCurrentStep(1);
            bulkUpdateCustomersError(null);
            bulkUpdateCustomersSuccess(null);
            bulkUploadCustomersSuccess(null);
            bulkUploadCustomersError(null);
        }
        setReadingSheet(false);
        const CustomerColumns = isBulkUploadCustomers ? bulkUploadCustomerMetadata?.import_columns : bulkUploadCustomerMetadata?.update_columns;
        const columnData = CustomerColumns?.map((i) => (
          {
            key: uuidv4(),
            xlsx_column: null,
            xlsx_column_example: null,
            procuzy_field: i?.column_name,
            procuzy_field_key: i?.column_key,
            required: i?.is_required,
            description: i?.description,
            is_custom_field: i?.is_custom_field,
            mapped: false,
          }
        ));
        setColumnMappingData(columnData);
      };
      reader.readAsBinaryString(file);
    }
  }, [fileList]);

  useEffect(() => {
    getBulkUploadCustomerMetadata();
  }, []);

  const columnMappingColumns = () => {
    const columns = [
      {
        title: 'Import',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '60px',
        render: (text, record) => (
          <div style={{ textAlign: 'center' }}>
            <Checkbox checked={record?.mapped} disabled />
          </div>
        ),
      },
      {
        title: 'Procuzy Field',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '250px',
        render: (text, record) => (
          <Fragment>
            <div className="flex-display flex-align-c">
              <H3Text text={record?.procuzy_field} className="" required={record?.required} />
              {record?.is_custom_field && <div className="status-tag" style={{ marginLeft: '5px' }}>Custom</div>}
            </div>
            {record?.description && <H3Text text={record?.description} className="bulk-import-description" />}
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'XLSX Column',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '266px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.xlsx_column}
              showSearch
              filterOption={false}
              onSelect={(value) => {
                const columnMappingCopy = JSON.parse(JSON.stringify(columnMappingData));
                for (let i = 0; i < columnMappingCopy?.length; i++) {
                  if (columnMappingCopy[i].key === record?.key) {
                    columnMappingCopy[i].xlsx_column = value;
                    columnMappingCopy[i].mapped = !!value;
                  }
                }
                setColumnMappingData(columnMappingCopy);
                setSearchXlsxField('');
              }}
              onSearch={(value) => setSearchXlsxField(value)}
              placeholder="Select field"
              style={{
                width: '250px',
              }}
              allowClear
              onClear={() => {
                const columnMappingCopy = JSON.parse(JSON.stringify(columnMappingData));
                const updatedColumnMapping = columnMappingCopy.map(item => {
                    if (item.key === record?.key) {
                        return {
                            ...item,
                            xlsx_column: null,
                            mapped: false, 
                        };
                    }
                    return item;
                });
                setColumnMappingData(updatedColumnMapping);
              }}
            >
              {sheetHeaders
                ?.filter((item) => item?.toLowerCase()?.includes(searchXlsxField?.toLowerCase()))
                .map(
                  (item, index) => (
                    <Option key={index} value={item} disabled={columnMappingData?.find((i) => i?.procuzy_field === item)?.mapped}>
                      {item}
                    </Option>
                  ),
                )}
            </PRZSelect>
            {sheetData?.[0]?.[record?.xlsx_column]?.toString() ? <H3Text text={`eg. ${sheetData?.[0]?.[record?.xlsx_column]?.toString()}`} className="bulk-import__column-example" /> : ''}
          </Fragment>
        ),
      },
    ];
    return columns;
  };
  /**
     *
     * @return {JSX.Element}
     */
  return (
    <Fragment>
      <div className="bulk-import__wrapper">
        <Steps
          current={currentStep}
          size="small"
          items={[
            {
              title: 'Upload File',
            },
            {
              title: 'Column Mapping',
            },
          ]}
        />
        {currentStep == 0 ? (
          <div className="bulk-import__step">
            <label className="bulk-import__form-label">
              Please upload customers data in CSV/XLSX format
              <span style={{ color: 'red' }}>*</span>
            </label>
            <Dragger {...uploadProps}>
              <Fragment>
                <p className="ant-upload-drag-icon">
                  {readingSheet ? <LoadingOutlined /> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text" style={{ fontSize: '13px !important' }}>Click or drag file to this area to upload</p>
                <p className="ant-upload-hint">
                  (Note - We support up to 2500 customers for a one-time upload)
                  <span style={{ color: 'red' }}>*</span>
                </p>
                <p className="ant-upload-hint">
                  (Supported formats .csv,.xlsx; max file size 5 MB)
                </p>

              </Fragment>
            </Dragger>
            <Alert
              showIcon
              message={(
                <Fragment>
                  <div>
                    <a
                      href={`${isBulkUploadCustomers ? Constants.BULK_CUSTOMER_UPLOAD_SHEET : Constants.BULK_CUSTOMER_UPDATE_SHEET}?org_id=${user?.tenant_info?.org_id}&tenant_id=${user?.tenant_info?.tenant_id}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the data template.
                  </div>
                  <div>
                    {isBulkUploadCustomers ? 'Customer Name, Legal Name, Customer Type are the mandatory fields for customers bulk import.'
                      : 'Only Procuzy Customer code is mandatory field for customers bulk update.'}
                  </div>
                </Fragment>
              )}
              type="warning"
              style={{ borderRadius: '4px', marginTop: '5px' }}
            />
            <div className="ant-col-md-24 ant-col-xs-24">
              <div className="bulk-update-customer-form__tenant-list-wrapper">
                <div className="bulk-update-customer-form__tenant-list-title">
                  <Checkbox
                    onChange={() => {
                      if (!allTenantsSelected) {
                        const defaultSelectedTenants = [];
                        for (let i = 0; i < tenants?.data?.length; i++) {
                          defaultSelectedTenants.push(tenants?.data?.[i]?.tenant_id);
                        }
                        setAllTenantsSelected(!allTenantsSelected);
                        setSelectedTenants(defaultSelectedTenants);
                      } else {
                        setSelectedTenants([]);
                        setAllTenantsSelected(!allTenantsSelected);
                      }
                    }}
                    checked={allTenantsSelected}
                  />
                  <div className="bulk-update-customer-form__tenant-list-title-text">
                    All Business Units
                    <span
                      onClick={() => setShowSelectedTenants(!showSelectedTenants)}
                    >
                      {showSelectedTenants ? "Show less" : "Show all"}
                    </span>
                  </div>
                </div>
                <div
                  style={{
                    marginBottom: '20px',
                  }}
                >
                  {(showSelectedTenants) && (
                    <TenantCheckboxGroup
                      tenants={tenants}
                      onChange={(value) => {
                        setSelectedTenants(value);
                        setAllTenantsSelected(value?.length === tenants?.data?.length);
                      }}
                      selectedValues={selectedTenants}
                    />
                  )}
                </div>
              </div>
            </div>
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  setCurrentStep(1);
                  bulkUpdateCustomersError(null);
                  bulkUpdateCustomersSuccess(null);
                  bulkUploadCustomersSuccess(null);
                  bulkUploadCustomersError(null);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '9px 15px',
                  width: '100px',
                }}
                disabled={readingSheet || !fileList?.length || getTenantsLoading || selectedTenants?.length === 0}
              />
            </div>
          </div>
        ) : ''}
        {currentStep == 1 ? (
          <div className="bulk-import__step">
            {inCompletedColumnMapping && columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length > 0 && (
              <div style={{ marginBottom: '5px' }}>
                <Alert
                  type="error"
                  message={`${columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length} mandatory columns not mapped. Please map all mandatory fields.`}
                  showIcon
                />
              </div>
            )}
            {columnMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="warning"
                  message={`${columnMappingData?.filter((item) => !item?.mapped)?.length} columns not mapped. If you proceed, these columns won't get imported into Procuzy.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {(sheetData.length > 0) && (
              <Fragment>
                <Alert
                  type="info"
                  message={`${sheetData.length} records will be ${isBulkUploadCustomers ? 'imported' : 'updated'} , click on ${isBulkUploadCustomers ? 'import' : 'update'} to proceed.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={columnMappingColumns()}
              bordered={false}
              dataSource={columnMappingData || []}
              pagination={false}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => setCurrentStep(0)}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
              />
              &nbsp; &nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text={isBulkUploadCustomers ? 'Import' : 'Update'}
                onClick={() => {
                  if (columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length) {
                    setInCompletedColumnMapping(true);
                  } else {
                    onSubmit();
                    setCurrentStep(2);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                isLoading={bulkUploadCustomersLoading || bulkUpdateCustomersLoading}
                disabled={bulkUploadCustomersLoading || bulkUpdateCustomersLoading}
              />
            </div>
          </div>
        ) : ''}
        {currentStep == 2 ? (
          <div className="bulk-import__step">
            {(bulkUploadError?.success_count || bulkUploadResponse?.success_count) && !bulkUploadCustomersLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadError?.success_count || bulkUploadResponse?.success_count}  records have been validated and the upload is in progress. Please refer to Bulk Uploads screen to check the status.`}
                  </div>
                )}
                type="success"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUploadError?.error_count && !bulkUploadCustomersLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadError?.error_count} Records Failed.`}
                    <br />
                    <a
                      href={bulkUploadError?.records_errored_file}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the records with error
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {(bulkUploadCustomersLoading || bulkUpdateCustomersLoading) && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => setCurrentStep(0)}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
                disabled={bulkUploadCustomersLoading || bulkUpdateCustomersLoading}
              />
              &nbsp; &nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text={isBulkUploadCustomers ? 'Import' : 'Update'}
                onClick={() => {
                  if (columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length) {
                    setInCompletedColumnMapping(true);
                  } else {
                    onSubmit();
                    setCurrentStep(2);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                isLoading={bulkUploadCustomersLoading || bulkUpdateCustomersLoading}
                disabled={bulkUploadCustomersLoading || bulkUpdateCustomersLoading}
              />
            </div>
            )}
            {bulkUploadError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() =>{
                  history.push('/analytics/bulkUpload')
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
                isLoading={bulkUploadCustomersLoading}
                disabled={bulkUploadCustomersLoading}
              />
            </div>
            )}
            {(bulkUpdateError?.success_count || bulkUpdateResponse?.success_count) && !bulkUpdateCustomersLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUpdateError?.success_count || bulkUpdateResponse?.success_count}  Records are validated and Update In Progress Please Check Status on Bulk Upload Section.`}
                  </div>
                )}
                type="success"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUpdateError?.error_count && !bulkUpdateCustomersLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUpdateError?.error_count} Records Failed.`}
                    <br />
                    <a
                      href={bulkUpdateError?.records_errored_file}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the records with error
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUpdateError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Continue"
                  onClick={() => {
                    history.push('/analytics/bulkUpload')
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={bulkUpdateCustomersLoading}
                  disabled={bulkUpdateCustomersLoading}
                />
              </div>
            )}
          </div>
        ) : ''}
      </div>
    </Fragment>
  );
}

const mapStateToProps = ({
  UserReducers, CustomerReducers, TenantReducers,
}) => ({
  user: UserReducers.user,
  getBulkUploadCustomerMetadataLoading: CustomerReducers.getBulkUploadCustomerMetadataLoading,
  bulkUploadCustomerMetadata: CustomerReducers.bulkUploadCustomerMetadata,
  bulkUploadCustomersLoading: CustomerReducers.bulkUploadCustomersLoading,
  bulkUploadResponse: CustomerReducers.bulkUploadResponse,
  bulkUploadError: CustomerReducers.bulkUploadError,
  bulkUpdateCustomersLoading: CustomerReducers.bulkUpdateCustomersLoading,
  bulkUpdateResponse: CustomerReducers.bulkUpdateResponse,
  bulkUpdateError: CustomerReducers.bulkUpdateError,
  tenants: TenantReducers.tenants,
  getTenantsLoading: TenantReducers.getTenantsLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getBulkUploadCustomerMetadata: () => dispatch(CustomerActions.getBulkUploadCustomerMetadata()),
  bulkUploadCustomers: (payload, callback) => dispatch(CustomerActions.bulkUploadCustomers(payload, callback)),
  bulkUpdateCustomers: (payload, callback) => dispatch(CustomerActions.bulkUpdateCustomers(payload, callback)),
  getTenants: (page, keyword, isVerified, orgId, limit, callback) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId, limit, callback)),
  bulkUploadCustomersError: (empty) => dispatch(CustomerActions.bulkUploadCustomersError(empty)),
  bulkUploadCustomersSuccess: (empty) => dispatch(CustomerActions.bulkUploadCustomersSuccess(empty)),
  bulkUpdateCustomersError: (empty) => dispatch(CustomerActions.bulkUpdateCustomersError(empty)),
  bulkUpdateCustomersSuccess: (empty) => dispatch(CustomerActions.bulkUpdateCustomersSuccess(empty)),
});

BulkImportCustomers.propTypes = {
  user: PropTypes.any,
  tenants: PropTypes.any,
  getTenants: PropTypes.func,
  getTenantsLoading: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkImportCustomers));
