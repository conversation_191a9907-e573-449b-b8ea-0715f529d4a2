// 🧠 React & Redux
import React, {
  Fragment, useEffect, useState, useReducer, useMemo,
} from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

// 🔑 UUID
import { v4 as uuidv4 } from 'uuid';

// 🎨 Ant Design
import {
  Alert, DatePicker, Select, Upload, notification, Button, Drawer,
  Switch, Radio, Checkbox, Tooltip, Card, Popconfirm,
} from 'antd';
import {
  PlusOutlined, PlusCircleFilled, EditFilled, UploadOutlined,
} from '@ant-design/icons';

// 🎯 FontAwesome
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCircleXmark, faPlusCircle, faCircleInfo,
} from '@fortawesome/free-solid-svg-icons';

// 📆 Constants & Helpers
import dayjs from 'dayjs';
import Constants, {
  INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF,
  SERVICE_ENDPOINT, toISTDate,
} from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// 🧩 UI Components
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import PRZSelect from '../../../Common/UI/PRZSelect';
import ChargesTaxInput from '../../../Common/ChargesTaxInput';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';

// 📄 Form & Document Components
import SelectDepartment from '@Components/Common/SelectDepartment';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import TagSelector from '@Components/Common/Selector/TagSelector';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import SelectSellerV2 from '../../../Common/SelectSellerV2';
import SelectTaxType from '../../../Admin/Common/SelectTaxType';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import ErrorHandle from '../../../Common/ErrorHandle';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';
import APInvoiceFormLines from './APInvoiceFormLines';

// 🚀 Actions
import TagActions from '@Actions/tagActions';
import TaxActions from '@Actions/taxActions';
import SellerActions from '@Actions/sellerActions';
import TenantActions from '@Actions/tenantActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';
import * as APInvoiceModule from '../../../../modules/purchase/accountPayableInvoice';
import { GetGRNV2 } from '@Modules/purchase/grn';
import { GetTallyConfigurations } from '../../../../modules/getTallyConfigurations';

// 🧮 Local Helpers & Reducers
import SelectGRNForAPInvoice from './helpers';
import apiErrorList from './APInvoiceErrors';
import {
  reducer,
  actionType,
} from './reducers';
import Footer from './Footer';

// 🎨 Styles
import './style.scss';

const { Option } = Select;

const uploadButtonFormLevel = (
  <Button icon={<UploadOutlined />}>Click to Upload</Button>
);
const uploadButtonForUploadingAttachments = (
  <div>
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  </div>
);

const AccountPayableInvoiceForm = ({ user, MONEY, priceMasking, match, selectedGRNForAPInvoice, getDocCFV2, getCurrencies, getCharges, currenciesResults, cfV2DocAPInvoice, getDocCFV2APInvoiceLoading, getGRNV2, grnV2, getGRNV2Loading, createTag, history, updateAPInvoiceLoading, getAPInvoiceLoading, selectedAPInvoice, getAPInvoice, createAPInvoice, updateAPInvoice, createAPInvoiceLoading, getTaxes, taxesGroup, getSellers, getAPInvoiceSuccess, getTallyConfigurations, tallyConfigurationsLoading, tallyConfigurations,
}) => {
  const initialFormState = {
    formSubmitted: false,
    apInvoiceType: 'GRN',
    selectedCurrencyName: null,
    selectedCurrencyID: null,
    isAutomaticConversionRate: false,
    currencyConversionRate: null,
    cfAPInvoiceDoc: [],
    apInvoiceNumber: '',
    initialAPInvoiceNumber: '',
    docSeqId: null,
    selectedTenant: user?.tenant_info?.tenant_id,
    tenantDepartmentId: null,
    selectedTenantSeller: null,
    apInvoiceTableData: [],
    visibleLineCfs: [],
    selectedSeller: null,
    selectedSellerId: null,
    gstNumber: '',
    invoiceNumber: '',
    apInvoiceDate: dayjs(),
    invoiceDate: dayjs(),
    freightSacCode: '',
    freightTaxInfo: null,
    cfAPInvoiceLine: [],
    isUserReadyOne: false,
    selectedGRNValue: [],
    discountType: 'Percent',
    selectedGRN: null,
    chargeData: [],
    ewayBillNumber: '',
    ewayBillDate: null,
    billOfEntryDate: null,
    billOfEntryNumber: '',
    portCode: '',
    ewayBillList: [],
    selectedTags: [],
    vendorAddress: null,
    isLineWiseDiscount: false,
    discountPercentage: '',
    charge1Name: 'Freight',
    charge1Value: '',
    taxTypeName: 'TDS',
    taxTypeInfo: null,
    freightTaxId: 'Not Applicable',
    freightTax: null,
    openFreightTax: false,
    freightTaxData: {
      child_taxes: [
        {
          tax_amount: 0,
          tax_type_name: '',
        },
      ],
    },
    selectedAddressType: '',
    showAddressDrawer: false,
    terms: '',
    fileList: [],
    taxTypeId: '',
    isAdhocAPInvoiceAllowed: false,
    updateDocumentReason: '',
    taxType: null,
    buttonClick: '',
    isUserReadyForUpdate: false,
    isUserReady: false,
    purchaseAccount: null,
    openDrawerOfMultipleSelectedGRNs: false,
    selectedGRNData: null,
    purchaseVoucherType: 'Purchase',
    costCentre:'',
  };
  const apiId = match?.params?.apiId;

  const [state, dispatch] = useReducer(reducer, initialFormState);

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const {
    formSubmitted, apInvoiceType, selectedGRNData, selectedCurrencyName, selectedCurrencyID, isAutomaticConversionRate, currencyConversionRate, cfAPInvoiceDoc, apInvoiceNumber, initialAPInvoiceNumber, docSeqId, selectedTenant, isAdhocAPInvoiceAllowed, tenantDepartmentId, selectedTenantSeller, apInvoiceTableData, visibleLineCfs, selectedSeller, selectedSellerId, gstNumber, invoiceNumber, apInvoiceDate, invoiceDate, freightSacCode, freightTaxInfo, cfAPInvoiceLine, isUserReadyOne, selectedGRNValue, discountType, selectedGRN, chargeData, ewayBillNumber, ewayBillDate, ewayBillList, selectedTags, vendorAddress, isLineWiseDiscount, discountPercentage, charge1Name, charge1Value, taxTypeName, taxTypeInfo, freightTaxId, freightTax, openFreightTax, freightTaxData, selectedAddressType, showAddressDrawer, terms, fileList, taxTypeId, updateDocumentReason, taxType, buttonClick, isUserReadyForUpdate, isUserReady, purchaseAccount, openDrawerOfMultipleSelectedGRNs, purchaseVoucherType, costCentre,
    billOfEntryNumber, billOfEntryDate, portCode,
  } = state;

  const tallyIntegrationConfiguration = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant)?.tally_configuration;
  const isTallyConnected = tallyIntegrationConfiguration?.purchase_voucher_integration;

  useEffect(() => {
    if (apiId) {
      getAPInvoice({
        query: {
          tenant_id: Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.READ).join(','),
        },
        url: SERVICE_ENDPOINT.GET_ACCOUNT_PAYABLE_INVOICE({ apiId }).url,
      }, (apInvoiceResponseDate) => { });
    }
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'ACCOUNT_PAYABLE_INVOICE',
    };
    getDocCFV2(payload);

    if (isTallyConnected) {
      getTallyConfigurations({
        query: {
          org_id: user?.org_id,
          column_type: 'PURCHASE_VOUCHERS,COST_CENTRES',
        },
      }, () => {});
    }

    getCurrencies();
    getCharges(user?.tenant_info?.org_id, 'PURCHASE', (charges) => {
      const freight = charges?.find((i) => (i?.ledger_name === 'Freight Charge' && i?.is_system_charge));
      if (freight) {
        dispatch({
          type: actionType.SET_MULTIPLE_FIELDS,
          payload: {
            freightSacCode: freight?.charge_sac_code,
          },
        });
      }
    });
    getTaxes(user?.tenant_info?.org_id, 1, 1000, null, true);

    return () => {
      getAPInvoiceSuccess(null);
    };
  }, []);

  const handleGRNChange = ({ selectedGRNCopy, data }) => {
    let selectedGRNValueData;
    if (data) {
      selectedGRNValueData = data.isArray ? data : [data];
    } else {
      selectedGRNValueData = selectedGRNCopy?.grn_id ? [{
        key: selectedGRNCopy?.grn_id,
        value: selectedGRNCopy?.grn_id,
        label: `#${selectedGRNCopy.grn_number} (${toISTDate(selectedGRNCopy.created_date || selectedGRNCopy.created_at).format(
          'DD/MM/YYYY',
        )}) - `,
      }] : [];
    }
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const isVendorOverseas = (selectedGRNCopy?.tenant_seller_info?.seller_type === 'OVERSEAS' || selectedGRNCopy?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        invoiceNumber: selectedGRNCopy?.invoice_number,
        selectedTags: selectedGRNCopy?.tags || [],
        selectedGRN: selectedGRNCopy,
        gstNumber: selectedGRNCopy?.tenant_seller_info?.gst_number,
        tenantDepartmentId: selectedGRNCopy?.tenant_department_id,
        fileList: selectedGRNCopy?.attachments || [],
        vendorAddress: selectedGRNCopy?.seller_address_info,
        selectedGRNValue: selectedGRNValueData,
        taxType: isVendorOverseas ? null : (selectedGRNCopy?.tcs_id ?? selectedGRNCopy?.tds_id),
        taxTypeId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_id : selectedGRNCopy?.tds_info?.tax_id),
        taxTypeInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedGRNCopy?.tcs_info ?? selectedGRNCopy?.tds_info),
        tax_group_info: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedGRNCopy?.tcs_info ?? selectedGRNCopy?.tds_info),
        tax_value: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_value : (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_value : selectedGRNCopy?.tds_info?.tax_value),
        tax_info: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedGRNCopy?.tcs_info ?? selectedGRNCopy?.tds_info),
        tax_name: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_name : (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_name : selectedGRNCopy?.tds_info?.tax_name),
        taxTypeName: (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_type_name : selectedGRNCopy?.tds_info?.tax_type_name) || 'TDS',
        chargeData:
          selectedGRNCopy?.other_charges?.map((otherCharge) => ({
            ...otherCharge,
            chargeKey: uuidv4(),
            chargesTaxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : otherCharge?.tax_info?.tax_id,
            chargesSacCode: isVendorOverseas ? '' : otherCharge?.charge_sac_code,
            chargesTaxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info,
            chargesTax: isVendorOverseas ? 0 : otherCharge?.tax_info?.tax_value,
            tallyLedgerName: otherCharge?.ledger_name || null,
            chargesTaxData: {
              ...otherCharge?.tax_info,
              child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info, selectedGRNCopy?.tenant_billing_address?.state, selectedGRNCopy?.seller_address_info?.state)?.tax_info?.child_taxes,
            },
          })) || [],
        discountType: selectedGRNCopy?.is_discount_in_percent ? 'Percent' : 'Amount',
      },
    });

    const apInvoiceData = selectedGRNCopy?.grn_lines?.map((item) => {
      const parentKey = uuidv4();
      const discountValue = item?.is_discount_in_percent ? Number(item?.line_discount_percentage) : Number(item?.line_discount_amount);

      const initialLineCFs = cfV2DocAPInvoice?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [], false) : [];

      const taxableValue = discountType === 'Percent'
        ? (item?.ap_received_qty * item?.offer_price) * (1 - discountValue / 100)
        : Math.max(item.ap_received_qty * item?.offer_price - discountValue, 0);

      return ({
        ...item,
        key: parentKey,
        ap_received_qty: item?.pending_billing_quantity,
        product_sku_name: item?.product_sku_info?.product_sku_name,
        product_sku_id: item?.product_sku_info?.product_sku_id,
        expiryDate: item?.product_sku_info?.expiry_days > 0 ? (dayjs().add(item?.product_sku_info?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE,
        taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info,
        child_taxes: Helpers.computeTaxation(taxableValue, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info, selectedGRNCopy?.tenant_billing_address?.state, selectedGRNCopy?.seller_address_info?.state)?.tax_info?.child_taxes,
        taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
        uomId: item?.uom_id,
        uomInfo: item?.uom_info?.[0],
        remarks: autoPrintDescription ? (item?.remark || '')?.replace(/<[^>]+>/g, '') : '',
        remarkRequired: !!((autoPrintDescription && (item?.remark || '')?.replace(/<[^>]+>/g, ''))),
        manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
        expiryDateFormat: item?.product_sku_info?.expiry_date_format,
        expiryDays: item?.product_sku_info?.expiry_days,
        discount: discountValue,
        lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
        lineCustomFields: FormHelpers.lineSystemFieldValue(initialLineCFs, item?.po_line_custom_fields),
        tax_group_info: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedGRNCopy?.tcs_info ?? selectedGRNCopy?.tds_info),
        tax_value: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_value : (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_value : selectedGRNCopy?.tds_info?.tax_value),
        tax_info: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedGRNCopy?.tcs_info ?? selectedGRNCopy?.tds_info),
        tax_name: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_name : (selectedGRNCopy?.tcs_info ? selectedGRNCopy?.tcs_info?.tax_name : selectedGRNCopy?.tds_info?.tax_name),
        tally_purchase_account: item?.tally_purchase_account || null,
        offer_price: item?.purchase_po_price ?? 0,
        assessable_value: item?.pending_billing_quantity * (item?.purchase_po_price ?? 0),
        isManualOverride: false,
        taxableType: '',
        natureOfTransaction: '',
      });
    });

    const initialLineCFs = cfV2DocAPInvoice?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [];

    const oldLineCustomField = selectedGRNCopy?.grn_lines?.[0]?.grn_line_custom_fields?.filter((item) => (item?.is_active)) || [];

    const mergedLineCFs = CustomFieldHelpers.mergeCustomFields(initialLineCFs, oldLineCustomField) || [];

    const initialDocCFs = cfV2DocAPInvoice?.data?.document_custom_fields?.filter((item) => item?.is_active) || [];

    const oldCustomField = selectedGRNCopy?.custom_fields?.filter((item) => (item?.is_active)) || [];

    const mergedDocCFs = CustomFieldHelpers.mergeCustomFields(initialDocCFs, oldCustomField) || [];

    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        selectedCurrencyID: selectedGRNCopy?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedGRNCopy?.org_currency_info,
        apInvoiceTableData: apInvoiceData,
        isLineWiseDiscount: !selectedGRNCopy?.is_line_wise_discount,
        currencyConversionRate: selectedGRNCopy?.conversion_rate,
        isAutomaticConversionRate: false,
        charge1Name: selectedGRNCopy?.charge_1_name || 'Freight',
        charge1Value: selectedGRNCopy?.charge_1_value,
        freightTaxId: selectedGRNCopy?.freight_tax_id || 'Not Applicable',
        freightTax: selectedGRNCopy?.freight_tax_info?.tax_value,
        freightTaxInfo: selectedGRNCopy?.freight_tax_info,
        freightTaxData: {
          ...selectedGRNCopy?.freight_tax_info,
          child_taxes: Helpers.computeTaxation(selectedGRNCopy?.charge_1_value, selectedGRNCopy?.freight_tax_info, selectedGRNCopy?.tenant_billing_address?.state, selectedGRNCopy?.seller_address_info?.state)?.tax_info?.child_taxes,
        },
        freightSacCode: selectedGRNCopy?.freight_sac_code,
        discountPercentage: selectedGRNCopy?.is_discount_in_percent ? selectedGRNCopy?.discount_percentage : selectedGRNCopy?.discount_amount,
        discountType: selectedGRNCopy?.is_discount_in_percent ? 'Percent' : 'Amount',
        cfAPInvoiceLine: mergedLineCFs,
        cfAPInvoiceDoc: mergedDocCFs,
        visibleLineCfs: mergedLineCFs,
        selectedTenantSeller: selectedGRNCopy?.tenant_seller_info?.tenant_seller_id,
        purchaseAccount: null,
      },
    });
  };

  const handleMultiGRNChange = ({ selectedGRNCopy, data, mergedSelectedGRNs }) => {
    const selectedGrnIds = data?.map(data => data.value);
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const isVendorOverseas = selectedSeller?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
    // already selected grns
    const oldGrnLine = apInvoiceTableData?.filter(item =>
      selectedGrnIds?.includes(item?.grn_id) ||
      (!item?.grn_id && item?.is_adhoc_line)
    );    // newly selected grns
    const tempSelectedGRNList = data?.filter(data => (selectedGRNValue || [])?.every(item => item?.value !== data?.value))?.map(data => data.value);
    // filtering already selected grns
    const selectedGRNCopyData = selectedGRNCopy?.filter(grn => tempSelectedGRNList?.includes(grn?.grn_id));
    const currentMergedSelectedGRNs = mergedSelectedGRNs?.filter(grn => selectedGrnIds?.includes(grn?.grn_id));
    let combinedAttachments = currentMergedSelectedGRNs
      ?.map(item => Array.isArray(item?.attachments) ? item.attachments : [])
      .flat()
      .filter(attachment => attachment && typeof attachment === 'object' && attachment.uid) || [];
    const combinedTags = [...new Set(currentMergedSelectedGRNs?.map(item => item?.tags).flat() || [])];
    let updateCaseGstNumber = selectedAPInvoice?.gst_number;
    let updateCaseVendorAddress = selectedAPInvoice?.seller_address_info;
    let updateCaseCurrencyInfo = selectedAPInvoice?.currency_info;
    let updateCaseConversionRate = selectedAPInvoice?.conversion_rate;

    let gstNumber = updateCaseGstNumber || selectedSeller?.seller_info?.gst_number;
    let vendorAddress = updateCaseVendorAddress || selectedSeller?.seller_info?.office_address_details;
    let currencyInfo = updateCaseCurrencyInfo || selectedSeller?.currency_info;
    let conversionRate = updateCaseConversionRate || currencyInfo.conversion_rate;

    // newly selected grns lines creating
    const apInvoiceLineData = selectedGRNCopyData?.map((item) => {
      return item.grn_lines?.map((line) => {
        const parentKey = uuidv4();
        const discountValue = line?.is_discount_in_percent
          ? Number(line?.line_discount_percentage)
          : Number(line?.line_discount_amount);

        const initialLineCFs = cfV2DocAPInvoice?.data?.document_line_custom_fields?.length
          ? CustomFieldHelpers.getCfStructure(
            cfV2DocAPInvoice?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [],
            false
          )
          : [];

        const taxableValue = discountType === 'Percent'
          ? (line?.ap_received_qty || 0) * line?.offer_price * (1 - discountValue / 100)
          : Math.max((line?.ap_received_qty || 0) * line?.offer_price - discountValue, 0);

        const overseasTaxInfo = taxesGroup?.data?.find((tax) => tax?.tax_value === 0);
        const taxInfo = isVendorOverseas ? overseasTaxInfo : line?.tax_info;

        return {
          ...line,
          grn_id: item?.grn_id,
          grn_number: item?.grn_number,
          sourceNumber: item?.grn_number,
          source_id: item?.grn_id,
          key: parentKey,
          ap_received_qty: line?.pending_billing_quantity,
          product_sku_name: line?.product_sku_info?.product_sku_name,
          product_sku_id: line?.product_sku_info?.product_sku_id,
          expiryDate:
            line?.product_sku_info?.expiry_days > 0
              ? dayjs().add(line?.product_sku_info?.expiry_days || 0, 'day').format('YYYY-MM-DD')
              : INFINITE_EXPIRY_DATE,
          taxInfo,
          child_taxes: Helpers.computeTaxation(
            taxableValue,
            taxInfo,
            item?.tenant_billing_address?.state,
            item?.seller_address_info?.state
          )?.tax_info?.child_taxes,
          taxId: isVendorOverseas ? overseasTaxInfo?.tax_id : line?.tax_id,
          uomId: line?.uom_id,
          uomInfo: line?.uom_info?.[0],
          remarks: autoPrintDescription ? (line?.remark || '')?.replace(/<[^>]+>/g, '') : '',
          remarkRequired: !!(autoPrintDescription && (line?.remark || '')?.replace(/<[^>]+>/g, '')),
          manufacturingDateFormat: line?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: line?.product_sku_info?.expiry_date_format,
          expiryDays: line?.product_sku_info?.expiry_days,
          discount: discountValue,
          lineDiscountType: line?.is_discount_in_percent ? 'Percent' : 'Amount',
          lineCustomFields: FormHelpers.lineSystemFieldValue(initialLineCFs, line?.po_line_custom_fields),
          tax_group_info: isVendorOverseas
            ? overseasTaxInfo
            : item?.tcs_info ?? item?.tds_info,
          tax_value: isVendorOverseas
            ? overseasTaxInfo?.tax_value
            : item?.tcs_info?.tax_value ?? item?.tds_info?.tax_value,
          tax_info: isVendorOverseas
            ? overseasTaxInfo
            : item?.tcs_info ?? item?.tds_info,
          tax_name: isVendorOverseas
            ? overseasTaxInfo?.tax_name
            : item?.tcs_info?.tax_name ?? item?.tds_info?.tax_name,
          tally_purchase_account: line?.tally_purchase_account || null,
          offer_price: line?.purchase_po_price ?? 0,
          assessable_value: line?.pending_billing_quantity * (line?.purchase_po_price ?? 0),
          isManualOverride: false,
          taxableType: '',
          natureOfTransaction: '',
        };
      }) || [];
    }).flat();
    // Combine old GRN lines with newly selected GRN lines
    const combineBothLines = [...apInvoiceLineData, ...oldGrnLine];

    if (combineBothLines?.length === 0 && match?.params?.apiId) {
      notification.error({
        message: 'At least one line is required to create an Account Payable Invoice.',
        duration: 5,
        placement: 'top',
      });
      return;
    }
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        selectedGRNValue: data,
        vendorAddress,
        selectedCurrencyID: currencyInfo?.org_currency_id,
        selectedCurrencyName: currencyInfo,
        currencyConversionRate: conversionRate,
        isAutomaticConversionRate: false,
        fileList: combinedAttachments,
        gstNumber,
        purchaseAccount: null,
        selectedTags: combinedTags,
        selectedGRN: selectedGRNCopy,
        apInvoiceTableData: combineBothLines,
        chargeData: [],
      },
    });
  };

  useEffect(() => {
    // update case
    if (apiId && !selectedGRNForAPInvoice?.grn_id) {
      if (user && selectedAPInvoice && !apInvoiceTableData?.length && cfV2DocAPInvoice?.data?.success) {
        getSellers('', selectedTenant, 1, 30, selectedAPInvoice?.tenant_seller_id);
        const checkAPInvoiceType = () => {
          const grnCount = selectedAPInvoice?.linked_grns?.length || 0;

          if (grnCount === 1) {
            return 'GRN';
          } else if (grnCount > 1) {
            return 'MULTIGRN';
          } else {
            return 'ADHOC';
          }
        };
        if (checkAPInvoiceType() === 'MULTIGRN') {
          getGRNV2({
            query: {
              tenant_id: selectedAPInvoice?.tenant_info?.tenant_id,
              tenant_seller_id: selectedAPInvoice?.tenant_seller_info?.tenant_seller_id,
              status: 'ISSUED',
              page: 1,
              limit: 30,
              allow_grn_to_create_ap_invoice: true,
            },
          });
        }

        const tableData = selectedAPInvoice?.ap_invoice_lines?.map((item) => {
          const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
          const taxableValue = item?.is_discount_in_percent
            ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
            : Math.max((item.quantity * item.offer_price) - discountValue, 0);

          return {
            ...item,
            key: uuidv4(),
            ap_received_qty: item?.quantity || 0,
            quantity: item?.quantity || 0,
            sourceNumber: item?.grn_number,
            source_id: item?.grn_id,
            received_qty: item?.ordered_quantity || 0,
            total_ap_invoice_qty: item?.total_received_qty || 0,
            product_sku_id: item?.product_sku_info?.product_sku_id,
            expiryDate: item?.product_sku_info?.expiry_days > 0 ? (dayjs().add(item?.product_sku_info?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE,
            taxId: item?.tax_group_info?.tax_id,
            taxInfo: item?.tax_group_info,
            child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_group_info, selectedAPInvoice?.tenant_billing_address?.state, selectedAPInvoice?.seller_address_info?.state)?.tax_info?.child_taxes,
            uomId: item?.uom_info[0]?.uom_id,
            uomInfo: item?.uom_info[0],
            uom_info: item?.uom_info[0],
            uomGroup: item?.product_sku_info?.group_id || item?.product_sku_info?.purchase_uom_info?.group_id,
            uom_list: item?.product_sku_info?.uom_list,
            manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
            expiryDateFormat: item?.product_sku_info?.expiry_date_format,
            expiryDays: item?.product_sku_info?.expiry_days,
            remarks: item?.remark,
            remarkRequired: !!item?.remark?.replace(/<[^>]+>/g, ''),
            discount: discountValue,
            product_sku_name: item?.product_sku_info?.product_sku_name,
            lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
            productCategoryInfo: item?.product_sku_info?.product_category_info || {},
            lineCustomFields: CustomFieldHelpers.mergeCustomFields(cfV2DocAPInvoice?.data?.document_line_custom_fields.filter((item) => item?.is_active), item?.ap_invoice_line_custom_fields || []), // check
            isLandedCost: item?.is_landed_cost,
            tally_purchase_account: item?.tally_purchase_account || null,
            assessable_value: item?.assessable_value || 0,
            isManualOverride: true,
            taxableType: item?.taxable_type || '',
            natureOfTransaction: item?.nature_of_transaction || '',
          };
        });

        dispatch({
          type: actionType.SET_MULTIPLE_FIELDS,
          payload: {
            selectedGRNValue: selectedAPInvoice?.linked_grns?.map((item) => ({
              key: item?.grn_id,
              value: item?.grn_id,
              label: [`#${item?.grn_number}`, ''],
            })),
            selectedGRNData: grnV2?.grn?.find((item) => selectedAPInvoice?.linked_grns?.some((grn) => grn.grn_id === item.grn_id)) || [],
            apInvoiceNumber: selectedAPInvoice?.ap_invoice_number,
            apInvoiceTableData: tableData,
            tenantDepartmentId: selectedAPInvoice?.tenant_department_id,
            apInvoiceType: checkAPInvoiceType(),
            terms: selectedAPInvoice?.remark,
            selectedTenantSeller: selectedAPInvoice?.tenant_seller_info?.tenant_seller_id,
            gstNumber: selectedAPInvoice?.tenant_seller_info?.gst_number,
            selectedSellerId: selectedAPInvoice?.seller_id,
            apInvoiceDate: dayjs(selectedAPInvoice?.ap_invoice_date),
            ewayBillNumber: selectedAPInvoice?.e_way_bill_number,
            ewayBillDate: selectedAPInvoice?.e_way_bill_date ? dayjs(selectedAPInvoice?.e_way_bill_date) : null,
            ewayBillList: selectedAPInvoice.e_way_bill,
            invoiceDate: selectedAPInvoice?.vendor_invoice_date ? dayjs(selectedAPInvoice?.vendor_invoice_date) : null,
            invoiceNumber: selectedAPInvoice?.vendor_invoice_number,
            fileList: selectedAPInvoice?.attachments,
            purchaseAccount: selectedAPInvoice?.tally_purchase_account || null,
            billOfEntryDate : selectedAPInvoice?.bill_of_entry_date ? dayjs(selectedAPInvoice?.bill_of_entry_date) : null,
            billOfEntryNumber : selectedAPInvoice?.bill_of_entry_number || '',
            portCode : selectedAPInvoice?.port_code || '',
            chargeData:
              selectedAPInvoice?.other_charges?.map((otherCharge) => ({
                ...otherCharge,
                chargeKey: uuidv4(),
                chargesTaxId: otherCharge?.tax_info?.tax_id,
                chargesSacCode: otherCharge?.charge_sac_code,
                chargesTaxInfo: otherCharge?.tax_info,
                chargesTax: otherCharge?.tax_info?.tax_value,
                tallyLedgerName: otherCharge?.ledger_name || null,
                chargesTaxData: {
                  ...otherCharge?.tax_info,
                  child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, otherCharge?.tax_info, selectedAPInvoice?.tenant_billing_address?.state, selectedAPInvoice?.seller_address_info?.state)?.tax_info?.child_taxes,
                },
              })) || [],
            discountPercentage: selectedAPInvoice?.is_discount_in_percent ? selectedAPInvoice?.discount_percentage : selectedAPInvoice?.discount_amount,
            discountType: selectedAPInvoice?.is_discount_in_percent ? 'Percent' : 'Amount',
            isLineWiseDiscount: !selectedAPInvoice?.is_line_wise_discount,
            taxType: selectedAPInvoice?.tcs_id || selectedAPInvoice?.tds_id,
            taxTypeId: selectedAPInvoice?.tcs_info ? selectedAPInvoice?.tcs_info?.tax_id : selectedAPInvoice?.tds_info?.tax_id,
            taxTypeInfo: selectedAPInvoice?.tcs_info || selectedAPInvoice?.tds_info,
            selectedCurrencyName: selectedAPInvoice?.org_currency_info,
            selectedCurrencyID: selectedAPInvoice?.org_currency_info?.org_currency_id,
            taxTypeName: selectedAPInvoice?.tcs_info ? selectedAPInvoice?.tcs_info?.tax_type_name : selectedAPInvoice?.tds_info?.tax_type_name || 'TDS',
            charge1Name: selectedAPInvoice?.charge_1_name || 'Freight',
            charge1Value: selectedAPInvoice?.charge_1_value,
            freightTaxId: selectedAPInvoice?.freight_tax_id || 'Not Applicable',
            freightTax: selectedAPInvoice?.freight_tax_info?.tax_value,
            freightTaxInfo: selectedAPInvoice?.freight_tax_info,
            freightTaxData: {
              ...selectedAPInvoice?.freight_tax_info,
              child_taxes: Helpers.computeTaxation(selectedAPInvoice?.charge_1_value, selectedAPInvoice?.freight_tax_info, selectedAPInvoice?.tenant_billing_address?.state, selectedAPInvoice?.seller_address_info?.state)?.tax_info?.child_taxes,
            },
            freightSacCode: selectedAPInvoice?.freight_sac_code,
            vendorAddress: selectedAPInvoice?.seller_address_info,
            selectedTags: selectedAPInvoice?.tags,
            isAutomaticConversionRate: false,
            currencyConversionRate: selectedAPInvoice?.conversion_rate,
            selectedTenant: selectedAPInvoice?.tenant_info?.tenant_id,
            purchaseVoucherType : selectedAPInvoice?.purchase_voucher_type || '',
            costCentre : selectedAPInvoice?.cost_centre || '',
          },
        });
      }
      if (selectedAPInvoice?.ap_invoice_id && cfV2DocAPInvoice?.data?.success && !isUserReadyForUpdate) {
        // Already Used Custom  Fields
        const oldCustomField = selectedAPInvoice?.custom_fields || [];
        const oldLineCustomField = selectedAPInvoice?.ap_invoice_lines?.[0]?.ap_invoice_line_custom_fields || [];
        dispatch({
          type: actionType.SET_MULTIPLE_FIELDS,
          payload: {
            cfAPInvoiceDoc: CustomFieldHelpers.mergeCustomFields(cfV2DocAPInvoice?.data?.document_custom_fields, oldCustomField, true) || [],
            cfAPInvoiceLine: CustomFieldHelpers.mergeCustomFields(cfV2DocAPInvoice?.data?.document_line_custom_fields, oldLineCustomField) || [],
            visibleLineCfs: CustomFieldHelpers.mergeCustomFields(cfV2DocAPInvoice.data.document_line_custom_fields, oldLineCustomField).filter((item) => item.isActive) || [],
            isUserReadyForUpdate: true,
          },
        });
      }
    }

    if (selectedGRNForAPInvoice?.grn_id && !apInvoiceTableData?.length && cfV2DocAPInvoice?.data?.success && !isUserReady
      && taxesGroup?.success) {
      handleGRNChange({ selectedGRNCopy: selectedGRNForAPInvoice });

      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: { isUserReady: true },
      });
    }

    if (cfV2DocAPInvoice && !isUserReadyOne && !selectedGRNForAPInvoice?.grn_id && !selectedAPInvoice?.ap_invoice_id) {
      const lineCFs = cfV2DocAPInvoice?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [], false) : [];

      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: {
          cfAPInvoiceDoc: CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice?.data?.document_custom_fields, true) || [],
          cfAPInvoiceLine: lineCFs,
          visibleLineCfs: lineCFs,
          isUserReadyOne: true,
          selectedTenant: user?.tenant_info?.tenant_id,
        },
      });
    }

    if (currenciesResults && currenciesResults?.length > 0 && (apInvoiceType === 'ADHOC' || selectedAPInvoice || selectedGRN || selectedGRNForAPInvoice)) {
      const defaultCurrency = currenciesResults?.find((currency) => currency.is_default === true);

      //! these are commented as we are already setting the currency in the respective places like in handleGRNChange (for selectedGRNForAPInvoice and GRN selection from API form) and in useEffect (for selectedAPInvoice [update case])

      // if (defaultCurrency && !selectedCurrencyID && selectedGRNForAPInvoice) {
      //   dispatch({
      //     type: actionType.SET_MULTIPLE_FIELDS,
      //     payload: {
      //       selectedCurrencyID: selectedGRNForAPInvoice?.org_currency_info?.org_currency_id,
      //       selectedCurrencyName: selectedGRNForAPInvoice?.org_currency_info,
      //       isAutomaticConversionRate: false,
      //       currencyConversionRate: selectedGRNForAPInvoice?.conversion_rate,
      //     },
      //   });
      // }

      // if (defaultCurrency && !selectedCurrencyID && selectedAPInvoice) {
      //   dispatch({
      //     type: actionType.SET_MULTIPLE_FIELDS,
      //     payload: {
      //       selectedCurrencyID: selectedAPInvoice?.org_currency_info?.org_currency_id,
      //       selectedCurrencyName: selectedAPInvoice?.org_currency_info,
      //       isAutomaticConversionRate: false,
      //       currencyConversionRate: selectedAPInvoice?.conversion_rate,
      //     },
      //   });
      // }

      // if (defaultCurrency && !selectedCurrencyID && selectedGRN) {
      //   dispatch({
      //     type: actionType.SET_MULTIPLE_FIELDS,
      //     payload: {
      //       selectedCurrencyID: selectedGRN?.org_currency_info?.org_currency_id,
      //       selectedCurrencyName: selectedGRN?.org_currency_info,
      //       isAutomaticConversionRate: false,
      //       currencyConversionRate: selectedGRN?.conversion_rate,
      //     },
      //   });
      // }

      if (defaultCurrency && !selectedCurrencyID && !selectedAPInvoice && !selectedGRNForAPInvoice) {
        dispatch({
          type: actionType.SET_MULTIPLE_FIELDS,
          payload: {
            selectedCurrencyID: defaultCurrency?.org_currency_id,
            selectedCurrencyName: defaultCurrency,
            currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? defaultCurrency?.automatic_conversion_rate : defaultCurrency?.conversion_rate,
            isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
          },
        });
      }
    }
  }, [cfV2DocAPInvoice, currenciesResults, apInvoiceType, selectedAPInvoice, selectedGRNForAPInvoice, taxesGroup]);

  const getApIErrors = () => {
    const { docLevelError, lineLevelError } = apiErrorList({
      selectedSeller, invoiceNumber, cfAPInvoiceDoc, apInvoiceType, apInvoiceDate, selectedGRNValue, cfAPInvoiceLine, apInvoiceTableData, match, ewayBillNumber, ewayBillList, vendorAddress, selectedGRNForAPInvoice, user, invoiceDate, selectedTenant, apInvoiceNumber, purchaseAccount, selectedGRN,
    });
    return { docLevelError: docLevelError ?? [], lineLevelError: lineLevelError ?? [] };
  };

  const { docLevelError: copyDocLevelError, lineLevelError: copyLineLevelError } = getApIErrors();

  const handleAPInvoiceTypeChange = (value) => {
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        apInvoiceType: value,
        selectedTenant: user?.tenant_info?.tenant_id,
        selectedTenantSeller: null,
        gstNumber: '',
        tenantDepartmentId: value === 'ADHOC' ? user?.tenant_info?.default_store_id : null,
        selectedSeller: null,
        vendorAddress: null,
        shippingAddress: null,
        formSubmitted: null,
        invoiceNumber: '',
        selectedCurrencyID: null,
        selectedCurrencyName: null,
        isAutomaticConversionRate: null,
        currencyConversionRate: null,
        visibleLineCfs: cfAPInvoiceLine,
        selectedTenantTallyIntegrationId: user?.tenant_info?.it_id,
        discountPercentage: null,
        cfAPInvoiceDoc: CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice?.data?.document_custom_fields, true) || [],
        selectedGRNValue: [],
        selectedGRN: null,
        selectedGRNJson: null,
        apInvoiceTableData: [],
      },
    });
  };

  const handleDocSeqChange = (event, newValue, seqId) => {
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        apInvoiceNumber: newValue || (event?.target?.value || ''),
        docSeqId: seqId,
      },
    });
  };

  const handleSellerSelection = (value) => {
    if (apInvoiceType === 'ADHOC') {
      const docCf = CustomFieldHelpers.postCfStructure(cfAPInvoiceDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
      const vendorCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
      const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, vendorCf) || [];
      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: {
          cfAPInvoiceDoc: mergedCf,
        },
      });
    }

    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: {
        selectedSeller: value,
        selectedTenantSeller: value?.tenant_seller_id,
        gstNumber: value?.seller_info?.gst_number,
        selectedGRNValue: null,
        vendorAddress: value?.seller_info?.office_address_details,
        selectedGRN: null,
        tenantDepartmentId: apInvoiceType !== 'ADHOC' ? user?.tenant_info?.default_store_id : tenantDepartmentId,
        apInvoiceTableData: [],
        isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
        selectedCurrencyID: value?.org_currency_id,
        selectedCurrencyName: value?.currency_info,
        currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? value?.currency_info?.automatic_conversion_rate : value?.currency_info?.conversion_rate,
      },
    });
    if (selectedTenant && apInvoiceType !== 'ADHOC') {
      getGRNV2({
        query: {
          tenant_id: selectedTenant,
          tenant_seller_id: value?.tenant_seller_id,
          status: 'ISSUED',
          page: 1,
          limit: 30,
          allow_grn_to_create_ap_invoice: true,
        },
      });
    }
  };

  function getLineTotals(tableData = apInvoiceTableData, charge = chargeData, charge1 = charge1Value) {
    let totalAmount = 0;
    let totalDiscount = 0;
    let totalBase = 0;
    let totalTcs = 0;
    let totalTds = 0;
    let totalTax = 0;
    let totalOtherCharge = 0;
    let apInvoiceTotal = 0;
    let totalTaxValue = 0;
    let freightCharge = Number(charge1);
    let totalOtherChargesForTaxableAmount = 0;
    let freightTaxAmount = 0;

    // Taxes Bifurcation
    if (tableData?.length > 0) {
      totalTaxValue = Helpers.groupAndSumByTaxName(tableData?.map((item) => item?.child_taxes)?.flat())?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
    }

    for (let i = 0; i < charge?.length; i++) {
      let currentOtherCharge = 0;
      let chargesTaxAmountLinesWise = 0;
      if (charge[i]?.charge_amount) {
        chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName([...charge[i]?.chargesTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
        currentOtherCharge = charge?.[i]?.chargesTax ? charge?.[i]?.charge_amount + (chargesTaxAmountLinesWise) : charge?.[i]?.charge_amount;
        if (charge?.[i]?.chargesTax) {
          totalOtherChargesForTaxableAmount += charge?.[i]?.charge_amount;
        }
      }
      totalOtherCharge += currentOtherCharge;
    }
    let lines = [];
    for (let i = 0; i < tableData?.length; i++) {
      let currentAmount = 0;
      let currentDiscount = 0;
      let currentBase = 0;
      let currentTax = 0;
      let currentGRN = 0;

      const quantityToUse = apInvoiceType === 'ADHOC' ? tableData[i]?.quantity : tableData[i]?.ap_received_qty;

      const discountValue = Number(tableData[i].discount);

      if (quantityToUse) {
        currentAmount += (quantityToUse * tableData[i].offer_price);

        if (discountValue) {
          if (tableData[i]?.lineDiscountType === 'Percent') {
            currentDiscount += (quantityToUse * tableData[i].offer_price) * (discountValue / 100);

            currentBase = (quantityToUse * tableData[i].offer_price) * Number(100 - discountValue) / 100;
          } else if (tableData[i]?.lineDiscountType === 'Amount') {
            currentDiscount += discountValue;
            currentBase += (quantityToUse * tableData[i].offer_price) - discountValue;
          }
        } else {
          currentDiscount += 0;
          currentBase += (quantityToUse * tableData[i].offer_price);
        }
      }
      // Subtract TDS from currentBase or Add TCS
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        const tcsAmount = currentBase * tcsRate;
        totalTcs += tcsAmount;
      } else if (taxTypeInfo && taxTypeName === 'TDS') {
        const tdsRate = taxTypeInfo?.tax_value / 100;
        const tdsAmount = currentBase * tdsRate;
        totalTds -= tdsAmount;
      }

      if (((tableData[i]?.taxInfo?.tax_value) || (tableData[i]?.taxInfo?.[0]?.tax_value))) currentTax = currentBase * (((tableData[i]?.taxInfo?.tax_value) || (tableData[i]?.taxInfo?.[0]?.tax_value)) / 100);
      if (currentBase) currentGRN = currentBase;

      totalAmount += currentAmount;
      totalDiscount += currentDiscount;
      totalBase += currentBase;
      totalTax += currentTax;
      apInvoiceTotal += currentGRN;
      lines.push({
        api_line_total: currentGRN + currentTax + (taxTypeName === 'TCS' ? totalTcs : totalTds),
        api_line_quantity: quantityToUse,
        api_line_unit_price: tableData[i].offer_price,
      });
    }
    apInvoiceTotal += totalTaxValue;
    lines = lines.map((line) => {
      let unitOtherCharge = 0;
      let unitFreightCharge = 0;
      const unitCostPrice = Number(line.api_line_unit_price);
      unitOtherCharge = Number(((line.api_line_total / apInvoiceTotal) * totalOtherCharge) / line.api_line_quantity);
      unitFreightCharge = Number(((line.api_line_total / apInvoiceTotal) * Number(charge1)) / line.api_line_quantity);
      line.unit_landed_cost = Number(unitCostPrice + unitOtherCharge + unitFreightCharge).toFixed(2) || 0;
      line.unit_other_cost = Number(unitOtherCharge).toFixed(2) || 0;
      line.unit_freight_cost = Number(unitFreightCharge).toFixed(2) || 0;
      return line;
    });

    if (freightTax) {
      freightTaxAmount = Helpers.groupAndSumByTaxName([...freightTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      totalBase += freightCharge;
      freightCharge += freightTaxAmount;
      // Subtract TDS from currentBase or Add TCS
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        totalTcs = totalBase * tcsRate;
      } else if (taxTypeInfo && taxTypeName === 'TDS') {
        const tdsRate = taxTypeInfo?.tax_value / 100;
        totalTds = -((totalBase + totalOtherChargesForTaxableAmount) * tdsRate);
      }
    }

    // Add TCS from to total amount including GST ,Freight Other charges
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;

      // If we have freight tax then add only freight tax amount as freight charge is already included in totalBase or if freight tax  is not present add freightCharge
      const tcsAmount = (totalBase + totalTaxValue + Number(totalOtherCharge) + (freightTax ? freightTaxAmount : freightCharge)) * tcsRate;
      totalTcs = tcsAmount;
    }

    apInvoiceTotal += (taxTypeName === 'TCS' ? totalTcs : totalTds);
    apInvoiceTotal += totalOtherCharge;
    apInvoiceTotal += freightCharge;
    totalBase += totalOtherChargesForTaxableAmount;
    return {
      totalAmount,
      totalDiscount,
      totalBase,
      totalTax,
      totalTcs,
      totalTds,
      apInvoiceTotal,
      lines,
    };
  }

  function customInputChange(fieldValue, cfId) {
    const newCustomField = cfAPInvoiceDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { cfAPInvoiceDoc: newCustomField } });
  }

  const getAPInvoiceTableData = (data) => {
    const copyData = JSON.parse(JSON.stringify(data));
    return [...copyData?.filter((item) => item?.product_sku_id), ...copyData?.filter((item) => !item?.product_sku_id)];
  };

  const getBillFromAddress = () => {
    let billFromAddress;
    if (selectedGRN) {
      billFromAddress = selectedGRN?.tenant_billing_address?.state;
    } else if (selectedAPInvoice) {
      billFromAddress = selectedAPInvoice?.tenant_billing_address?.state;
    } else {
      billFromAddress = user?.tenant_info?.state;
    }
    return billFromAddress;
  };

  const getBillToAddress = () => {
    let billToAddress;
    if (vendorAddress) {
      billToAddress = vendorAddress?.state;
    } else if (selectedGRN) {
      billToAddress = selectedGRN?.seller_address_info?.state;
    }
    return billToAddress;
  };

  const handleDiscountPercentageChange = ({ value, tableData = apInvoiceTableData }) => {
    const copyData = JSON.parse(JSON.stringify(tableData));

    if (apInvoiceType !== 'ADHOC') {
      copyData.map((item) => {
        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.received_qty * cur.offer_price), 0);

        const discountValue = discountType === 'Percent' ? Number.parseFloat(value) : ((item.received_qty * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

        const taxableValue = discountType === 'Percent'
          ? (item?.received_qty * item?.offer_price) * (1 - discountValue / 100)
          : Math.max(item.received_qty * item?.offer_price - discountValue, 0);

        item.discount = discountValue;
        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
        return item;
      });
    } else {
      copyData.map((item) => {
        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

        const discountValue = discountType === 'Percent' ? Number.parseFloat(value) : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

        const taxableValue = discountType === 'Percent'
          ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
          : Math.max(item.quantity * item?.offer_price - discountValue, 0);

        item.discount = discountValue;
        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
        return item;
      });
    }
    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { apInvoiceTableData: copyData, discountPercentage: Number.parseFloat(value) } });
  };

  const handleDelete = (key, id) => {
    if (apInvoiceTableData.length > 1) {
      const copyData = apInvoiceTableData.filter((item) => item.key !== key || item.grn_line_id !== id);
      const updatedSelectedPoValue = selectedGRNValue?.filter((item) => copyData?.some((data) => data?.grn_id === item?.value));
      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: { apInvoiceTableData: copyData, selectedGRNValue: updatedSelectedPoValue },
      });

      if (isLineWiseDiscount) handleDiscountPercentageChange({ value: discountPercentage, tableData: copyData });
    } else {
      notification.error({
        message: 'You need to have at least one item in the AP Invoice.',
        duration: 4,
        placement: 'top',
      });
    }
  };

  const handleProductChangeValue = (value, key) => {
    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: { apInvoiceTableData: copyData },
    });
  };

  const createData = ({
    tenantSku, dataItem, productData, selectedSeller, isLineWiseDiscount, discountPercentage, autoPrintDescription, discountType, cfV2DocAPInvoice, isLandedCost,
  }) => {
    const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice.data.document_line_custom_fields?.filter((item) => item?.is_active), false);

    const isVendorOverseas = selectedSeller?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
    const copyDataItem = JSON.parse(JSON.stringify(dataItem));
    copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
    copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
    copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
    copyDataItem.product_type = tenantSku?.product_type;
    copyDataItem.product_sku_info = tenantSku?.product_info;
    copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
    copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
    copyDataItem.quantity = 1;
    copyDataItem.unitPrice = tenantSku?.selling_price || 0;
    copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
    copyDataItem.taxInfo = isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info;
    copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.cost_price), isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
    copyDataItem.taxId = isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id;
    copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id || 0;
    copyDataItem.uom_info = tenantSku?.purchase_uom_info;
    copyDataItem.uomInfo = tenantSku?.purchase_uom_info;
    copyDataItem.uomGroup = tenantSku?.group_id || tenantSku?.purchase_uom_info?.group_id;
    copyDataItem.productCategoryInfo = tenantSku?.product_category_info || {};
    copyDataItem.uom_list = tenantSku?.uom_list;
    copyDataItem.discount = isLineWiseDiscount ? discountPercentage : 0;
    copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
    copyDataItem.ap_received_qty = 1;
    copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
    copyDataItem.remarkRequired = !!((autoPrintDescription && (productData?.description || '')?.replace(/<[^>]+>/g, '')));
    copyDataItem.lineDiscountType = isLineWiseDiscount ? discountType : 'Percent';
    copyDataItem.offer_price = tenantSku?.cost_price || 0;
    copyDataItem.isLandedCost = isLandedCost;
    copyDataItem.tally_purchase_account = null;
    copyDataItem.assessable_value = 1 * (tenantSku?.cost_price || 0);
    copyDataItem.isManualOverride = false;
    copyDataItem.taxableType = '';
    copyDataItem.natureOfTransaction = '';
    copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(lineCFs, [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])])?.map((k) => ({
      ...k,
      fieldValue: k?.fieldName === 'Rate' ? copyDataItem.unitPrice : (k?.fieldName === 'Quantity' ? copyDataItem.quantity : k?.fieldValue),
    }));
    return copyDataItem;
  };

  const handleProductChange = (tenantSku, key, productData, isMultiMode, callback, isLandedCost) => {
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const automaticArNumber = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];
      for (let i = 0; i < tenantSku.length; i++) {
        const newData = {
          key: uuidv4(),
          lineCustomFields: visibleLineCfs,
        };
        const productSKUData = productData?.find((item) => item?.product_sku_id === tenantSku[i]?.product_sku_id);

        const newDataRow = createData({
          tenantSku: tenantSku[i],
          dataItem: newData,
          productData: productSKUData,
          selectedSeller,
          isLineWiseDiscount,
          discountPercentage,
          autoPrintDescription,
          discountType,
          cfV2DocAPInvoice,
          isLandedCost,
        });
        copyData.push(newDataRow);
      }

      if (callback) callback();

      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: {
          apInvoiceTableData: [...state.apInvoiceTableData, ...copyData],
        },
      });
    } else {
      const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
      for (let i = 0; i < copyData?.length; i++) {
        if (copyData[i].key === key) {
          copyData[i] = createData({
            tenantSku,
            dataItem: copyData[i],
            productData,
            selectedSeller,
            isLineWiseDiscount,
            discountPercentage,
            autoPrintDescription,
            discountType,
            cfV2DocAPInvoice,
            isLandedCost,
          });
        }
      }
      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: {
          apInvoiceTableData: copyData,
        },
      });
      if (isLineWiseDiscount) handleDiscountPercentageChange({ value: discountPercentage, tableData: copyData });
    }
  };

  const handleMultiProductChange = (tenantSku, key, productData, callback, isLandedCost) => {
    handleProductChange(tenantSku, key, productData, true, callback, isLandedCost);
  };

  const addNewRow = (isLandedCost) => {
    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
    copyData.push({
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: '',
      selling_price: '',
      taxId: '',
      lot: '',
      product_sku_id: null,
      product_sku_info: null,
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      lineCustomFields: cfAPInvoiceLine,
      is_adhoc_line: true,
      isLandedCost: isLandedCost,
      // lineCustomFields: visibleLineCfs,
      tally_purchase_account: null,
    });
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: { apInvoiceTableData: copyData },
    });
  };

  const customFieldVisibilityChange = (visible, cfId) => {
    let visibleLineCfsCopy = JSON.parse(JSON.stringify(visibleLineCfs));
    visibleLineCfsCopy = visibleLineCfsCopy?.map((customField) => {
      if (customField?.cfId === cfId) {
        return { ...customField, visible };
      }
      return { ...customField };
    });
    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: { visibleLineCfs: visibleLineCfsCopy },
    });
  };

  const isReceivedQty = apInvoiceTableData?.every((item) => {
    const itemQuantityToUse = apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty;
    return itemQuantityToUse > 0;
  });

  const handleDeleteCharge = (chargeKey) => {
    const copyChargeData = chargeData.filter((item) => item.chargeKey !== chargeKey);
    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copyChargeData } });
  };

  const splitChargesData = (charge) => {
    const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
    const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
    return { chargeWithTaxName, chargeWithoutTaxName };
  };
  const renderCharges = (charge) => charge?.map((line) => {
    const isVendorOverseas = (selectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedGRNForAPInvoice?.tenant_seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
    return (
      <div
        key={line?.chargeKey}
        className="form-calculator__field"
      >
        <div className="form-calculator__field-name">
          {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.use_custom_charges ? (
            <div className="select_extra_charge_wrapper">
              <SelectExtraCharge
                containerClassName="orgInputContainer"
                selectedChargeName={line.charge_name}
                disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                onChange={(value) => {
                  const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                  copyChargeData.map((item) => {
                    if (item.chargeKey === line.chargeKey) {
                      item.charge_name = value?.ledger_name;
                      item.chargesSacCode = (value?.charge_sac_code) || null;
                    }
                  });
                  dispatch({
                    type: actionType.SET_MULTIPLE_FIELDS,
                    payload: { chargeData: copyChargeData },
                  });
                }}
                customStyle={{
                  width: '220px',
                  backgroundColor: 'white',
                }}
                excludeCharges={chargeData?.map((item) => item?.charge_name)}
                entityName="PURCHASE"
              />
              {line?.chargesTax && !isVendorOverseas && (
                <div style={{
                  color: '#2d7df7',
                  fontWeight: '400',
                  fontSize: '12px',
                }}
                >
                  {!isVendorOverseas && `tax@${line?.chargesTax}%`}
                </div>
              )}
            </div>
          ) : (
            <H3FormInput
              value={line?.charge_name}
              type="text"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_name) <= 0
                ? 'form-error__input'
                : ''
                }`}
              placeholder="Charge name.."
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    item.charge_name = e.target.value;
                  }
                });
                dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copyChargeData } });
              }}
              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
            />
          )}

        </div>
        <div className="form-calculator__field-value" style={{ display: 'flex', gap: '0px' }}>
          <div style={{ width: '140px', marginRight: '-27px' }}>
            <H3FormInput
              value={line?.charge_amount}
              type="number"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_amount) <= 0
                ? 'form-error__input'
                : ''
                }`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    const updatedChargeAmount = Number.parseFloat(e.target.value) || 0;

                    // Update charge_amount
                    item.charge_amount = updatedChargeAmount;
                    // Compute chargesTaxData with updated values
                    const computedTaxData = Helpers.computeTaxation(
                      updatedChargeAmount,
                      line.chargesTaxInfo,
                      getBillFromAddress(),
                      getBillToAddress(),
                    );
                    // Update chargesTaxData
                    item.chargesTaxData = {
                      ...line?.chargesTaxInfo,
                      child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                    };
                  }
                  return apInvoiceTableData;
                });
                dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copyChargeData } });
              }}
              allowNegative
              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
            />
          </div>
          <ChargesTaxInput
            selectedTenant={selectedTenant}
            selectedChargeName={line.charge_name}
            openChargesTax={line?.openChargesTax}
            setOpenChargesTax={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  item.openChargesTax = value;
                }
                return apInvoiceTableData;
              });
              dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copyChargeData } });
            }}
            sacCode={line?.chargesSacCode}
            setSacCode={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  item.chargesSacCode = value;
                }
                return apInvoiceTableData;
              });
              dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copyChargeData } });
            }}
            chargesTaxId={line?.chargesTaxId}
            setChargesTaxData={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  return {
                    ...item,
                    chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                    chargesTax: !value ? null : value?.tax_value,
                    chargesTaxInfo: value || null,
                    chargesTaxData: !value ? {
                      child_taxes: [
                        {
                          tax_amount: 0,
                          tax_type_name: '',
                        },
                      ],
                    } : {
                      ...value,
                      child_taxes: Helpers.computeTaxation(line?.charge_amount, value, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes,
                    },
                  };
                }
                return item;
              });
              dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: updatedChargeData } });
            }}
            isVendorOverseas={isVendorOverseas}
            tallyLedgerName={line?.tallyLedgerName}
            setTallyLedgerName={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item?.chargeKey === line?.chargeKey) {
                  return {
                    ...item,
                    tallyLedgerName: value,
                  };
                }
                return item;
              });
              dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: updatedChargeData } });
            }}
            showTallyLedgerPurchase
          />
          <div
            className="form-calculator__delete-line-button"
            onClick={() => handleDeleteCharge(line?.chargeKey)}
          >
            <FontAwesomeIcon
              icon={faCircleXmark}
              size="sm"
              style={{ color: '#6f7276' }}
            />
          </div>
        </div>
      </div>
    );
  });

  const restrictMessage = () => {
    const messages = []; // Collect restriction messages her

    if (isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)) {
      messages.push(
        `You don't have access to view/edit${isHideCostPrice ? ' cost price' : ''}${isHideCostPrice && isHideSellingPrice ? ' and' : ''}${isHideSellingPrice ? ' selling price' : ''}.`,
      );
    } else if (apInvoiceType === 'ADHOC' && isAdhocAPInvoiceAllowed) {
      messages.push(
        'You don\'t have access to create ADHOC AP Invoice in the selected business unit.',
      );
    }

    return messages; // Return collected messages or an empty array
  };

  const isCreateValidDataADHOC = (apInvoiceData) => {
    let isValid = true;
    if ((!selectedSeller?.seller_id && !selectedSellerId) || !vendorAddress) {
      isValid = false;
    }
    if (!apInvoiceDate) return false;

    if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN') {
      isValid = false;
    }
    for (let i = 0; i < apInvoiceData?.length; i++) {
      const item = apInvoiceData[i];
      if ((Number(item.quantity) <= 0 || Number(item.offer_price) <= 0) || !apInvoiceDate || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant))?.it_id && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false)
        || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
    }
    const { docLevelError, lineLevelError } = getApIErrors();
    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }

    return isValid;
  };

  const isDataValid2 = () => {
    let isDataValid = true;
    if (chargeData?.length) {
      for (let i = 0; i < chargeData?.length; i++) {
        if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
          isDataValid = false;
        }
      }
    }
    return isDataValid;
  };

  const isCreateValidData = (apInvoiceData) => {
    let isValid = true;

    if (!apInvoiceData) return false;

    if (apInvoiceType === 'ADHOC') {
      return !isValid;
    }
    if (user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !purchaseAccount) {
      isValid = false;
    }
    for (let i = 0; i < apInvoiceData?.length; i++) {
      const item = apInvoiceData[i];

      if (((item.ap_received_qty) <= 0 || Number(item.offer_price) <= 0) || !apInvoiceData || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant || selectedGRNForAPInvoice?.tenant_id || selectedGRN?.tenant_id || user?.tenant_info?.tenant_id))?.it_id && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false) || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
    }
    const { docLevelError, lineLevelError } = getApIErrors();
    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }
    return isValid;
  };

  const isUpdateValidData = (apInvoiceData) => {
    let isValid = true;

    if (user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !purchaseAccount) {
      isValid = false;
    }

    for (let i = 0; i < apInvoiceData?.length; i++) {
      const item = apInvoiceData[i];

      if ((Number(item.ap_received_qty) <= 0 || Number(item.offer_price) <= 0) || !apInvoiceDate || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant || selectedGRNForAPInvoice?.tenant_id || selectedGRN?.tenant_id || user?.tenant_info?.tenant_id))?.it_id && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false) || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
    }
    return isValid;
  };

  const handleCreateAPInvoice = (approve) => {
    if (approve) {
      dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { buttonClick: 'APPROVE', formSubmitted: true } });
    } else {
      dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { buttonClick: 'DRAFT', formSubmitted: true } });
    }

    if (cfAPInvoiceDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
      notification.open({
        message: 'Please fill all the mandatory inputs',
        duration: 4,
        placement: 'top',
        type: 'error',
      });
      return;
    }

    const { docLevelError, lineLevelError } = getApIErrors();
    if (!!docLevelError?.length || lineLevelError?.length) {
      return;
    }

    if (apiId) {
      // this is update case
      const createPayloadAndUpdateAPInvoice = () => {
        const apInvoiceLines = apInvoiceTableData?.map(
          (item) => ({
            ap_invoice_line_id: item?.ap_invoice_line_id || null,
            tenant_product_id: item.tenant_product_id || '',
            product_sku_id: item.product_sku_id || '',
            quantity: apInvoiceType === 'ADHOC' ? Number(item.quantity) : Number(item.ap_received_qty),
            offer_price: Number(item.offer_price) || 0,
            remarks: item.remarks || '',
            tax_id: item?.taxId || '',
            grn_line_id: item?.grn_line_id || null,
            tax_group_info: item?.taxInfo,
            uom_id: item?.uomId || item?.uom_info?.[0]?.uom_id || item?.product_sku_info?.uom_info?.uom_id || '',
            uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
            expiry_date: item.expiryDate || null,
            line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty) * Number.parseFloat(item.offer_price))) * 100),
            is_discount_in_percent: item?.lineDiscountType === 'Percent',
            custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
            seller_address_info: vendorAddress,
            is_landed_cost: item?.isLandedCost,
            tally_purchase_account: item.tally_purchase_account,
            assessable_value: isTallyConnected ? (item?.assessable_value || 0) : null,
            taxable_type: item?.taxableType || '',
            nature_of_transaction: item?.natureOfTransaction || '',
          }),
        );
        if (apInvoiceLines?.length) {
          const payload = {
            account_payable_invoice: {
              ap_invoice_lines: apInvoiceLines.filter(Boolean),
              tenant_seller_info: {
                ...selectedAPInvoice?.tenant_seller_info,
                gst_number: gstNumber,
              },
              tally_purchase_account: purchaseAccount,
              ap_invoice_id: selectedAPInvoice?.ap_invoice_id,
              tenant_department_id: selectedAPInvoice?.tenant_department_id,
              ap_invoice_date: apInvoiceDate,
              vendor_invoice_date: invoiceDate,
              vendor_invoice_number: invoiceNumber || null,
              seller_id: selectedAPInvoice?.seller_id || null,
              seller_address_info: vendorAddress,
              payment_terms: selectedAPInvoice?.payment_terms || [],
              status: (apInvoiceLines.filter(Boolean).length > 0) && (approve ? 'ISSUED' : 'DRAFT'),
              tenant_id: selectedTenant,
              remark: terms,
              attachments: fileList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              custom_fields: CustomFieldHelpers.postCfStructure(cfAPInvoiceDoc),
              other_charges: chargeData?.map((charge) => ({
                charge_name: charge?.charge_name,
                charge_amount: charge?.charge_amount,
                charge_type: '',
                charge_sac_code: charge?.chargesSacCode || null,
                tax_info: charge?.chargesTaxInfo || null,
                ledger_name: charge?.tallyLedgerName || null,
              })) || [],
              discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (getLineTotals().totalAmount || 0)) * 100,
              is_discount_in_percent: discountType === 'Percent',
              is_line_wise_discount: !isLineWiseDiscount,
              tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
              tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
              tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
              org_currency_id: selectedCurrencyName?.org_currency_id,
              charge_1_name: charge1Name,
              charge_1_value: Number(charge1Value),
              freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
              freight_tax_info: freightTaxInfo,
              freight_sac_code: freightSacCode,
              e_way_bill: ewayBillList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              e_way_bill_number: ewayBillNumber,
              e_way_bill_date: ewayBillDate,
              bill_of_entry_date: billOfEntryDate,
              bill_of_entry_number: billOfEntryNumber,
              port_code: portCode,
              ap_invoice_number: initialAPInvoiceNumber?.toLowerCase()?.trim() === apInvoiceNumber?.toLowerCase()?.trim() ? null : apInvoiceNumber,
              purchase_voucher_type: purchaseVoucherType,
              cost_centre: costCentre
            },
          };
          updateAPInvoice(
            {
              body: payload,
            },
            (apInvoice) => {
              if (selectedTags?.length) {
                const tagPayload = {
                  entity_name: 'ACCOUNT_PAYABLE_INVOICE',
                  entity_id: apInvoice?.account_payable_invoice?.ap_invoice_id,
                  action: 'ADD',
                  tag: selectedTags.join(','),
                };
                createTag(tagPayload, () => {
                  history.push(`/purchase/account-payable-invoice/view/${apInvoice?.account_payable_invoice?.ap_invoice_id}`);
                });
              } else {
                history.push(`/purchase/account-payable-invoice/view/${apInvoice?.account_payable_invoice?.ap_invoice_id}`);
              }
            },
          );
        }
      };
      const condition = apInvoiceType !== 'ADHOC'
        ? isUpdateValidData(apInvoiceTableData) : isCreateValidDataADHOC(apInvoiceTableData);

      if (condition && isDataValid2() && !cfAPInvoiceDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        // creating payload for update case
        createPayloadAndUpdateAPInvoice();
      }
    } else {
      const createPayloadAndCreateAPInvoice = ({ isSelectedGRNForAPInvoice = false }) => {
        const apInvoiceLines = apInvoiceTableData?.map(
          (item) => ({
            tenant_product_id: item.tenant_product_id || '',
            product_sku_id: item.product_sku_id || '',
            quantity: apInvoiceType === 'ADHOC' ? Number(item.quantity) : Number(item.ap_received_qty),
            offer_price: Number(item.offer_price) || 0,
            remarks: item.remarks || '',
            tax_id: item?.taxId || '',
            grn_line_id: item?.grn_line_id || null,
            tax_group_info: item?.taxInfo,
            uom_id: item?.uomId || item?.uom_info?.[0]?.uom_id || item?.product_sku_info?.uom_info?.uom_id || '',
            uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
            expiry_date: item.expiryDate || null,
            line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty) * Number.parseFloat(item.offer_price))) * 100),
            is_discount_in_percent: item?.lineDiscountType === 'Percent',
            custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
            is_landed_cost: item?.isLandedCost,
            tally_purchase_account: item.tally_purchase_account,
            assessable_value: isTallyConnected ? (item?.assessable_value || 0) : null,
            taxable_type: item?.taxableType || '',
            nature_of_transaction: item?.natureOfTransaction || '',
          }),
        );
        if (apInvoiceLines?.length) {
          const payload = {
            account_payable_invoice: {
              ap_invoice_lines: apInvoiceLines.filter((item) => item),
              tenant_seller_info: isSelectedGRNForAPInvoice ? {
                ...selectedGRNForAPInvoice?.tenant_seller_info,
                gst_number: gstNumber,
              } : {
                ...selectedSeller?.seller_info,
                tenant_seller_id: selectedSeller?.tenant_seller_id,
                gst_number: gstNumber,
              },
              tally_purchase_account: purchaseAccount,
              tenant_department_id: tenantDepartmentId,
              ap_invoice_date: apInvoiceDate,
              vendor_invoice_date: invoiceDate,
              vendor_invoice_number: invoiceNumber || null,
              seller_id: isSelectedGRNForAPInvoice ? selectedGRNForAPInvoice?.seller_id : selectedSeller?.seller_info?.seller_id,
              seller_address_info: vendorAddress,
              payment_terms: isSelectedGRNForAPInvoice ? selectedGRNForAPInvoice?.payment_terms : (selectedSeller?.seller_info?.default_payment_terms?.due_days
                ? [
                  {
                    advance_amount: 0,
                    due_days: selectedSeller?.seller_info?.default_payment_terms?.due_days,
                  },
                ]
                : []),
              status: (apInvoiceLines.filter((item) => item).length > 0) && (approve ? 'ISSUED' : 'DRAFT'),
              tenant_id: selectedTenant,
              remark: terms,
              attachments: fileList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              custom_fields: CustomFieldHelpers.postCfStructure(cfAPInvoiceDoc),
              other_charges: chargeData?.map((charge) => ({
                charge_name: charge?.charge_name,
                charge_amount: charge?.charge_amount,
                charge_type: '',
                charge_sac_code: charge?.chargesSacCode || null,
                tax_info: charge?.chargesTaxInfo || null,
                ledger_name: charge?.tallyLedgerName || null,
              })) || [],
              discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (getLineTotals().totalAmount || 0)) * 100,
              is_discount_in_percent: discountType === 'Percent',
              is_line_wise_discount: !isLineWiseDiscount,
              tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
              tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
              tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
              org_currency_id: selectedCurrencyName?.org_currency_id,
              charge_1_name: charge1Name,
              charge_1_value: Number(charge1Value),
              freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
              freight_tax_info: freightTaxInfo,
              freight_sac_code: freightSacCode,
              e_way_bill: ewayBillList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              e_way_bill_number: ewayBillNumber,
              e_way_bill_date: ewayBillDate,
              bill_of_entry_date: billOfEntryDate,
              bill_of_entry_number: billOfEntryNumber,
              port_code: portCode,
              ap_invoice_number: initialAPInvoiceNumber?.toLowerCase()?.trim() === apInvoiceNumber?.toLowerCase()?.trim() ? null : apInvoiceNumber,
              seq_id: docSeqId || null,
              purchase_voucher_type: purchaseVoucherType,
              cost_centre: costCentre,
              // grn_id: (isSelectedGRNForAPInvoice ? selectedGRNForAPInvoice?.grn_id : selectedGRN?.grn_id) || null,
            },
          };
          createAPInvoice(
            {
              body: payload,
            },
            (apInvoice) => {
              if (selectedTags?.length) {
                const tagPayload = {
                  entity_name: 'ACCOUNT_PAYABLE_INVOICE',
                  entity_id: apInvoice?.account_payable_invoice?.ap_invoice_id,
                  action: 'ADD',
                  tag: selectedTags.join(','),
                };
                createTag(tagPayload, () => {
                  history.push(`/purchase/account-payable-invoice/view/${apInvoice?.account_payable_invoice?.ap_invoice_id}`);
                });
              } else {
                history.push(`/purchase/account-payable-invoice/view/${apInvoice?.account_payable_invoice?.ap_invoice_id}`);
              }
            },
          );
        }
      };

      if (selectedGRNForAPInvoice?.grn_id && isCreateValidData(apInvoiceTableData) && isDataValid2() && apInvoiceTableData?.filter((item) => Number(item.ap_received_qty))?.length > 0 && !cfAPInvoiceDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        // this case for ap invoice from grn screen
        createPayloadAndCreateAPInvoice({ isSelectedGRNForAPInvoice: true });
      }

      if (!selectedGRNForAPInvoice?.grn_id && isCreateValidData(apInvoiceTableData) && isDataValid2() && apInvoiceTableData?.filter((item) => Number(item.ap_received_qty))?.length > 0 && !cfAPInvoiceDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        // this case for grn by selecting po in grn form
        createPayloadAndCreateAPInvoice({});
      } else if (isCreateValidDataADHOC(apInvoiceTableData) && isDataValid2() && apInvoiceTableData?.filter((item) => Number(item.quantity))?.length > 0 && !cfAPInvoiceDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        // this case for creating adhoc

        createPayloadAndCreateAPInvoice({});
      }
    }
  };

  const addNewChargesRow = () => {
    const copychargeData = JSON.parse(JSON.stringify(chargeData));
    copychargeData.push({
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
      chargesTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
    });
    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { chargeData: copychargeData } });
  };

  const handleFullQuantity = (item) => {
    if (Number(Number(item?.received_qty) - Number(item?.total_ap_invoice_qty || 0)) < 0) {
      // Do Nothing
    } else {
      const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
      const updatedData = copyData.map((obj) => {
        if (!item.ap_invoice_line_id && (obj.grn_line_id === item.grn_line_id && item.grn_line_id)) {
          const discountValue = Number(item?.discount) || 0;
          const totalPrice = ((Number(item?.received_qty) - Number(item?.total_ap_invoice_qty || 0)) * item.offer_price);
          const taxableValue = item?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
          return {
            ...obj,
            ap_received_qty: Number(item?.received_qty) - Number(item?.total_ap_invoice_qty || 0),
            child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes,
          };
        }
        return obj;
      });
      dispatch({
        type: actionType.SET_MULTIPLE_FIELDS,
        payload: { apInvoiceTableData: updatedData },
      });
    }
  };

  // 🔹 Single reusable function to evaluate dependent fields (with expression handling included)
  const evaluateDependentFields = (cf, cfMap) => {
    if (!cf?.dependentFields?.length) return;

    for (const dependentFieldId of cf.dependentFields) {
      const dependentField = cfMap[dependentFieldId];
      if (dependentField?.defaultExpression) {
        try {
          // inline expression evaluator
          const expression = dependentField.defaultExpression;
          const evaluatedExpression = expression.replaceAll(/{{cf_(\d+)}}/g, (match, p1) => {
            const field = cfMap[p1];
            if (!field) return 0;
            return Number(field.fieldValue || 0);
          });

          const evaluatedValue = eval(evaluatedExpression);
          dependentField.fieldValue = evaluatedValue;

          // 🔁 recursive evaluation for chained dependencies
          this.evaluateDependentFields(dependentField, cfMap);
        } catch (error) {
          console.error(
            `Failed to evaluate expression for field ${dependentFieldId}:`,
            error
          );
        }
      }
    }
  };
  const customLineInputChange = (
    fieldValue,
    cfId,
    record,
    returnCfData = false
  ) => {
    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));

    let updatedLineCfs = null; // to hold updated lineCustomFields

    for (const item of copyData) {
      if (
        (record.po_line_id === item.po_line_id && item.po_line_id) ||
        (record.grn_line_id === item?.grn_line_id && item?.grn_line_id) ||
        (record.key === item?.key && item?.key)
      ) {
        const copylineCfs = item?.lineCustomFields || [];
        const cfMap = {};
        for (const cf of copylineCfs) {
          cfMap[cf.cfId] = cf; // build map for dependency evaluation
        }

        for (const cf of copylineCfs) {
          if (cf.cfId === cfId) {
            // update current field
            cf.fieldValue =
              cf.fieldType === 'ATTACHMENT'
                ? fieldValue?.map((attachment) => ({
                  url:
                    attachment?.response?.response?.location ||
                    attachment?.url,
                  type: attachment.type,
                  name: attachment.name,
                  uid: attachment.uid,
                }))
                : fieldValue;

            // ✅ call reusable dependent evaluator (must be imported or passed as prop)
            evaluateDependentFields(cf, cfMap);

            updatedLineCfs = copylineCfs; // store updated lineCustomFields
            break;
          }
        }
        break;
      }
    }

    // ✅ if returnCfData flag is true → just return updated lineCustomFields
    if (returnCfData) {
      return updatedLineCfs;
    }

    const keyUseForUpdateQuantity = apInvoiceType !== 'ADHOC' ? 'ap_received_qty' : 'quantity';

    const modifiedCopyData = copyData?.map((data) => ({
      ...data,
      [keyUseForUpdateQuantity]:
        data?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')
          ?.fieldValue || data?.[keyUseForUpdateQuantity],
      offer_price:
        data?.lineCustomFields?.find((cf) => cf.fieldName === 'Rate')
          ?.fieldValue || data?.offer_price,
    }));

    dispatch({
      type: actionType.SET_MULTIPLE_FIELDS,
      payload: { apInvoiceTableData: modifiedCopyData },
    });

    if (isLineWiseDiscount) {
      handleDiscountPercentageChange({
        value: discountPercentage,
        tableData: modifiedCopyData,
      });
    }
  };

  const isVendorOverseas = useMemo(() => {
    const sellerType = selectedSeller?.seller_info?.seller_type || selectedGRNForAPInvoice?.tenant_seller_info?.seller_type || selectedAPInvoice?.tenant_seller_info?.seller_type;
    const hideTax = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    return sellerType === 'OVERSEAS' && hideTax;
  }, [
    selectedSeller,
    selectedGRNForAPInvoice,
    selectedAPInvoice,
    user,
  ]);

  const hasEmptyRow = apInvoiceTableData?.some(
    (item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)
  );

  const purchaseAccountList = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant)?.tally_configuration?.purchase_account_list;

  return (
    <Fragment>
      {getAPInvoiceLoading ? (
        <FormLoadingSkull />
      ) : (
        <Fragment>
          <div
            className="form__wrapper"
            style={{ paddingTop: selectedGRNForAPInvoice?.grn_id ? '0px' : '90px' }}
          >
            {formSubmitted
              && (copyDocLevelError?.length > 0
                || copyLineLevelError?.length > 0)
              && (
                <ErrorHandle
                  message="Mandatory fields required"
                  docLevelErrors={copyDocLevelError}
                  lineLevelErrors={copyLineLevelError}
                />
              )}
            <div className="ant-row">
              <div className="ant-col-md-24">
                <div className="form__section">
                  <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                    <H3Text text="PART A" className="form__section-title" />
                    <div className="form__section-line" />
                    <div style={{
                      display: 'flex', justifyContent: 'end', alignItems: 'center', gap: '2px',
                    }}
                    >
                      <CurrencyConversionV2
                        selectedCurrencyName={selectedCurrencyName}
                        selectedCurrencyID={selectedCurrencyID}
                        isAutomaticConversionRate={isAutomaticConversionRate}
                        currencyConversionRate={currencyConversionRate}
                        setCurrencyConversionRate={(val) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { currencyConversionRate: val } })}
                        setIsAutomaticConversionRate={(val) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { isAutomaticConversionRate: val } })}
                        setSelectedCurrencyName={(val) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { selectedCurrencyName: val } })}
                        setSelectedCurrencyID={(val) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { selectedCurrencyID: val } })}
                      />
                      <CustomDocumentInputs
                        customFields={cfAPInvoiceDoc}
                        updateCustomFields={(cf) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { cfAPInvoiceDoc: cf } })}
                      />
                    </div>
                  </div>
                  {/* Form Section Inputs */}
                  <div className="form__section-inputs mg-bottom-20">
                    <div className="ant-row">
                      <div className="ant-col-md-24">
                        {!selectedGRNForAPInvoice && (
                          <div className="form__input-row" style={{ alignItems: 'center', marginBottom: '15px' }}>
                            <Radio.Group
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading || getDocCFV2APInvoiceLoading || match?.params?.apiId || selectedGRNForAPInvoice?.grn_id}
                              value={apInvoiceType}
                              className="mg-top-5"
                              onChange={(e) => handleAPInvoiceTypeChange(e.target.value)}
                            >
                              <Radio value="GRN">Goods Receiving Note</Radio>
                              <Radio value="ADHOC">ADHOC</Radio>
                              <Radio value="MULTIGRN">MULTI GRNs</Radio>
                            </Radio.Group>
                          </div>
                        )}
                      </div>
                      <DocumentNumberSeqInput
                        valueFromProps={apInvoiceNumber}
                        updateCase={match?.params?.apiId}
                        setInitialDocSeqNumber={(value) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { initialAPInvoiceNumber: value } })}
                        entityName="ACCOUNT_PAYABLE_INVOICE"
                        docSeqId={docSeqId}
                        tenantId={selectedTenant}
                        onChangeFromProps={(event, newValue, seqId) => handleDocSeqChange(event, newValue, seqId)}
                        docTitle="API#"
                        formSubmitted={formSubmitted}
                      />
                      {apInvoiceType === 'ADHOC'
                        && (
                          <div className="ant-col-md-6">
                            <TenantSelector
                              selectedTenant={selectedTenant}
                              showSearch
                              onChange={(value) => {
                                const copychargeData = [];
                                copychargeData.push({
                                  chargeKey: uuidv4(),
                                  charge_name: '',
                                  charge_amount: 0,
                                  chargesTaxData: {
                                    child_taxes: [
                                      {
                                        tax_amount: 0,
                                        tax_type_name: '',
                                      },
                                    ],
                                  },
                                });

                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: {
                                    selectedTenant: value,
                                    tenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_store_id,
                                    vendorAddress: '',
                                    selectedTenantSeller: '',
                                    apInvoiceTableData: [{
                                      key: uuidv4(),
                                      asset1: '',
                                      product_sku_name: '',
                                      quantity: '',
                                      selling_price: '',
                                      taxId: '',
                                      lot: '',
                                      product_sku_id: null,
                                      product_sku_info: null,
                                      child_taxes: [{
                                        tax_amount: 0,
                                        tax_type_name: '',
                                      }],
                                      // lineCustomFields: cfGoodReceivingNotesLine
                                      lineCustomFields: visibleLineCfs,
                                    }],
                                    gstNumber: '',
                                    chargeData: copychargeData,
                                    purchaseAccount: null,
                                  },
                                });
                              }}
                              showAll={false}
                              title="Location"
                              customStyle={{ height: '28px', border: 'none' }}
                              labelClassName="form__input-row__label"
                              inputClassName="form__input-row__input"
                              containerClassName="form__input-row"
                              noDropdownAlign
                              placeholder="Select Business Unit"
                              includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.CREATE)}
                              disabled={match?.params?.apiId || selectedGRNForAPInvoice || selectedGRN}
                            />
                          </div>
                        )}
                      {(match?.params?.apiId || selectedGRNForAPInvoice?.grn_id) ? (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="Vendor"
                              className="form__input-row__label"
                            />
                            <div className="form__input-row__input">
                              <Tooltip
                                title={selectedGRNForAPInvoice ? selectedGRNForAPInvoice?.tenant_seller_info?.seller_name : selectedAPInvoice?.tenant_seller_info?.seller_name}
                                placement="top"
                                overlayInnerStyle={{ background: 'white', color: 'black' }}
                              >
                                <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                  <H3Text
                                    text={selectedGRNForAPInvoice ? selectedGRNForAPInvoice?.tenant_seller_info?.seller_name : selectedAPInvoice?.tenant_seller_info?.seller_name}
                                    style={{ padding: '7px' }}
                                  />
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="ant-col-md-6">
                          <SelectSellerV2
                            selectedSeller={selectedTenantSeller}
                            onChange={(value) => handleSellerSelection(value)}
                            containerClass="orgInputContainer form__input-row"
                            inputClassName={`form__input-row__input ${(formSubmitted && !selectedSeller) ? 'form__input-row__input-error' : ''}`}
                            labelClassName="form__input-row__label"
                            disabled={createAPInvoiceLoading || match?.params?.apiId || selectedGRNForAPInvoice?.grn_id}
                            showAddVendor
                            tenantId={selectedTenant}
                          />
                        </div>
                      )}
                      {((match?.params?.apiId || selectedGRNForAPInvoice?.grn_id) && (selectedAPInvoice?.linked_grns?.[0]?.grn_number || selectedGRNForAPInvoice?.grn_number) && apInvoiceType !== 'MULTIGRN') ? (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="GRN"
                              className="form__input-row__label"
                            />
                            <div className="form__input-row__input">
                              <H3Text
                                text={`${selectedAPInvoice?.linked_grns?.[0]?.grn_number || selectedGRNForAPInvoice?.grn_number} `}
                                style={{ padding: '7px' }}
                              />
                            </div>
                          </div>
                        </div>
                      ) : (apInvoiceType !== 'ADHOC') && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <div className="form__input-row__label">
                              Select GRN
                            </div>
                            <div>
                              <SelectGRNForAPInvoice
                                disabled={!selectedTenantSeller || createAPInvoiceLoading || updateAPInvoiceLoading}
                                selectedGRNValue={selectedGRNValue}
                                mode={apInvoiceType === 'MULTIGRN' ? 'multiple' : 'single'}
                                onChange={(data) => {
                                  if (apInvoiceType === 'MULTIGRN') {
                                    const oldSelectedGRNValues = selectedGRNData || [];
                                    const selectedGRNValues = (data || [])
                                      .map((item) => grnV2?.grn?.find((item2) => item2.grn_id === Number(item?.value)))
                                      .filter(Boolean);

                                    // Step 2: Merge old + new uniquely (by grn_id)
                                    const mergedSelectedGRNs = [
                                      ...oldSelectedGRNValues,
                                      ...selectedGRNValues.filter(
                                        (newItem) => !oldSelectedGRNValues.some((oldItem) => oldItem.grn_id === newItem.grn_id)
                                      ),
                                    ];
                                    dispatch({
                                      type: actionType.SET_MULTIPLE_FIELDS,
                                      payload: {
                                        selectedGRNData: mergedSelectedGRNs,
                                      },
                                    });
                                    handleMultiGRNChange({
                                      selectedGRNCopy: selectedGRNValues,
                                      data,
                                      mergedSelectedGRNs,
                                    });
                                  } else {

                                    let selectedGRNCopy = grnV2?.grn.filter(
                                      (item) => item.grn_id === Number(data.value),
                                    );
                                    selectedGRNCopy = selectedGRNCopy?.[0];
                                    handleGRNChange({ selectedGRNCopy, data });
                                  }
                                }}
                                onClear={() => {
                                  dispatch({
                                    type: actionType.SET_MULTIPLE_FIELDS,
                                    payload: {
                                      gstNumber: '',
                                      formSubmitted: null,
                                      invoiceNumber: '',
                                      selectedCurrencyID: null,
                                      selectedCurrencyName: null,
                                      isAutomaticConversionRate: null,
                                      currencyConversionRate: null,
                                      visibleLineCfs: cfAPInvoiceLine,
                                      discountPercentage: null,
                                      cfAPInvoiceDoc: CustomFieldHelpers.getCfStructure(cfV2DocAPInvoice?.data?.document_custom_fields, true) || [],
                                      selectedGRNValue: [],
                                      selectedGRN: null,
                                      apInvoiceTableData: [],
                                      selectedTags: [],
                                      vendorAddress: '',
                                      chargeData: [],
                                      purchaseAccount: null,
                                    },
                                  });
                                }}
                                grnData={grnV2}
                                getGRNV2={getGRNV2}
                                selectedTenant={selectedTenant}
                                selectedTenantSeller={selectedTenantSeller}
                                loading={getGRNV2Loading || createAPInvoiceLoading || updateAPInvoiceLoading}
                                inputClassName={`orders-selector ${formSubmitted && !selectedGRNValue ? 'form__input-row__input-error' : ''}`}
                              />
                              {apInvoiceType === 'GRN' && selectedGRN ? (
                                <H3Text
                                  text="View GRN"
                                  className="create-api__view-grn-button_new"
                                  onClick={() => {
                                    const win = window.open(`/purchase/goods-receiving/view/${selectedGRN?.grn_id}`, '_blank');
                                    win.focus();
                                  }}
                                />
                              ) : (
                                apInvoiceType === 'MULTIGRN' && selectedGRNValue?.length > 0 && (
                                  <H3Text
                                    text="View GRN"
                                    className="create-api__view-grn-button_new"
                                    onClick={() => {
                                      dispatch({
                                        type: actionType.SET_MULTIPLE_FIELDS,
                                        payload: {
                                          openDrawerOfMultipleSelectedGRNs: true,
                                        },
                                      });
                                    }}
                                  />
                                ))}

                            </div>
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text text="Department" className="form__input-row__label" />
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={selectedTenant}
                              selectedDepartment={tenantDepartmentId}
                              noDropdownAlign
                              onChange={(value) => {
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: {
                                    tenantDepartmentId: value?.tenant_department_id,
                                  },
                                });
                              }}
                              tenentLevelDepartment
                              emptyNotAllowed
                              customStyle={{
                                border: '1px solid rgba(68, 130, 218, 0.25)',
                                borderRadius: '4px',
                                height: '28px',
                                padding: '0px',
                                marginBottom: '10px',
                              }}
                              loading={createAPInvoiceLoading || updateAPInvoiceLoading}
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading || selectedAPInvoice || selectedGRNForAPInvoice || selectedGRN}
                              labelClassName="mo-form__input-row__label"
                              inputClassName="orgFormInput input"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="GST Number"
                            className="form__input-row__label"
                          />
                          <H3FormInput
                            name="GST Number"
                            type="text"
                            disabled={
                              createAPInvoiceLoading
                              || updateAPInvoiceLoading
                            }
                            containerClassName="orgInputContainer form__input-row__input"
                            labelClassName="orgFormLabel"
                            inputClassName="orgFormInput input"
                            placeholder=""
                            onChange={(event) => {
                              dispatch({
                                type: actionType.SET_MULTIPLE_FIELDS,
                                payload: {
                                  gstNumber: event.target.value,
                                },
                              });
                            }}
                            value={gstNumber}
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Vendor Invoice#"
                            className="form__input-row__label"
                          />
                          <H3FormInput
                            name="invoice number"
                            type="text"
                            containerClassName="orgInputContainer form__input-row__input"
                            labelClassName="orgFormLabel"
                            inputClassName="orgFormInput input"
                            placeholder=""
                            onChange={(e) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { invoiceNumber: e.target.value } })}
                            value={invoiceNumber}
                          />
                        </div>
                      </div>
                      {/* api date */}
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <div className="form__input-row__label">
                            AP Invoice Date
                            <span style={{ color: 'red' }}>{'  *'}</span>
                          </div>
                          <div className={`form__input-row__input ${(formSubmitted && !apInvoiceDate) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={dayjs(apInvoiceDate, 'DD-MM-YYYY')}
                              onChange={(value) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { apInvoiceDate: value } })}
                              disabledDate={
                                (d) => !d
                                  || (user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.allow_future_date_grn ? null : d.isAfter(dayjs().add(1, 'days').format('YYYY-MM-DD')))
                                  || (user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.allow_past_date_grn ? null : d.isSameOrBefore(dayjs().format('YYYY-MM-DD')))
                              }
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                              format="DD-MM-YYYY"
                              allowClear={false}
                              style={{
                                border: '1px solid rgba(68, 130, 218, 0.2)',
                                borderRadius: '2px',
                                height: '28px',
                                padding: '1px 3px',
                                width: '100%',
                                background: 'white',
                                marginBottom:
                                  formSubmitted && !apInvoiceDate ? '0px' : '10px',
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Invoice Date"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__input ${(formSubmitted && !invoiceDate) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={invoiceDate}
                              onChange={(value) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { invoiceDate: value } })}
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                              format="DD-MM-YYYY"
                              style={{
                                borderRadius: '2px',
                                height: '28px',
                                padding: '1px 3px',
                                width: '100%',
                                background: 'white',
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      {/* b) Bill of Entry Number */}
                      {isTallyConnected && <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Bill of Entry Number"
                            className="form__input-row__label"
                          />
                          <div className="form__input-row__input">
                            <H3FormInput
                              name="billOfEntryNumber"
                              type="text"
                              containerClassName="orgInputContainer"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              onChange={(event) =>
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: { billOfEntryNumber: event.target.value },
                                })
                              }
                              value={billOfEntryNumber}
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                            />
                          </div>
                        </div>
                      </div>}

                      {/* d) Bill of Entry Date */}
                      {isTallyConnected && <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Bill of Entry Date"
                            className="form__input-row__label"
                          />
                          <div className="form__input-row__input">
                            <DatePicker
                              value={billOfEntryDate}
                              onChange={(value) =>
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: { billOfEntryDate: value },
                                })
                              }
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                              format="DD-MM-YYYY"
                              style={{
                                borderRadius: '2px',
                                height: '28px',
                                padding: '1px 3px',
                                width: '100%',
                                background: 'white',
                              }}
                            />
                          </div>
                        </div>
                      </div>}

                      {/* c) Port Code */}
                      {isTallyConnected && <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Port Code"
                            className="form__input-row__label"
                          />
                          <div className="form__input-row__input">
                            <H3FormInput
                              name="portCode"
                              type="text"
                              containerClassName="orgInputContainer"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              onChange={(event) =>
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: { portCode: event.target.value },
                                })
                              }
                              value={portCode}
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                            />
                          </div>
                        </div>
                      </div>}

                      {user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Purchase Account" className="form__input-row__label" required />
                            <div className={`form__input-row__input ${formSubmitted && !purchaseAccount ? 'form__input-row__input-error' : ''}`}>
                              <PRZSelect
                                value={purchaseAccount}
                                onChange={(value) => {
                                  dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { purchaseAccount: value } });
                                }}
                                loading={createAPInvoiceLoading || updateAPInvoiceLoading}
                                disabled={createAPInvoiceLoading}
                              >
                                {purchaseAccountList?.map((item) => (
                                  <Option key={item.purchase_account_name} value={item.purchase_account_name}>
                                    {' '}
                                    {item.purchase_account_name}
                                  </Option>
                                ))}
                              </PRZSelect>

                            </div>
                          </div>
                        </div>
                      )}
                      {isTallyConnected && (
                        <Fragment>
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Tally Voucher Type" className="form__input-row__label"/>
                              <div className='form__input-row__input'>
                                <PRZSelect
                                  value={purchaseVoucherType || 'Purchase'}
                                  onChange={(value) => {
                                    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { purchaseVoucherType: value } });
                                  }}
                                  loading={createAPInvoiceLoading || updateAPInvoiceLoading || tallyConfigurationsLoading}
                                  disabled={createAPInvoiceLoading}
                                  options={tallyConfigurations?.voucher_types?.map((__k) => ({
                                    value: __k.voucher_type_name,
                                    label: __k.voucher_type_name,
                                  }))}
                                />
                              </div>
                            </div>
                          </div>
                          {tallyConfigurations?.cost_centres?.length > 0 &&  (<div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Tally Cost Centre" className="form__input-row__label"/>
                              <div className='form__input-row__input'>
                                <PRZSelect
                                  value={costCentre || ''}
                                  onChange={(value) => {
                                    dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { costCentre : value } });
                                  }}
                                  loading={createAPInvoiceLoading || updateAPInvoiceLoading || tallyConfigurationsLoading}
                                  disabled={createAPInvoiceLoading}
                                  options={tallyConfigurations?.cost_centres?.map((__k) => ({
                                    value: __k.name,
                                    label: __k.name,
                                  }))}
                                />
                              </div>
                            </div>
                          </div>)}
                        </Fragment>
                      )}
                      {getLineTotals().totalAmount >= 50000 && user?.tenant_info?.country_code === 'IN'
                        ? (
                          <React.Fragment>
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text
                                  text="e-Way Bill number"
                                  className="form__input-row__label"
                                  required={user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_e_way_bill_mandatory}
                                />
                                <H3FormInput
                                  name="Ewaybill number"
                                  type="text"
                                  containerClassName={`orgInputContainer form__input-row__input ${(formSubmitted && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_e_way_bill_mandatory && !ewayBillNumber) ? 'form__input-row__input-error' : ''}`}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput input"
                                  onChange={(event) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { ewayBillNumber: event.target.value } })}
                                  maxlength="100"
                                  value={ewayBillNumber}
                                  disabled={createAPInvoiceLoading}
                                />
                              </div>
                            </div>
                            {isTallyConnected && <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text
                                  text="e-Way Bill date"
                                  className="form__input-row__label"
                                  required={
                                    user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_e_way_bill_mandatory
                                  }
                                />
                                <div
                                  className='form__input-row__input'
                                >
                                  <DatePicker
                                    value={ewayBillDate}
                                    onChange={(value) =>
                                      dispatch({
                                        type: actionType.SET_MULTIPLE_FIELDS,
                                        payload: { ewayBillDate: value },
                                      })
                                    }
                                    disabled={createAPInvoiceLoading}
                                    format="DD-MM-YYYY"
                                    style={{
                                      borderRadius: '2px',
                                      height: '28px',
                                      padding: '1px 3px',
                                      width: '100%',
                                      background: 'white',
                                    }}
                                  />
                                </div>
                              </div>
                            </div>}

                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text
                                  text="e-Way Bill Attachment"
                                  className="form__input-row__label"
                                  required={user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_e_way_bill_mandatory}
                                />
                                <Upload
                                  action={Constants.UPLOAD_FILE}
                                  // listType="picture-card"
                                  fileList={ewayBillList}
                                  disabled={createAPInvoiceLoading}
                                  multiple
                                  onChange={(ewayBillListData) => {
                                    dispatch({
                                      type: actionType.SET_MULTIPLE_FIELDS,
                                      payload: {
                                        ewayBillList: ewayBillListData?.fileList?.map((item) => ({
                                          ...item,
                                          url: item?.response?.response?.location || item?.url,
                                        })),
                                      },
                                    });
                                  }}
                                >
                                  {ewayBillList?.length >= 1 ? null : uploadButtonFormLevel}
                                </Upload>
                              </div>
                            </div>
                          </React.Fragment>
                        ) : <React.Fragment />}

                      <div className="ant-col-md-6">
                        <div className="form__input-row ">
                          <div className="form__input-row__label">
                            Labels
                          </div>
                          <TagSelector
                            hideTitle
                            entityType="GRN"
                            selectedTags={selectedTags}
                            isMultiple
                            showSearch
                            onChange={(value) => {
                              dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { selectedTags: value } });
                            }}
                            placeholder="Select Tags"
                            isForm
                            containerWrapper="form__input-row__input"
                            disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                            maxTagCount="responsive"
                          />
                        </div>
                      </div>
                      <CustomFieldV3
                        customFields={cfAPInvoiceDoc}
                        formSubmitted={formSubmitted}
                        customInputChange={(value, cfId) => customInputChange(value, cfId)}
                        wrapperClassName="ant-col-md-6"
                        containerClassName="form__input-row"
                        labelClassName="form__input-row__label"
                        inputClassName="form__input-row__input"
                        errorClassName="form__input-row__input-error"
                        disableCase={createAPInvoiceLoading || updateAPInvoiceLoading
                          || (apInvoiceType !== 'ADHOC' && !selectedGRNValue && !selectedAPInvoice?.linked_grns?.length && !selectedGRNForAPInvoice?.grn_id)}
                        hideTitle
                        isCarryForward
                      />
                    </div>
                  </div>
                </div>
                {/* FOrm Section B */}
                <div className="form__section">
                  <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                    <H3Text text="PART B" className="form__section-title" />
                    <div className="form__section-line" />
                  </div>
                  <div className="form__section-inputs">
                    <div className="ant-row">
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            text="Vendor's Address"
                            className="form__input-row__label"
                            required
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !vendorAddress) ? 'form__input-row__address-error' : ''} `}>
                            <div className="form__input-row__address">
                              {vendorAddress && (
                                <div className="form__input-row__address-info">
                                  <div className="form__input-row__address-l1">
                                    {vendorAddress?.address1}
                                  </div>
                                  <div className="form__input-row__address-l2">
                                    {`${vendorAddress?.city}, ${vendorAddress?.state}, ${vendorAddress?.postal_code}, ${vendorAddress?.country}`}
                                  </div>
                                </div>
                              )}
                              {!vendorAddress && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => {
                                  dispatch({
                                    type: actionType.SET_MULTIPLE_FIELDS,
                                    payload: {
                                      selectedAddressType: 'SELLER',
                                      showAddressDrawer: true,
                                    },
                                  });
                                }}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {formSubmitted && !vendorAddress && (
                              <div className="input-error">
                                *Please select vendor address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Form Lines */}
            <div className="api-form-lines-wrapper">
              <APInvoiceFormLines
                customClassName={!!selectedGRNForAPInvoice}
                apInvoiceTableData={getAPInvoiceTableData(apInvoiceTableData)}
                handleProductChangeValue={handleProductChangeValue}
                handleProductChange={handleProductChange}
                apInvoiceType={apInvoiceType}
                addNewRow={addNewRow}
                apiId={match?.params?.apiId}
                updateAPInvoiceTableData={(value) => {
                  dispatch({
                    type: actionType.SET_MULTIPLE_FIELDS,
                    payload: { apInvoiceTableData: value },
                  });
                }}
                tenantDepartmentId={tenantDepartmentId}
                selectedTenant={selectedTenant}
                formSubmitted={formSubmitted}
                handleMultiProductChange={handleMultiProductChange}
                loading={createAPInvoiceLoading || updateAPInvoiceLoading}
                billFromState={getBillFromAddress}
                billToState={getBillToAddress}
                isLineWiseDiscount={isLineWiseDiscount}
                onChangeLineWiseDiscount={(value) => {
                  dispatch({
                    type: actionType.SET_MULTIPLE_FIELDS,
                    payload: { isLineWiseDiscount: value },
                  });
                }}
                discountPercentage={(value) => dispatch({
                  type: actionType.SET_MULTIPLE_FIELDS,
                  payload: { discountPercentage: value },
                })}
                discountType={discountType}
                visibleLineCfs={visibleLineCfs}
                customFieldVisibilityChange={(visible, cfId) => {
                  customFieldVisibilityChange(visible, cfId);
                }}
                selectedCurrencyName={selectedCurrencyName}
                isCarryForward={selectedGRNForAPInvoice || selectedGRNValue}
                handleDelete={(value, id) => handleDelete(value, id)}
                handleFullQuantity={handleFullQuantity}
                customLineInputChange={(value, cfId, key, returnCfData) => customLineInputChange(value, cfId, key, returnCfData)}
                isVendorOverseas={isVendorOverseas}
              />
            </div>
            {/* Form Data */}
            <div className="form__data-wrapper">
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div className="flex-display" style={{ gap: '5px' }}>
                    <div
                      className={`new-row-button ${apInvoiceTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`}
                      onClick={() => {
                        if (!apInvoiceTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) addNewRow(false);
                      }}
                    >
                      <FontAwesomeIcon icon={faPlusCircle} />
                      &nbsp;New Item
                    </div>
                    <div
                      className={`new-row-button ${apInvoiceTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`}
                      onClick={() => {
                        if (!apInvoiceTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) addNewRow(true);
                      }}
                    >
                      <FontAwesomeIcon icon={faPlusCircle} />
                      &nbsp;Landed Cost
                    </div>
                  </div>
                </div>
                <div className="ant-col-md-12">
                  <div className="form__data-tc">
                    <label className="orgFormLabel">
                      Additional Remarks
                    </label>
                    <textarea
                      className="orgFormInput"
                      rows="4"
                      onChange={(event) => {
                        dispatch({
                          type: actionType.SET_MULTIPLE_FIELDS,
                          payload: { terms: event.target.value },
                        });
                      }}
                      disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                      value={terms}
                      style={{
                        height: '100px',
                      }}
                    />
                  </div>
                  <div className="form__data-attachment">
                    <label className="orgFormLabel">Attachment(s)</label>
                    <Upload
                      action={Constants.UPLOAD_FILE}
                      listType="picture-card"
                      fileList={fileList || []}
                      disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                      multiple
                      onChange={(fileListData) => {
                        dispatch({
                          type: actionType.SET_MULTIPLE_FIELDS,
                          payload: {
                            fileList: fileListData?.fileList?.map((item) => ({
                              ...item,
                              url: item?.response?.response?.location || item?.url,
                            })),
                          },
                        });
                      }}
                    >
                      {fileList?.length >= 20 ? null : uploadButtonForUploadingAttachments}
                    </Upload>
                  </div>
                </div>
                <div className="ant-col-md-12">
                  <div className="form-calculator__wrapper">
                    <div className="form-calculator">
                      <div className="form-calculator__field">
                        <H3Text
                          text="Sub Total"
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY((getLineTotals().totalAmount || 0), selectedCurrencyName?.currency_code)}
                          className="form-calculator__field-value"
                          hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                          popOverMessage="You don't have access to view sub total amount"
                        />
                      </div>
                      {!isLineWiseDiscount && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Discount"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY((getLineTotals().totalDiscount || 0), selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                          />
                        </div>
                      )}
                      {isLineWiseDiscount && selectedCurrencyName && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Discount"
                            className="form-calculator__field-name"
                          />
                          <div
                            className="form-calculator__field-value"
                            style={{ display: 'flex' }}
                          >
                            <div style={{ width: '112px' }}>
                              <H3FormInput
                                value={discountPercentage}
                                type="number"
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                onChange={(e) => {
                                  const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                                  copyData.map((item) => {
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                                    const discountValue = discountType === 'Percent' ? Number.parseFloat(e.target.value || 0) : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(e.target.value || 0);

                                    const taxableValue = discountType === 'Percent'
                                      ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                                      : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                                    item.discount = discountValue;
                                    item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
                                    return apInvoiceTableData;
                                  });
                                  dispatch({
                                    type: actionType.SET_MULTIPLE_FIELDS,
                                    payload: { apInvoiceTableData: copyData, discountPercentage: Number.parseFloat(e.target.value || 0) },
                                  });
                                }}
                              />
                            </div>
                            <div className="form-calculator__discount-type">
                              <PRZSelect
                                value={discountType}
                                onChange={(value) => {
                                  const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                                  copyData.map((item) => {
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                                    const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number(discountPercentage);

                                    const taxableValue = value === 'Percent'
                                      ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                                      : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                                    item.lineDiscountType = value;
                                    item.discount = discountValue;
                                    item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes;
                                    return item;
                                  });
                                  dispatch({
                                    type: actionType.SET_MULTIPLE_FIELDS,
                                    payload: {
                                      apInvoiceTableData: copyData,
                                      discountType: value,
                                    },
                                  });
                                }}
                              // disabled
                              >
                                <Option key="Amount" value="Amount">
                                  {`${selectedCurrencyName?.currency_symbol}`}
                                </Option>
                                <Option key="Percent" value="Percent">
                                  %
                                </Option>
                              </PRZSelect>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="form-calculator__field">
                        <div
                          className="form-calculator__field-name"
                          style={{ display: 'flex', alignItems: 'center' }}
                        >
                          <H3Text
                            text={charge1Name}
                            style={{ marginRight: '10px' }}
                          />
                          {freightTax && (
                            <div style={{
                              color: '#2d7df7',
                              fontWeight: '400',
                              fontSize: '12px',
                            }}
                            >
                              {`tax@${freightTax}%`}
                            </div>
                          )}
                        </div>
                        <div
                          className="form-calculator__field-value"
                          style={{ display: 'flex' }}
                        >
                          <div style={{ width: '112px' }}>
                            <H3FormInput
                              value={charge1Value}
                              type="number"
                              containerClassName={`${formSubmitted && Number(charge1Value) < 0
                                ? 'form-error__input'
                                : ''
                                }`}
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput"
                              onChange={(e) => {
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: {
                                    charge1Value: e.target.value,
                                    freightTaxData: {
                                      ...freightTaxData,
                                      child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes,
                                    },
                                  },
                                });
                              }}
                              disabled={!isReceivedQty}
                            />
                          </div>
                          {!isVendorOverseas && (
                            <FreightTaxInput
                              freightTaxId={freightTaxId}
                              openFreightTax={openFreightTax}
                              sacCode={freightSacCode}
                              setOpenFreightTax={(value) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { openFreightTax: value } })}
                              setFreightTaxData={(value) => dispatch({
                                type: actionType.SET_MULTIPLE_FIELDS,
                                payload: {
                                  freightTaxId: !value ? 'Not Applicable' : value?.tax_id,
                                  freightTax: !value ? null : value?.tax_value,
                                  freightTaxInfo: !value ? null : value,
                                  freightTaxData: !value ? {
                                    child_taxes: [
                                      {
                                        tax_amount: 0,
                                        tax_type_name: '',
                                      },
                                    ],
                                  } : {
                                    ...value,
                                    child_taxes: Helpers.computeTaxation(charge1Value, value, getBillFromAddress(), getBillToAddress())?.tax_info?.child_taxes,
                                  },
                                },
                              })}
                              setSacCode={(value) => dispatch({ type: actionType.SET_MULTIPLE_FIELDS, payload: { freightSacCode: value } })}
                            />
                          )}
                        </div>
                      </div>

                      {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}

                      <div className="form-calculator__field">
                        <H3Text
                          text="Taxable Amount"
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY((getLineTotals().totalBase || 0), selectedCurrencyName?.currency_code)}
                          className="form-calculator__field-value"
                          hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                          popOverMessage="You don't have access to view taxable amount"
                        />
                      </div>

                      {!isVendorOverseas && (apInvoiceType !== 'ADHOC' ? apInvoiceTableData?.length > 0 : true) && apInvoiceTableData?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...apInvoiceTableData, freightTaxData, ...chargeData?.flatMap((charge) => charge?.chargesTaxData)]))?.map((tax, i) => (
                        <Fragment key={i}>
                          <div className="form-calculator__field">
                            <H3Text
                              text={tax?.tax_type_name}
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={MONEY((tax?.tax_amount || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage={`You don't have access to view ${tax?.tax_type_name?.toLowerCase()}`}
                            />
                          </div>
                        </Fragment>
                      ))}

                      {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && !isVendorOverseas && (
                        <div className="form-calculator__field">
                          <div
                            className="form-calculator__field-name"
                            style={{
                              display: 'flow',
                            }}
                          >
                            <Radio.Group
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                              onChange={(event) => {
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: {
                                    taxTypeName: event.target.value,
                                    taxTypeId: '',
                                    taxTypeInfo: null,
                                  },
                                });
                              }}
                              value={taxTypeName}
                            >
                              <Radio value="TDS">TDS</Radio>
                              <Radio value="TCS">TCS</Radio>
                            </Radio.Group>
                            <SelectTaxType
                              containerClassName="orgInputContainer"
                              selectedTaxType={taxTypeId}
                              disabled={createAPInvoiceLoading || updateAPInvoiceLoading}
                              onChange={(value) => {
                                dispatch({
                                  type: actionType.SET_MULTIPLE_FIELDS,
                                  payload: {
                                    taxTypeId: value?.tax_id,
                                    taxTypeInfo: value,
                                    taxTypeName: value?.tax_type_name,
                                    taxType: value?.tax_type,
                                  },
                                });
                              }}
                              taxTypeName={taxTypeName}
                              customStyle={{
                                width: '220px',
                                backgroundColor: 'white',
                              }}
                            />
                          </div>

                          {taxTypeName === 'TCS' ? (
                            <H3Text
                              text={MONEY((getLineTotals().totalTcs || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                            />
                          ) : (
                            <H3Text
                              text={MONEY((getLineTotals().totalTds || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                            />
                          )}
                        </div>
                      )}
                      {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
                      <div
                        className="new-charge-row-button"
                        onClick={() => addNewChargesRow()}
                      >
                        <span className="new-charge-row-button__icon">
                          <PlusCircleFilled />
                        </span>
                        <div>Add Charges</div>
                      </div>
                      {user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Round Off"
                            className="form-calculator__field-name"
                          />
                          <Tooltip
                            title={`Round Off method for AP Invoice is set to ${user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                          >
                            <div style={{ cursor: 'pointer' }}>
                              <FontAwesomeIcon icon={faCircleInfo} size="lg" style={{ color: '#2D7DF7' }} />
                            </div>
                          </Tooltip>
                          <H3Text
                            text={`${Helpers.configuredRoundOff(getLineTotals().apInvoiceTotal, user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                              Math.abs(Helpers.configuredRoundOff(getLineTotals().apInvoiceTotal, user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.round_off_method)?.roundOff),
                              selectedCurrencyName?.currency_code,
                            )}`}
                            className="form-calculator__field-value"
                            hideText={
                              isDataMaskingPolicyEnable && isHideCostPrice
                            }
                            popOverMessage="You don't have access to view sub total"
                          />
                        </div>
                      )}
                      <div className="form-calculator__field form-calculator__field-total">
                        <H3Text
                          text="Grand Total"
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY(Helpers.configuredRoundOff(getLineTotals().apInvoiceTotal || 0, user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.round_off_method)?.value, selectedCurrencyName?.currency_code)}
                          className="form-calculator__field-value"
                          hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                          popOverMessage="You don't have access to view grand total amount"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Fragment>
      )}
      {/* // Footer */}

      <br />
      <br />
      <br />

      <Footer
        state={state}
        priceMasking={priceMasking}
        selectedAPInvoice={selectedAPInvoice}
        apiId={apiId}
        createAPInvoiceLoading={createAPInvoiceLoading}
        updateAPInvoiceLoading={updateAPInvoiceLoading}
        dispatch={dispatch}
        user={user}
        handleCreateAPInvoice={handleCreateAPInvoice}
      />

      <Drawer
        open={showAddressDrawer}
        onClose={() => dispatch({
          type: actionType.SET_MULTIPLE_FIELDS,
          payload: { showAddressDrawer: false, selectedAddressType: '' },
        })}
        width="360"
        destroyOnClose
      >
        <AddressSelector
          title="Vendor Address"
          addressType={selectedAddressType?.split('_')[0]}
          selectedAddressId={vendorAddress?.address_id}
          onAddressChange={(address) => {
            dispatch({
              type: actionType.SET_MULTIPLE_FIELDS,
              payload: {
                vendorAddress: address,
                showAddressDrawer: false,
                apInvoiceTableData: apInvoiceTableData?.map((record) => ({
                  ...record,
                  child_taxes: apInvoiceTableData?.[0]?.product_sku_name ? Helpers.computeTaxation((apInvoiceType === 'ADHOC' ? record?.quantity : record.ap_received_qty * record.offer_price) * (record.discount ? Number(100 - record.discount) / 100 : 1), record.taxInfo, getBillFromAddress(), address?.state)?.tax_info?.child_taxes : [{
                    tax_amount: 0,
                    tax_type_name: '',
                  }],
                })),
              },
            });
          }}
          entityId={selectedSeller?.seller_id || selectedSellerId || selectedGRNForAPInvoice?.seller_id}
          entityType="SELLER"
          seller={selectedTenantSeller}
          tenantId={selectedTenant || selectedGRN?.tenant_id || selectedGRNForAPInvoice?.tenant_id || selectedGRN?.tenant_id}
        />
      </Drawer>
      {
        apInvoiceType === 'MULTIGRN' &&
        <PRZDrawer
          open={openDrawerOfMultipleSelectedGRNs}
          onClose={() => dispatch({
            type: actionType.SET_MULTIPLE_FIELDS,
            payload: { openDrawerOfMultipleSelectedGRNs: false, },
          })}
          width={300}
          title="Selected GRNs"
          headerWidth={260}
        >
          {selectedGRNValue?.map((grn) => (
            <Card
              key={grn?.key}
              onClick={() => window.open(`/purchase/goods-receiving/view/${grn.value}`, '_blank')}
              className="grn-card"
              bodyStyle={{ padding: '4px' }}
            >
              <div className="grn-card__content">
                <span className="grn-card__label">{grn?.label}</span>
              </div>
            </Card>
          ))}
        </PRZDrawer>
      }
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, SellerReducers, GRNReducers, CFV2Reducers, InventoryLocationReducers, VendorRatingReducers, CurrenciesReducers, ExtraChargesReducers, TagReducers, GetPurchaseOrders, GetGRNV2, CreateAPInvoice, UpdateAPInvoice, GetAPInvoice, TaxReducers, GetTallyConfigurations,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  priceMasking: UserReducers.priceMasking,
  currenciesResults: CurrenciesReducers.currenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  cfV2DocAPInvoice: CFV2Reducers.cfV2DocAPInvoice,
  getDocCFV2APInvoiceLoading: CFV2Reducers.getDocCFV2APInvoiceLoading,
  grnV2: GetGRNV2.data,
  getGRNV2Loading: GetGRNV2.loading,
  createTagLoading: TagReducers.createTagLoading,
  createAPInvoiceLoading: CreateAPInvoice.loading,
  updateAPInvoiceLoading: UpdateAPInvoice.loading,
  getAPInvoiceLoading: GetAPInvoice.loading,
  selectedAPInvoice: Array.isArray(GetAPInvoice.data) ? GetAPInvoice.data[0] : GetAPInvoice.data,
  taxesGroup: TaxReducers.taxesGroup,
  getTaxesLoading: TaxReducers.getTaxesLoading,
  sellers: SellerReducers.sellers,
  tallyConfigurations: GetTallyConfigurations.data,
  tallyConfigurationsLoading: GetTallyConfigurations.loading,
});

const mapDispatchToProps = (dispatch) => ({
  getTenantsConfiguration: (tenantId, callback) => dispatch(TenantActions.getTenantsConfiguration(tenantId, callback)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  getCharges: (orgId, entityName, callback) => dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback)),
  getGRNV2: (payload, callback) => dispatch(GetGRNV2.actions.request(payload, callback)),
  createTag: (payload, callback) => dispatch(TagActions.createTag(payload, callback)),
  getAPInvoice: (payload, callback) => dispatch(APInvoiceModule.GetAPInvoice.actions.request(payload, callback)),
  getAPInvoiceSuccess: (payload, callback) => dispatch(APInvoiceModule.GetAPInvoice.actions.success(payload, callback)),
  createAPInvoice: (payload, callback) => dispatch(APInvoiceModule.CreateAPInvoice.actions.request(payload, callback)),
  updateAPInvoice: (payload, callback) => dispatch(APInvoiceModule.UpdateAPInvoice.actions.request(payload, callback)),
  getTaxes: (orgId, page, limit, isActive, isGroup) => dispatch(TaxActions.getTaxes(orgId, page, limit, isActive, isGroup)),
  getSellers: (keyword, tenantId, page, limit, sellerId, callback) => dispatch(SellerActions.getSellers(keyword, tenantId, page, limit, sellerId, callback)),
  getTallyConfigurations: (payload, callback) => dispatch(GetTallyConfigurations.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(AccountPayableInvoiceForm));
