{"name": "procuzy", "version": "1.0.0", "description": "procuzy customer application", "main": "index.jsx", "scripts": {"build:size": "npm run build && npm run size", "size": "node bundle-size-tracker.js", "build": "node --max-old-space-size=4096 pre-build.js && webpack --config webpack.config.build.js --mode production && node post-build.js && node create-header-rule-file.js", "start": "webpack serve --config webpack.config.js --hot --mode development --history-api-fallback --host 0.0.0.0 --port 3500", "dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider && webpack serve --config webpack.config.js --hot --mode development --history-api-fallback --host 0.0.0.0 --port 3500", "uat": "cross-env webpack serve --config webpack.config.js --hot --mode development --history-api-fallback --host 0.0.0.0 --port 3500 --env backend=uat", "prod": "webpack serve --config webpack.config.js --hot --mode development --history-api-fallback --host 0.0.0.0 --port 3500", "lint": "eslint .", "lint:fix": "eslint . --fix", "analyze": "cross-env ANALYZE=true npm run build"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@analytics/segment": "^2.1.0", "@ant-design/icons": "^5.1.0", "@ant-design/plots": "^1.2.5", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@sentry/react": "^9.30.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.80.6", "@yaireo/tagify": "^4.26.5", "analytics": "^0.8.0", "antd": "5.5.0", "antd-img-crop": "^4.12.2", "apexcharts": "3.42.0", "axios": "^0.21.1", "core-js": "^3.39.0", "dayjs": "^1.11.11", "decimal.js": "^10.5.0", "dotenv": "^16.6.1", "firebase": "^9.15.0", "immutability-helper": "^3.1.1", "lottie-react": "^2.4.1", "mixpanel-browser": "^2.47.0", "process": "^0.11.10", "prop-types": "^15.7.2", "react": "^18.2.0", "react-apexcharts": "1.4.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-infinite-scroll-component": "^6.1.0", "react-modal": "^3.16.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^5.3.4", "redux": "^4.2.1", "redux-saga": "^1.2.3", "sass": "^1.89.2", "socket.io-client": "^4.7.5", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.30.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/redux-logger": "^3.0.13", "@vue/preload-webpack-plugin": "^2.0.0", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "babel-loader": "^10.0.0", "babel-plugin-import": "^1.13.8", "brotli-webpack-plugin": "^1.1.0", "buffer": "^6.0.3", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^10.0.0", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "eslint": "^9.30.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-unicorn": "^59.0.1", "file-loader": "^6.2.0", "glob": "^11.0.3", "globals": "^16.3.0", "html-webpack-plugin": "^5.6.3", "ignore-loader": "^0.1.2", "mini-css-extract-plugin": "^2.9.2", "node-polyfill-webpack-plugin": "^4.1.0", "path": "^0.12.7", "path-browserify": "^1.0.1", "redux-logger": "^3.0.6", "resolve-url-loader": "^5.0.0", "sass-loader": "^16.0.5", "scss-loader": "0.0.1", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.3.14", "thread-loader": "^4.0.4", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "url-loader": "^4.1.1", "util": "^0.12.5", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2", "webpack-cleanup-plugin": "^0.5.1", "webpack-cli": "^6.0.1", "webpack-combine-loaders": "^2.0.4", "webpack-dev-server": "^5.2.2", "wrangler": "^4.32.0"}}