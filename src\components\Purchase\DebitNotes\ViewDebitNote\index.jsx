// React & Redux
import React, { Fragment, useEffect, useState } from 'react';
import { withRouter, } from 'react-router-dom';
import { connect } from 'react-redux';

// Ant Design Components
import { Menu } from 'antd';

// Utilities
import { v4 as uuidv4 } from 'uuid';
import 'react-quill/dist/quill.bubble.css';

// Constants & Helpers
import Constants from '@Apis/constants';
import Helpers from '@Apis/helpers';

// Actions
import DNActions from '@Actions/dnActions';
import AttachmentActions from '@Actions/attachmentActions';
import ActivityLogActions from '@Actions/activityLogActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import TallyIntegrationActions from '@Actions/integrations/tallyIntegrationActions';
import BusyIntegrationActions from '../../../../actions/integrations/busyIntegrationAction';

// UI Libraries
import H3Text from '@Uilib/h3Text';

// Components - Common
import ViewPayment from '@Components/Common/ViewPayment';
import ViewLoadingSkull from '../../../Common/ViewLoadingSkull';
import ViewDocRightSection from '@Components/Common/ViewDocRightSection';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';

// Components - Module Specific
import DebitNoteLines from './DebitNoteLines';
import RecordIncomingPayment from '../../../Payments/RecordPayment/RecordIncomingPayment';
import LinkIncomingPayment from '../../../Payments/LinkPayment/LinkIncomingPayment';
import ViewDNLeftSectionHeader from './ViewDNLeftSectionHeader';
import ViewDNLeftSectionDocDetails from './ViewDNLeftSectionDocDetails';
import ViewDebitNoteFooter from './ViewDebitNoteFooter';
import {
  getLinkedDocumentInfoHelper,
  handleFileChangeHelper,
} from './helper';

// Styles
import './style.scss';


/**
 *
 */

function ViewDebitNote({
  user, match, getDNById, getDNByIdLoading, updateDNStatus, updateDNStatusLoading, updateDNWorkflowStep, updateDNWorkflowStepLoading,
  history, deleteDN, deleteDNLoading, callback, getAttachmentById, getAttachmentByIdLoading, updateAttachment, updateAttachmentLoading,
  selectedAttachment, pushDebitNoteToBusy, pushDebitNoteToBusyLoading, downloadDocument, MONEY, priceMasking, isQuickView, selectedDNId, selectedDN, grnData, syncTallyGRN, syncTallyGRNLoading, pullTallyVendorPayment, getDNByIdSuccess, getActivityLogSuccess
}) {

  const [state, setState] = useState({
    dateList: [],
    showCheckout1: false,
    showCheckout2: false,
    showCheckout3: false,
    currentUpdate: '',
    isGetAttachment: false,
    showPayment: false,
    linkPayment: false,
  });

  const updateState = (newState) => {

    setState((oldState) => ({
      ...oldState,
      ...newState,
    }));
  };

  const {
    currentUpdate,
    isGetAttachment,
    showPayment,
    linkPayment,
    cancellationPop,
    cancellationReason,
    fileList,
    showViewPayout,
    selectedPayment,
  } = state;

  useEffect(() => {

    if (selectedDNId) {
      getDNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ).join(','), selectedDNId);
      getActivityLogSuccess(null);
    }

    return () => {
      getDNByIdSuccess(null);
    };
  }, []);

  useEffect(() => {

    const { debitNoteId } = match.params;
    if (user?.tenant_info?.tenant_id) {
      if (window.location.href.includes('/purchase/debit-note/view') && debitNoteId) {
        getDNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ).join(','), debitNoteId);
      }
    }
  }, [user]);

  useEffect(() => {

    if (selectedDN) {
      const currentDN = selectedDN?.data?.debit_notes?.[0];
      updateState({
        fileList: currentDN?.attachments,
      });
    }
  }, [selectedDN]);

  useEffect(() => {

    if (selectedAttachment && isGetAttachment && !getAttachmentByIdLoading && !updateAttachmentLoading) {
      updateState({
        fileList: selectedAttachment,
        isGetAttachment: false,
      });
    }
  }, [selectedAttachment, isGetAttachment, getAttachmentByIdLoading, updateAttachmentLoading]);

  const currentDN = selectedDN?.data?.debit_notes?.[0] || null;

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const isTallyConnected = (selectedDN?.data?.debit_notes[0]?.debit_note_voucher_integration) && (['CONFIRMED'].includes(selectedDN?.data?.debit_notes[0]?.status));

  const fixedMenuBar = user?.side_menu_bar_type === 'FIXED';

  const isVendorOverseas = currentDN?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

  const menu = (
    <Menu>
      {currentDN?.org_currency_info?.org_currency_id && (
        <Menu.Item>
          <H3Text
            text={`Debit Note (${currentDN?.org_currency_info?.currency_code})`}
            onClick={() => downloadDocument({
              url: `${Constants.DN}/download?dn_id=${currentDN?.dn_id}&tenant_id=${currentDN?.tenant_info?.tenant_id}&download_document_in_base_currency=false`,
              document_type: 'DEBIT_NOTE',
              document_number: currentDN?.debit_note_number,
              key: uuidv4(),
            })}
            className="hide__in-mobile"
          />
        </Menu.Item>
      )}
      {(currentDN?.base_currency_info?.org_currency_id && currentDN?.org_currency_info?.org_currency_id !== currentDN?.base_currency_info?.org_currency_id) && (
        <Menu.Item>
          <H3Text
            text={`Debit Note (${currentDN?.base_currency_info?.currency_code})`}
            onClick={() => downloadDocument({
              url: `${Constants.DN}/download?dn_id=${currentDN?.dn_id}&tenant_id=${currentDN?.tenant_info?.tenant_id}&download_document_in_base_currency=true`,
              document_type: 'DEBIT_NOTE',
              document_number: currentDN?.debit_note_number,
              key: uuidv4(),
            })}
            className="hide__in-mobile"
          />
        </Menu.Item>
      )}
    </Menu>
  );

  const handleFileChange = (fileListData) => (
    handleFileChangeHelper({ fileListData, currentDN, updateState, updateAttachment, getAttachmentById, })
  );

  const getLinkedDocumentInfo = () => (
    getLinkedDocumentInfoHelper({ currentDN, state, updateState, priceMasking, })
  );

  return (
    <Fragment>
      <div className={`view-document__wrapper ${!isQuickView ? 'view-document__wrapper-page' : ''}`}>
        {!(getDNByIdLoading)
          ? (
            <div className="ant-row">
              {/* <div className={window.location.href.includes('/purchase/debit-note/view') ? (fixedMenuBar ? 'ant-col-md-16 ant-col-xs-24' : ant-col-md-17 ant-col-xs-24) : 'ant-col-md-24'}> */}

              <div className={`${window.location.href.includes('/purchase/debit-note/view')
                ? (fixedMenuBar ? 'ant-col-md-16' : 'ant-col-md-17')
                : 'ant-col-md-24'} ant-col-xs-24`}>
                <div className={`view-left__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
                  <ViewDNLeftSectionHeader
                    currentDN={currentDN}
                    getDNById={getDNById}
                    selectedDNId={selectedDNId}
                    callback={callback}
                    updateState={updateState}
                    updateDNStatusLoading={updateDNStatusLoading}
                    currentUpdate={currentUpdate}
                    cancellationReason={cancellationReason}
                    cancellationPop={cancellationPop}
                    updateDNStatus={updateDNStatus}
                    deleteDN={deleteDN}
                    deleteDNLoading={deleteDNLoading}
                    pushDebitNoteToBusyLoading={pushDebitNoteToBusyLoading}
                    pushDebitNoteToBusy={pushDebitNoteToBusy}
                    isTallyConnected={isTallyConnected}
                    syncTallyGRN={syncTallyGRN}
                    user={user}
                    downloadDocument={downloadDocument}
                  />
                  <ViewDNLeftSectionDocDetails
                    currentDN={currentDN}
                    user={user}
                    isTallyConnected={isTallyConnected}
                  />
                  <br />
                  <DebitNoteLines
                    dnLines={currentDN?.dn_lines || []}
                    selectedDN={selectedDN}
                    currencyCode={currentDN?.org_currency_info?.currency_code}
                    isVendorOverseas={isVendorOverseas}
                  />
                  <ViewDebitNoteFooter
                    currentDN={currentDN}
                    fileList={fileList}
                    getAttachmentByIdLoading={getAttachmentByIdLoading}
                    updateAttachmentLoading={updateAttachmentLoading}
                    handleFileChange={handleFileChange}
                    isDataMaskingPolicyEnable={isDataMaskingPolicyEnable}
                    isHideCostPrice={isHideCostPrice}
                    MONEY={MONEY}
                    isVendorOverseas={isVendorOverseas}
                    user={user}
                  />
                </div>
              </div>
              {window.location.href.includes('/purchase/debit-note/view') && (
                <ViewDocRightSection
                  entityType='DEBIT_NOTE'
                  entityId={currentDN?.dn_id}
                  MONEY={MONEY}
                  user={user}
                  isQuickView={isQuickView}
                  tagSelectorProps={{
                    isEnabled: false,
                  }}
                  workflowTimeLineProps={{
                    isEnabled: false,
                  }}
                  activityLogProps={{
                    isEnabled: false,
                    entityName: 'Debit Note',
                    entityType: "debit_note"
                  }}
                  linkedDocumentsInfo={getLinkedDocumentInfo()}
                />
              )}
            </div>
          )
          : (
            //  Loader
            <ViewLoadingSkull isQuickView={isQuickView} fixedMenuBar={fixedMenuBar} />
          )}
      </div>

      <PRZDrawer
        open={showPayment}
        width={420}
        onClose={() => updateState({ showPayment: false })}
        destroyOnClose
        headerWidth={375}
        title='Record New Payment'
      >
        <RecordIncomingPayment
          selectedDN={currentDN}
          callback={() => {
            updateState({ showPayment: false });
            getDNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ).join(','), currentDN.dn_id);
          }}
        />
      </PRZDrawer>
      <PRZDrawer
        open={linkPayment}
        width={820}
        onClose={() => updateState({ linkPayment: false })}
        destroyOnClose
        headerWidth={775}
        title='Link Payment'
      >
        <LinkIncomingPayment
          selectedDN={currentDN}
          callback={() => {
            updateState({ linkPayment: false });
            getDNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ).join(','), currentDN.dn_id);
          }}
        />
      </PRZDrawer>
      <PRZDrawer
        open={showViewPayout}
        onClose={() => updateState({ showViewPayout: false })}
        width={880}
        destroyOnClose
        headerWidth={825}
        title={`View Payment #${selectedPayment?.documentId}`}
      >
        <ViewPayment
          selectedPayment={selectedPayment}
          selectedPaymentId={selectedPayment?.documentId}
          paymentType={selectedPayment?.appliedPaymentType}
          screen="debit_note"
          callback={() => {
            updateState({ showViewPayout: false });
            getDNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ).join(','), currentDN.dn_id);
          }}
        />
      </PRZDrawer>

    </Fragment >
  );
};

const mapStateToProps = ({
  UserReducers, GRNReducers, DNReducers, AttachmentReducers, BusyIntegrationReducers, TallyIntegrationReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  getDNByIdLoading: DNReducers.getDNByIdLoading,
  selectedDN: DNReducers.selectedDN,
  updateDNStatusLoading: DNReducers.updateDNStatusLoading,
  updateDNWorkflowStepLoading: DNReducers.updateDNWorkflowStepLoading,
  grnData: GRNReducers.grnData,
  deleteDNLoading: DNReducers.deleteDNLoading,
  selectedAttachment: AttachmentReducers.selectedAttachment,
  getAttachmentByIdLoading: AttachmentReducers.getAttachmentByIdLoading,
  updateAttachmentLoading: AttachmentReducers.updateAttachmentLoading,
  pushDebitNoteToBusyLoading: BusyIntegrationReducers.pushDebitNoteToBusyLoading,
  priceMasking: UserReducers.priceMasking,
  syncTallyGRNLoading: TallyIntegrationReducers.syncTallyGRNLoading,
});

const mapDispatchToProps = (dispatch) => ({
  updateDNStatus: (payload, callback) => dispatch(DNActions.updateDNStatus(payload, callback)),
  deleteDN: (payload, callback) => dispatch(DNActions.deleteDN(payload, callback)),
  updateDNWorkflowStep: (payload, callback) => dispatch(DNActions.updateDNWorkflowStep(payload, callback)),
  getDNById: (tenantId, dnId) => dispatch(DNActions.getDNById(tenantId, dnId)),
  getDNByIdSuccess: (selectedDN) => dispatch(DNActions.getDNByIdSuccess(selectedDN)),
  getAttachmentById: (entityId, entityName, callback) => dispatch(AttachmentActions.getAttachmentById(entityId, entityName, callback)),
  updateAttachment: (payload, callback) => dispatch(AttachmentActions.updateAttachment(payload, callback)),
  getActivityLogSuccess: (activityLog) => dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
  pushDebitNoteToBusy: (payload, callback) => dispatch(BusyIntegrationActions.pushDebitNoteToBusy(payload, callback)),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  pullTallyVendorPayment: (payload, callback) => dispatch(TallyIntegrationActions.pullTallyVendorPayment(payload, callback)),
  syncTallyGRN: (payload, callback) => dispatch(TallyIntegrationActions.syncTallyGRN(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewDebitNote));
