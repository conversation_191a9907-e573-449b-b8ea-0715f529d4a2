// React and core libraries
import React, { Fragment, Suspense, useState, useEffect } from 'react';

// React Router
import { Switch, Route } from 'react-router-dom';

// Redux
import { connect } from 'react-redux';

// Firebase
import { ref, onValue } from 'firebase/database';
import { rtdb } from './apis/firebase';

// Third-party libraries
import { message } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

// Ant Design global reset
import 'antd/dist/reset.css';

// Day.js plugins
dayjs.extend(require('dayjs/plugin/utc'));
dayjs.extend(require('dayjs/plugin/isSameOrBefore'));
dayjs.extend(require('dayjs/plugin/quarterOfYear'));
dayjs.extend(require('dayjs/plugin/customParseFormat'));

// Routes and route guards
import Routes from '@Routes';
import ProtectedRoute from '@Routes/protectedRoute';

// Redux Actions
import UserActions from '@Actions/userActions';

// Components
import Loader from '@Components/Common/Loader';
import H3Text from '@Uilib/h3Text';
import DevelopmentTool from './components/DevelopmentTool/DevelopmentTool';
import RefreshRequiredBanner from './components/RefreshRequiredBanner';
import ServerDownBanner from './components/ServerDownBanner';
import DocumentDownload from './components/DocumentDownload';

// Utilities, Constants, Configs
import { toISTDate, mobileViewBreakPoint } from '@Apis/constants';
import releaseConfig from '../config.json';
import { getRouteMappingData } from './config/routeMapping';

// Global setups or side-effect imports
import './apis/global';
import * as FavoriteEntityModule from './modules/favoriteEntity';
import { GET_KEYBOARD_SHORTCUTS } from './modules/keyBoardShortcut';
import * as DevRevModule from '@Modules/devRevUserAccessToken';

const App = ({
  user, isAuthenticated, getUserProfile, logoutUserLoading, downloadQueue, getKeyBoardShortcuts, getFavoriteEntity, getDevRevAccessToken, devRevToken,
}) => {
  const [offtime, setOfftime] = useState(false);
  const [offtimeMsg, setOfftimeMsg] = useState('Website Down');
  const [k, setK] = useState(0);
  const [refreshRequired, setRefreshRequired] = useState(false);
  const [timer, setTimer] = useState(60);
  const [routeMapping, setRouteMapping] = useState({});
  const [differenceInDays, setDifferenceInDays] = useState(0);
  const [showExpiryNotice, setShowExpiryNotice] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= mobileViewBreakPoint);

  const cfBranch = [
    'uat.elephant-org-main.pages.dev',
    'uat.app.procuzy.com',
    'app.procuzy.com',
  ].some(domain => window.location.href.includes(domain));

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= mobileViewBreakPoint);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      getUserProfile();
      getKeyBoardShortcuts();
      getFavoriteEntity();
    }

    const envRef = ref(rtdb, releaseConfig?.environment);
    const notificationRef = ref(rtdb, `${releaseConfig?.environment}/notification`);

    onValue(envRef, (snapshot) => {
      const data = snapshot.val();
      setOfftime(data.offtime);
      setOfftimeMsg(data.notes);
    });

    onValue(notificationRef, (snapshot) => {
      const data = snapshot.val();
      if (k) {
        message.info(data);
      } else {
        setK(1);
      }
    });

    // Firebase version check
    const fetchFirebaseData = () => {
      if (releaseConfig?.currentVersion && !window.location.origin.includes('localhost') && releaseConfig?.environment !== 'dev') {
        const versionRef = ref(rtdb, `${releaseConfig?.environment}/minimum_version`);

        onValue(versionRef, (snapshot) => {
          const data = snapshot.val();
          if (data !== releaseConfig?.currentVersion) {
            setRefreshRequired(true);
            let _timer = 60;
            const intervalId = setInterval(() => {
              if (_timer > 0) {
                _timer -= 1;
                setTimer(_timer);
              } else {
                clearInterval(intervalId);
                window.location.reload(true);
              }
            }, 1000);
          } else {
            setRefreshRequired(false);
          }
        });
      }
    };

    fetchFirebaseData();
    localStorage.setItem('user_menu_config', JSON.stringify('FIXED'));
  }, []);

  useEffect(() => {
    if (cfBranch && !devRevToken?.access_token) {
      getDevRevAccessToken();
    }
  }, [user]);

  useEffect(() => {
    if (user?.tenant_info) {
      const routeMappingData = getRouteMappingData(user.tenant_info);
      setRouteMapping(routeMappingData);

      const subscriptionEndDate = user?.tenant_info?.global_config?.settings?.next_renewal_date;
      if (subscriptionEndDate) calculateSubscriptionExpiry(subscriptionEndDate);
    }
  }, [user?.tenant_info]);

  // ---- Utility: safe script loader ----
  const loadScriptOnce = (id, src, onLoad) => {
    if (document.getElementById(id)) {
      if (onLoad) onLoad();
      return;
    }
    const script = document.createElement('script');
    script.id = id;
    script.src = src;
    script.async = true;
    script.onload = onLoad;
    document.body.appendChild(script);
  };

  useEffect(() => {
    if (!user) return;

    // Intercom
    window.Intercom = function () {
      (window.Intercom.q = window.Intercom.q || []).push(arguments);
    };
    loadScriptOnce('intercom-sdk', 'https://widget.intercom.io/widget/zhm1nuf9', () => {
      window.Intercom('boot', {
        app_id: 'zhm1nuf9',
        hide_default_launcher: true,
      });
    });

    // UserGuiding
    loadScriptOnce('userguiding-sdk', 'https://static.userguiding.com/media/user-guiding-9BM85100WGGID-embedded.js');

    // Featurebase - Only load if on UAT domain
    if (cfBranch && user.feature_base_user_hash) {
      loadScriptOnce('featurebase-sdk', 'https://do.featurebase.app/js/sdk.js', () => {
        if (typeof globalThis.Featurebase !== 'function') {
          globalThis.Featurebase = function () {
            (globalThis.Featurebase.q = globalThis.Featurebase.q || []).push(arguments);
          };
        }

        // Boot Featurebase messenger with configuration
        globalThis.Featurebase('boot', {
          appId: '64ff90384efeba2edb753715',
          email: user?.email,
          userId: user?.username,
          createdAt: user?.createdAt || new Date().toISOString(),
          theme: 'light',
          language: 'en',
          userHash: user.feature_base_user_hash,
          hideDefaultLauncher: true,
        });
        globalThis.Featurebase('init_changelog_widget', {
          organization: 'procuzy', // Replace this with your organization name, copy-paste the subdomain part from your Featurebase workspace url (e.g. https://*yourorg*.featurebase.app)
          // dropdown: {
          //   enabled: true, // Add this to enable the dropdown view of the changelog
          //   placement: 'right', // Add this to change the placement of the dropdown
          // },
          popup: {
            enabled: true, // Add this to enable the popup view of the changelog
            usersName: `${user?.first_name?.trim()} ${user?.last_name?.trim()}`?.trim(), // This will show the user's name in the popup as a greeting
            autoOpenForNewUpdates: true, // This will open the popup for new updates
          },
          // category: [], // Filter results by changelog category, add category names in array, the names are case-sensitive
          theme: 'light', // Choose between dark or light theme
          locale: 'en', // Change the language, view all available languages from https://help.featurebase.app/en/articles/8879098-using-featurebase-in-my-language
        });
      });
    }

    // UserPilot
    window.userpilotSettings = { token: 'NX-18d45351' };
    loadScriptOnce('userpilot-sdk', 'https://js.userpilot.io/sdk/latest.js');
  }, [user]);

  // ---- DevRev Plug SDK ----
  useEffect(() => {
    if (!cfBranch) return;
    if (!devRevToken?.access_token) return;

    loadScriptOnce('devrev-plug-sdk', 'https://plug-platform.devrev.ai/static/plug.js', () => {
      if (!window.plugSDK) return;

      if (!window.plugSDKInitialized) {
        // First time init
        window.plugSDK.init({
          app_id: 'DvRvStPZG9uOmNvcmU6ZHZydi1pbi0xOmRldm8vMnk5OTl5T2pSUjpwbHVnX3NldHRpbmcvMV9ffHxfXzIwMjUtMDgtMTIgMDk6MDU6MTIuMjA2NTU1MTIyICswMDAwIFVUQw==xlxendsDvRv',
          session_token: devRevToken.access_token,
          widget_alignment: 'left',
          enable_default_launcher: false,
          custom_launcher_selector: '#widget-placement',
        });
        window.plugSDKInitialized = true;
      } else {
        // Update token if SDK already running
        try {
          if (window.plugSDK.updateSessionToken) {
            window.plugSDK.updateSessionToken(devRevToken.access_token);
          }
        } catch (e) {
          console.warn('DevRev: updateSessionToken failed', e);
        }
      }
    });
  }, [cfBranch, devRevToken?.access_token]);

  const calculateSubscriptionExpiry = (endDate) => {
    const providedEndDate = new Date(endDate);
    const currentDate = new Date();
    const differenceInMilliseconds = providedEndDate - currentDate;
    const daysDifference = Math.floor(differenceInMilliseconds / (1000 * 60 * 60 * 24));
    setDifferenceInDays(daysDifference);

    const isNearExpiry = daysDifference > -30 && daysDifference <= 15;
    localStorage.setItem('expiry_notification', isNearExpiry ? 'true' : 'false');
  };

  const renderRoutes = () => Routes.map((item) => {
    const {
      path, exact, component, isPrivate,
    } = item;
    const routeProps = {
      path,
      exact,
      key: path,
      component,
      isRestricted: routeMapping[item.path]?.isRestricted || false,
      moduleName: routeMapping[item.path]?.moduleName || 'This Feature',
      isForm: routeMapping[item.path]?.isForm,
    };
    return isPrivate ? <ProtectedRoute {...routeProps} /> : <Route {...routeProps} />;
  });

  const renderExpiryMessage = () => {
    if (differenceInDays > 0 && differenceInDays <= 15) {
      return `Your account is due for renewal on ${toISTDate(user?.tenant_info?.global_config?.settings?.next_renewal_date).format('MMM DD, YYYY')}. Please renew your subscription to enjoy uninterrupted services.`;
    }
    if (differenceInDays === 0) {
      return 'Your account is due for renewal today. Please renew your subscription to enjoy uninterrupted services.';
    }
    if (differenceInDays > -30 && differenceInDays < 0) {
      return `Your account was due for renewal on ${toISTDate(user?.tenant_info?.global_config?.settings?.next_renewal_date).format('MMM DD, YYYY')}. Please renew your subscription immediately to enjoy uninterrupted service.`;
    }
    return null;
  };

  return (
    <Fragment>
      <DevelopmentTool />
      {
        user?.tenant_info && (differenceInDays > -30 && differenceInDays < 15) && showExpiryNotice && (
          <div className="refresh-required-wrapper__outer">
            <div className="refresh-required-wrapper" style={{ position: isMobile ? 'relative' : '' }}>
              <H3Text
                text={<div>{renderExpiryMessage()}</div>}
                className="refresh-required__text"
              />
              <div style={{ display: 'flex', gap: '10px', alignItems: 'center', cursor: 'pointer' }}>
                <div className="refresh-required__button" onClick={() => window.Intercom('showNewMessage')}>Contact Us</div>
              </div>
              {
                isMobile && (
                  <CloseOutlined
                    style={{ position: 'absolute', top: '10px', right: '10px', fontSize: '16px', cursor: 'pointer' }}
                    onClick={() => setShowExpiryNotice(false)}
                  />
                )
              }
            </div>
          </div>
        )
      }
      {refreshRequired && <RefreshRequiredBanner timer={timer} />}

      {(offtime && !(localStorage?.getItem('offtime') === 'false')) ? (
        <ServerDownBanner offtimeMsg={offtimeMsg} />
      ) : (
        <Suspense fallback={<Loader />}>
          <Switch>{renderRoutes()}</Switch>
          {logoutUserLoading && <Loader />}
        </Suspense>
      )}
      <DocumentDownload downloadQueue={downloadQueue} />
      <div id="widget-placement" className="devrev-wrapper"></div>
    </Fragment>
  );
};

const mapStateToProps = ({ UserReducers, AnalyticsReducers, getDevRevAccessToken }) => ({
  user: UserReducers.user,
  isAuthenticated: UserReducers.isAuthenticated,
  environment: UserReducers.environment,
  logoutUserLoading: UserReducers.logoutUserLoading,
  downloadQueue: AnalyticsReducers.downloadQueue,
  org: UserReducers.org,
  devRevToken: getDevRevAccessToken.data,
});

const mapDispatchToProps = (dispatch) => ({
  getUserProfile: () => dispatch(UserActions.getUserProfile()),
  getKeyBoardShortcuts: () => dispatch(GET_KEYBOARD_SHORTCUTS.actions.request()),
  getFavoriteEntity: (payload, callback) => dispatch(FavoriteEntityModule.GetFavoriteEntity.actions.request(payload, callback)),
  getDevRevAccessToken: () => dispatch(DevRevModule.getDevRevAccessToken.actions.request()),
});

export default connect(mapStateToProps, mapDispatchToProps)(App);
