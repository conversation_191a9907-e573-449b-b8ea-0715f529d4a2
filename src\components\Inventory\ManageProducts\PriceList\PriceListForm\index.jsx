import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Select, DatePicker, Radio, Popover, Upload, Spin, notification,
  Tabs,
  Button,
  Table,
  Drawer,
  Popconfirm,
} from 'antd';
import {
  QuestionCircleOutlined, CheckCircleFilled, DownloadOutlined, UploadOutlined, LoadingOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import { faCircleCheck, faLightbulb, faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PriceListActions from '@Actions/inventory/priceListActions';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import TenantSelector from '../../../../Common/Selector/TenantSelector';
import H3Image from '@Uilib/h3Image';
import './style.scss';
import Helpers from '@Apis/helpers';
import { cdnUrl } from "@Utils/cdnHelper";
import SelectCustomers from '../SelectCustomers';
import PRZButton from '../../../../Common/UI/PRZButton';
import PRZSelect from '../../../../Common/UI/PRZSelect';

const { RangePicker } = DatePicker;
const dateFormat = 'DD/MM/YYYY';
const { TabPane } = Tabs;

const PriceListForm = ({
  user, createPriceList, getCustomPriceList, updatePriceList, createPriceListLoading, getCustomPriceListLoading,
  updatePriceListLoading, callback, getPriceListById, priceListId, selectedTenantIds, getPriceListByIdLoading, getExistingPriceList, getExistingPriceListLoading,
}) => {
  const [selectedTenant, setSelectedTenant] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState([]);
  const [selectedState, setSelectedState] = useState([]);
  const [priceListName, setPriceListName] = useState('');
  const [priceListType, setPriceListType] = useState('');
  const [description, setDescription] = useState('');
  const [pricingRule, setPricingRule] = useState('');
  const [priceListPercent, setPriceListPercent] = useState('');
  const [pricingScheme, setPricingScheme] = useState('');
  const [validity, setValidity] = useState([dayjs(dayjs(), dateFormat), dayjs(dayjs().add(1, 'month'), dateFormat)]);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [fileListData, setFileListData] = useState([]);
  const [activeTab, setActiveTab] = useState('1');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [tableDataForCustomers, setTableDataForCustomers] = useState([]);

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  const handleDelete = (key) => {
    const newTableData = tableDataForCustomers?.filter((item) => item.key !== key);
    setTableDataForCustomers(newTableData);
  };

  const columnsForSelectCustomers = [
    {
      title: 'CUSTOMER',
      key: 'customer_name',
      width: 220,
      render: (text, record) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '5px',
          }}
        >
          <H3Text
            text={(
              <React.Fragment>
                {record?.customer_info?.internal_cust_code || '-'}
              </React.Fragment>
            )}
            style={{ color: '#2D7DF7', fontWeight: '500' }}
          />
          <H3Text
            text={(
              <React.Fragment>
                {record?.customer_info?.customer_name ? record?.customer_info?.customer_name : '-'}
              </React.Fragment>
            )}
            style={{ fontWeight: '500' }}
          />
        </div>
      ),
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: 'CUSTOMER TYPE',
      key: 'customer_code',
      width: 100,
      render: (text, record) => `${record?.customer_info?.customer_type
        ? record?.customer_info?.customer_type
        : '-'
        }`,
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: 'SALES MANAGER',
      key: 'sales_manager',
      width: 120,
      render: (text, record) => (
        <React.Fragment>
          {record?.customer_info?.account_manager_info?.account_manager_id ? (`${record?.customer_info?.account_manager_info?.first_name} ${record?.customer_info?.account_manager_info?.last_name}`) : '-'}
        </React.Fragment>
      ),
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: '',
      fixed: 'right',
      width: 40,
      render: (text, record) => (
        <Popconfirm title="Are you sure you want to remove this item?" onConfirm={() => handleDelete(record?.key)} placement='topRight'>
          <div className="delete-line-button__new" style={{ marginLeft: 'auto' }}>
            <FontAwesomeIcon icon={faTrashCan} />
          </div>
        </Popconfirm>
      ),
    },
  ];

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setFileList([file]);
      return false;
    },
    onChange: (e) => {
      const { file } = e;
      const reader = new FileReader();
      reader.onload = (evt) => {
        const bstr = evt.target.result;
        const wb = XLSXRead(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSXUtils.sheet_to_json(ws, { header: 1 });
        const columnsToFind = ['INTERNAL SKU CODE', 'SKU NAME', 'SKU ID', 'DISCOUNT', 'IS RATE INCLUSIVE OF TAX', 'PRICE LIST RATE', 'START QUANTITY', 'END QUANTITY'];
        const columnIndices = {};
        data[0].forEach((columnName, index) => {
          if (columnsToFind.includes(columnName)) {
            columnIndices[columnName] = index;
          }
        });
        const copyFileData = data?.map((item) => ({
          internal_sku_code: item?.[columnIndices['INTERNAL SKU CODE']],
          product_sku_name: item?.[columnIndices['SKU NAME']],
          product_sku_id: item?.[columnIndices['SKU ID']],
          price_list_amount: Number(item?.[columnIndices['PRICE LIST RATE']] || 0),
          discount_percentage: Number(item?.[columnIndices.DISCOUNT] || 0),
          is_inclusive_of_tax: Boolean(item?.[columnIndices['IS RATE INCLUSIVE OF TAX']] || false),
          start_quantity: Number(item?.[columnIndices['START QUANTITY']] || 0),
          end_quantity: Number(item?.[columnIndices['END QUANTITY']] || 0),
        }))?.slice(1);
        setFileListData(copyFileData?.filter((item) => item?.internal_sku_code));
      };
      reader.readAsBinaryString(file);
    },
    fileList,
    disabled: createPriceListLoading || updatePriceListLoading,
  };

  useEffect(() => {
    setPriceListType('ALL_ITEMS');
    setPricingRule('MARK_UP_PERCENTAGE');
    setPricingScheme('UNIT_PRICING');
    if (priceListId) {
      getPriceListById((selectedTenantIds || Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.READ)?.join(',')), priceListId, true, (priceListData) => {
        setPriceListName(priceListData?.price_list_name);
        setSelectedTenant(priceListData?.tenant_ids);
        setSelectedState(priceListData?.applicable_state);
        setSelectedCustomer(priceListData?.customer_ids);
        setPriceListType(priceListData?.price_list_type);
        setDescription(priceListData?.remarks);
        setPricingRule(priceListData?.pricing_rule);
        setPriceListPercent(priceListData?.pricing_rule_percentage);
        setPricingScheme(priceListData?.pricing_scheme);
        setValidity([dayjs(priceListData?.valid_from), dayjs(priceListData?.valid_to)]);
        setTableDataForCustomers(priceListData?.customer_details?.map((item) => ({
          key: item?.customer_id,
          customer_info: item,
          customer_id: item?.customer_id,
        })));
      });
    }
  }, []);

  const isDataValid = () => {
    let isValid = true;
    if (priceListType === 'ALL_ITEMS') {
      if (!priceListPercent || !pricingRule) {
        isValid = false;
      }
    } else if (!priceListId && fileListData?.length < 1) {
      isValid = false;
      notification.error({
        message: 'Import the XLS file that you\'ve exported and updated with the customised rates to update the price list.',
        placement: 'top',
        duration: 4,
      });
    }

    return isValid;
  };

  const handleAddPriceList = () => {
    setFormSubmitted(true);
    const allCustomers = tableDataForCustomers?.map((item) => item?.customer_id) || [];

    if (priceListName && isDataValid() && validity[0] && validity[1]) {
      const payload = {
        price_list_name: priceListName,
        tenant_ids: selectedTenant,
        valid_from: dayjs(validity[0])?.startOf('day').valueOf(),
        valid_to: dayjs(validity[1])?.endOf('day').valueOf(),
        customer_ids: allCustomers,
        applicable_state: selectedState,
        price_list_type: priceListType,
        pricing_rule: priceListType === 'ALL_ITEMS' ? pricingRule : null,
        pricing_rule_percentage: priceListType === 'ALL_ITEMS' ? Number(priceListPercent) : null,
        remarks: description,
        pricing_scheme: priceListType === 'SELECTED_ITEMS' ? pricingScheme : null,
        price_list_products: priceListType === 'SELECTED_ITEMS' ? fileListData : [],
      };

      if (priceListId) {
        payload.price_list_id = priceListId;
        updatePriceList(payload, () => {
          callback();
        });
      } else {
        createPriceList(payload, () => {
          callback();
        });
      }
    }
  };
  const content = (
    <div>
      <div className="ant-row">
        <div className="ant-col-md-24">
          Use Slab Pricing to configure the unit price of the item to be based on the quantity of items sold.
        </div>
        <div className="ant-col-md-24">
          <b>For Example</b>
          , when less than 10 items are sold or purchased, you can set each item's price at ₹10 and when more than 10 items are sold or purchased, you can set each item's price at ₹5. So, for 5 items, the total price will be ₹50, while for 15 items, the total price will be ₹75.
        </div>
        <div className="ant-col-md-24"><b>Note:</b></div>
        <ul>
          <li>If you don’t enter the End Quantity for the last range and the item quantity is greater than the start quantity of the last range, then the custom rate of the last range will be applied.</li>
          <li>
            If you don’t enter a custom range, then the default item rate will be applied for that quantity range.
          </li>
        </ul>
      </div>
    </div>
  );

  return (getPriceListByIdLoading)
    ? (
      <React.Fragment>
        <div className="view-price-list__loading">
          <div className="loadingBlock view-price-list__loading-l1" />
          <div className="loadingBlock view-price-list__loading-l2" />
          <div className="loadingBlock view-price-list__loading-l3" />
          <div className="loadingBlock view-price-list__loading-l4" />
          <div className="loadingBlock view-price-list__loading-l5" />
        </div>
      </React.Fragment>
    )
    : (
      <div className="create-price-list__wrapper">
        <Drawer
          title="Report Details"
          placement="right"
          onClose={() => setDrawerVisible(false)}
          visible={drawerVisible}
          width={720}
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header">
              <H3Text text="Add customers" className="custom-drawer__title" />
              <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => setDrawerVisible(false)} />
            </div>
            <SelectCustomers
              setTableDataForCustomers={setTableDataForCustomers}
              setDrawerVisible={setDrawerVisible}
              SelectCustomersForPriceList={tableDataForCustomers?.map((item) => item?.customer_id)}
            />
          </div>
        </Drawer>
        <div className="create-price-list__wrapper-inner">
          <div className="ant-row">
            <div className="ant-col-md-12">
              <div className="create-price-list__input-row orgInputContainer">
                <H3Text required text="Price List Name" className="create-price-list__input-row__label" />
                <H3FormInput
                  name="Price List Name"
                  type="text"
                  containerClassName="create-price-list__input-row__input"
                  labelClassName="orgFormLabel"
                  inputClassName="orgFormInput input"
                  placeholder="eg. Q3 South Price List"
                  disabled={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
                  onChange={(e) => setPriceListName(e.target.value)}
                  value={priceListName}
                  required
                  showError={formSubmitted && !priceListName}
                />
              </div>
            </div>
            <div className="ant-col-md-12">

              <div className="create-price-list__input-row" style={{ marginBottom: '12px' }}>
                <H3Text required text="Locations" className="create-price-list__input-row__label" />
                <TenantSelector
                  selectedTenant={selectedTenant}
                  noDropdownAlign
                  showSearch
                  onChange={(value) => {
                    setSelectedTenant(value);
                  }}
                  hideTitle
                  style={{
                    border: '1px solid rgba(68, 130, 218, 0.2)',
                    borderRadius: '2px',
                    height: '32px',
                    width: '100%',
                    fontSize: '13px',
                  }}
                  containerClassName="create-price-list__input-row__input"
                  placeholder="Select Business Unit"
                  isMultiple
                  // includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.QUALITY_RULES, Helpers.permissionTypes.READ)}
                  showError={formSubmitted && selectedTenant?.length < 1}
                  disabled={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
                />
              </div>

            </div>
            {!priceListId && (
              <div className="ant-col-md-24">
                <div className="create-price-list__input-row orgInputContainer">
                  <H3Text
                    required
                    text="Are you creating a price list of all items or specific items?"
                    className="create-price-list__input-row__label"
                  />
                  <div className="create-price-list__input-row__input">
                    <div className="ant-row" style={{ width: 'calc(100% + 10px)' }}>
                      <div className="ant-col-md-12">
                        <div
                          className="create-price-list__input-radio"
                          onClick={() => {
                            if (!priceListId) {
                              setPriceListType('ALL_ITEMS');
                            }
                          }}
                        >
                          <div
                            className={`create-price-list__input-radio-icon ${priceListId && 'disable-icon'}`}
                            style={{ color: priceListType === 'ALL_ITEMS' ? '#22C55E' : '#bbbbbb' }}
                          >
                            <FontAwesomeIcon icon={faCircleCheck} />
                          </div>
                          <div className="create-price-list__input-radio-detail">
                            <div className="create-price-list__input-radio-detail-upper">All Items</div>
                            <div className="create-price-list__input-radio-detail-bottom">
                              Mark up or mark down the rates
                              of all items
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div
                          className="create-price-list__input-radio"
                          onClick={() => {
                            if (!priceListId) {
                              setPriceListType('SELECTED_ITEMS');
                            }
                          }}
                        >
                          <div
                            className={`create-price-list__input-radio-icon ${priceListId && 'disable-icon'}`}
                            style={{ color: priceListType === 'SELECTED_ITEMS' ? '#22C55E' : '#bbbbbb' }}
                          >
                            <FontAwesomeIcon icon={faCircleCheck} />
                          </div>
                          <div className="create-price-list__input-radio-detail">
                            <div className="create-price-list__input-radio-detail-upper">Individual Items</div>
                            <div className="create-price-list__input-radio-detail-bottom">
                              Customise the rate of each
                              item
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {priceListType === 'ALL_ITEMS' && (
              <div className="ant-col-md-12">
                <div className="create-price-list__input-row orgInputContainer">
                  <H3Text required text="Markup/Markdown Percentage" className="create-price-list__input-row__label" />
                  <div className="create-price-list__rule-wrapper">
                    <PRZSelect
                      value={pricingRule}
                      defaultValue={pricingRule}
                      onChange={(value) => {
                        setPricingRule(value);
                      }}
                      style={{
                        width: '100px',
                        fontSize: '13px',
                        backgroundColor: '#f8f8f8',
                      }}
                      options={[
                        {
                          value: 'MARK_UP_PERCENTAGE',
                          label: 'Markup',
                        },
                        {
                          value: 'MARK_DOWN_PERCENTAGE',
                          label: 'Markdown',
                        },
                      ]}
                      showError={formSubmitted && priceListType === 'ALL_ITEMS' && !(priceListPercent > 0)}
                      errorName="*Please enter percentage"
                      errorClassName="input-error"
                    />
                    <div className="create-price-list__rule-middle">
                      <H3FormInput
                        name="percentage"
                        type="number"
                        containerClassName="create-price-list__input-row__input"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput input"
                        placeholder=""
                        disabled={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
                        onChange={(e) => setPriceListPercent(e.target.value)}
                        value={priceListPercent}
                        required
                      />
                    </div>
                    <div className="create-price-list__rule-right">%</div>
                  </div>
                </div>
              </div>
            )}
            {priceListType === 'SELECTED_ITEMS' && (
              <div className="ant-col-md-12">
                <div className="create-price-list__input-row">
                  <H3Text required text="Select the pricing scheme" className="create-price-list__input-row__label" />
                  <div className="create-price-list__input-row__input" style={{ display: 'flex', alignItems: 'center' }}>
                    <div className="create-price-list__input-radio-group-wrapper">
                      <Radio.Group
                        disabled={createPriceListLoading || updatePriceListLoading || priceListId || getPriceListByIdLoading}
                        onChange={(event) => {
                          if (!priceListId) {
                            setPricingScheme(event.target.value);
                          }
                        }}
                        value={pricingScheme}
                      >
                        <Radio value="UNIT_PRICING">Unit Pricing </Radio>
                        <Radio value="VOLUME_PRICING">Slab Pricing</Radio>
                      </Radio.Group>
                    </div>
                    <div className="create-price-list__input-row-ques-icon">
                      <Popover
                        placement="right"
                        content={content}
                        title="Slab Pricing"
                        overlayStyle={{
                          width: '485px',
                        }}
                        trigger="click"
                      >
                        <QuestionCircleOutlined />
                      </Popover>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="ant-col-md-12">
              <div className="create-price-list__input-row orgInputContainer">
                <H3Text required text="Price List Validity" className="create-price-list__input-row__label" />
                <div className="create-price-list__input-row__input">
                  <RangePicker
                    value={validity}
                    onChange={(date) => {
                      setValidity(date);
                    }}
                    allowClear={false}
                    format="DD-MM-YYYY"
                    style={{
                      border: '1px solid rgba(68, 130, 218, 0.2)',
                      borderColor: formSubmitted && !validity ? 'red' : 'rgba(68, 130, 218, 0.2)',
                      borderRadius: '2px',
                      height: '32px',
                      padding: '1px 10px',
                      width: '100%',
                      background: 'white',
                    }}
                    disabled={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
                  />
                </div>
              </div>
            </div>

            {priceListType === 'SELECTED_ITEMS' && (
              <div className="ant-col-md-24">
                <div className="create-price-list__info-wrapper">
                  <div className="create-price-list__info-title">
                    <FontAwesomeIcon icon={faLightbulb} />
                    &nbsp;
                    Follow below steps to upload prices in bulk
                  </div>
                  <div className="create-price-list__inner-info-wrapper">
                    <div className="create-price-list__inner-info-title">1. Export items as XLS file</div>
                    <div className="create-price-list__inner-info">Export all your items in an XLS file, customise the rates, and import the file into Procuzy.</div>
                    <div
                      className={`create-price-list__inner-ixp-btn ${(!selectedTenant || !priceListName) ? 'disabled' : ''}`}
                      style={{ cursor: (!selectedTenant || !priceListName) ? 'not-allowed' : 'pointer' }}
                      onClick={() => {
                        if (selectedTenant && priceListName && !getCustomPriceListLoading && !getExistingPriceListLoading) {
                          if (priceListId) {
                            getExistingPriceList(user?.tenant_info?.org_id, priceListId, true);
                          } else {
                            getCustomPriceList(user?.tenant_info?.org_id, pricingScheme);
                          }
                        }
                      }}
                    >
                      <div style={{ marginRight: '7px' }}><UploadOutlined /></div>
                      <div>{!(getCustomPriceListLoading || getExistingPriceListLoading) ? 'Export Items' : <LoadingOutlined style={{ marginLeft: '30px' }} />}</div>
                    </div>
                  </div>

                  <div className="create-price-list__inner-info-wrapper">
                    <div className="create-price-list__inner-info-title">2. Import items as XLS file</div>
                    <div className="create-price-list__inner-info">
                      Import the XLS file that you've exported and updated with the customised rates to update the price list.
                    </div>
                    <div className="create-price-list__inner-info">
                      Once you import the file, the existing items and its rates in this price list will be replaced with the data in the import file.
                    </div>

                    <Upload {...uploadProps}>
                      <div className="create-price-list__inner-ixp-btn">
                        <div style={{ marginRight: '7px' }}><DownloadOutlined /></div>
                        <div>Import Items</div>
                      </div>
                    </Upload>
                  </div>
                </div>
              </div>
            )}
            <div className="ant-col-md-24">
              <div className="create-price-list__input-row orgInputContainer additional-remark-css" style={{ marginBottom: '0px' }}>
                <H3Text text="Additional Remarks" className="create-price-list__input-row__label" />
                <div className="create-price-list__input-row__input ">
                  <textarea
                    className="text-area__pr"
                    rows="2"
                    cols="50"
                    placeholder="Enter the description here..."
                    onChange={(event) => {
                      setDescription(event.target.value);
                    }}
                    value={description}
                    disabled={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
                  />
                </div>
              </div>
            </div>
            <div className="ant-col-md-24">
              <div style={{ padding: '5px' }}>
                <Tabs activeKey={activeTab} onChange={handleTabChange}>
                  <TabPane
                    tab="Customers"
                    key="1"
                  >
                    {selectedTenant?.length > 0 && (
                      <span
                        className="add-customer-button add-customer-button-text"
                        onClick={() => setDrawerVisible(true)}
                      >
                        + Add Customers
                      </span>
                    )}
                    <Table
                      columns={columnsForSelectCustomers}
                      scroll={{ y: 195 }}
                      pagination={false}
                      bordered={false}
                      size="small"
                      dataSource={tableDataForCustomers}
                      title={() => (
                        <H3Text
                          text={`${tableDataForCustomers && tableDataForCustomers?.length ? tableDataForCustomers?.length : '0'
                            } Total Customers`}
                          className=""
                        />
                      )}

                    />
                  </TabPane>
                </Tabs>
              </div>
            </div>

          </div>
        </div>

        <div className="custom-drawer__footer" style={{ width: '1030px' }}>
          <div className="ant-col-md-6" style={{ marginTop: '0.5rem' }}>
            <PRZButton
              onClick={() => handleAddPriceList()}
              isLoading={createPriceListLoading || updatePriceListLoading || getPriceListByIdLoading}
              buttonStyle={{ width: "150px" }}
            >
              <div>{priceListId ? 'Update Price List' : 'Create Price List'}</div>
            </PRZButton>
          </div>
        </div>
      </div >
    );
};

const mapStateToProps = ({
  TenantReducers, PriceListReducers, UserReducers,
}) => ({
  user: UserReducers.user,
  users: TenantReducers.users,
  updatePriceListLoading: PriceListReducers.updatePriceListLoading,
  createPriceListLoading: PriceListReducers.createPriceListLoading,
  getPriceListByIdLoading: PriceListReducers.getPriceListByIdLoading,
  getCustomPriceListLoading: PriceListReducers.getCustomPriceListLoading,
  getExistingPriceListLoading: PriceListReducers.getExistingPriceListLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getCustomPriceList: (orgId, sampleType) => dispatch(
    PriceListActions.getCustomPriceList(orgId, sampleType),
  ),
  getExistingPriceList: (orgId, priceListId, includeOtherProducts) => dispatch(
    PriceListActions.getExistingPriceList(orgId, priceListId, includeOtherProducts),
  ),
  getPriceListById: (tenantId, priceListId, isListScreen, callback) => dispatch(PriceListActions.getPriceListById(tenantId, priceListId, isListScreen, callback)),
  updatePriceList: (payload, callback) => dispatch(
    PriceListActions.updatePriceList(payload, callback),
  ),
  createPriceList: (payload, callback) => dispatch(
    PriceListActions.createPriceList(payload, callback),
  ),
});

PriceListForm.propTypes = {
  user: PropTypes.any,
  selectedPriceList: PropTypes.any,
  getCustomPriceList: PropTypes.func,
  updatePriceList: PropTypes.func,
  createPriceList: PropTypes.func,
  getCustomPriceListLoading: PropTypes.bool,
  createPriceListLoading: PropTypes.bool,
  updatePriceListLoading: PropTypes.bool,
  callback: PropTypes.func,
  getPriceListById: PropTypes.func,
  priceListId: PropTypes.any,
  selectedTenantIds: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(PriceListForm));
