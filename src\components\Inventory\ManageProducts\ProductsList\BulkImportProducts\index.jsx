/* eslint-disable array-callback-return */
/* eslint-disable no-use-before-define */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Steps, Alert, Upload, Checkbox, Table, Select, notification
} from 'antd';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import { InboxOutlined, LoadingOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCircleCheck, faTriangleExclamation,
} from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import Constants from '@Apis/constants';
import TenantActions from '@Actions/tenantActions';
import ProductActions from '@Actions/productActions';
import H3Text from '@Uilib/h3Text';
import './style.scss';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import Helpers from '@Apis/helpers';
import TenantCheckboxGroup from '../../../../Common/TenantCheckboxGroup';
import PRZSelect from '../../../../Common/UI/PRZSelect';

const { Option } = Select;
const { Dragger } = Upload;
/**
 *
 */
function BulkImportProducts({
  user, MONEY, history, getBulkUploadMetadataLoading, getBulkUploadMetadata, bulkUploadMetadata, bulkUploadProducts, bulkUploadProductsLoading, callback, bulkUploadError, bulkUploadResponse, isBulkUploadProducts, productsBulkUpdate, bulkUpdateError, bulkUpdateResponse, productsBulkUpdateLoading, tenants, getTenants, getTenantsLoading, productsBulkUpdateSuccess, productsBulkUpdateError, bulkUploadProductsSuccess, bulkUploadProductsError,
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [searchXlsxField, setSearchXlsxField] = useState('');
  const [fileList, setFileList] = useState([]);
  const [sheetHeaders, setSheetHeaders] = useState(null);
  const [sheetData, setSheetData] = useState(null);
  const [readingSheet, setReadingSheet] = useState(false);
  const [columnMappingData, setColumnMappingData] = useState([]);
  const [uomMappingData, setUomMappingData] = useState([]);
  const [taxMappingData, setTaxMappingData] = useState([]);
  const [uomSearchKeyword, setUomSearchKeyword] = useState('');
  const [incompletedUomMapping, setIncompletedUomMapping] = useState(false);
  const [taxSearchKeyword, setTaxSearchKeyword] = useState('');
  const [incompletedTaxMapping, setIncompletedTaxMapping] = useState(false);
  const [incompletedColumnMapping, setIncompletedColumnMapping] = useState(false);
  const [allTenantsSelected, setAllTenantsSelected] = useState(true);
  const [selectedTenants, setSelectedTenants] = useState([]);
  const [showSelectedTenants, setShowSelectedTenants] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setReadingSheet(true);
      setFileList([file]);
      return false;
    },
    fileList,
    disabled: getBulkUploadMetadataLoading || readingSheet,
    loading: getBulkUploadMetadataLoading || readingSheet,
  };

  const onSubmit = () => {
    setIsSubmitted(true);
    const columnMapping = {};
    for (let i = 0; i < columnMappingData?.length; i++) {
      columnMapping[columnMappingData[i]?.procuzy_field_key] = columnMappingData[i]?.xlsx_column;
    }
    const uomMapping = {};
    for (let i = 0; i < uomMappingData?.length; i++) {
      uomMapping[uomMappingData[i]?.xlsx_uom] = uomMappingData[i]?.procuzy_uom;
    }
    const taxMapping = {};
    for (let i = 0; i < taxMappingData?.length; i++) {
      taxMapping[taxMappingData[i]?.xlsx_tax?.toString()] = taxMappingData[i]?.procuzy_tax;
    }
    const selectedTenantsList = user?.user_tenants.filter((data) => selectedTenants.includes(data.tenant_id));

    const payload = {
      file: fileList[0],
      columnMapping,
      uomMapping,
      taxMapping,
      tenants: Helpers.getTenantEntityPermission(selectedTenantsList, Helpers.permissionEntities.PRODUCT, isBulkUploadProducts ? Helpers.permissionTypes.CREATE : Helpers.permissionTypes.UPDATE).join(','),
      current_tenant_id: user?.tenant_info?.tenant_id,
    };
    if (isBulkUploadProducts) {
      bulkUploadProducts(payload, () => {
        callback();
        history.push('/analytics/bulkUpload');
      });
    } else {
      productsBulkUpdate(payload, () => {
        callback();
        history.push('/analytics/bulkUpload');
      });
    }
  };

  const getHeaders = (sheet) => {
    const range = XLSXUtils.decode_range(sheet['!ref']);
    const headers = [];

    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = sheet[XLSXUtils.encode_cell({ r: range.s.r, c: C })];
      headers.push(headerCell ? headerCell.v : 'undefined');
    }
    return headers;
  };

  useEffect(() => {
    getTenants(1, '', true, user?.tenant_info?.org_id, 100, (tenantsData) => {
      const selectedTenantsList = [];
      if (tenantsData?.data?.length > 0) {
        for (let i = 0; i < tenantsData?.data?.length; i++) {
          selectedTenantsList.push(tenantsData?.data?.[i]?.tenant_id);
        }
      }

      setSelectedTenants(selectedTenantsList);
    });
  }, []);

  useEffect(() => {
    if (tenants && !selectedTenants?.length && allTenantsSelected) {
      const newSelectedTenants = tenants?.data?.map((tenant) => tenant.tenant_id);
      setSelectedTenants(newSelectedTenants);
    }
  }, [selectedTenants, allTenantsSelected]);

  useEffect(() => {
    if (fileList?.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();
      reader.onload = (evt) => {
        const bstr = evt.target.result;
        const wb = XLSXRead(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSXUtils.sheet_to_json(ws, { header: 1 });
        const headers = getHeaders(ws);
        setSheetHeaders(headers?.filter((i) => i !== 'undefined'));
        const structuredData = data.slice(1).map((row) => {
          const rowData = {};
          headers?.filter((i) => i !== 'undefined').forEach((header, index) => {
            rowData[header] = row[index];
          });
          return rowData;
        });
        const filteredData = structuredData.filter(row => {
          return Object.values(row)?.some(value => value !== undefined && value !== null && (typeof value === 'string' ? value.trim() !== '' : true));
        });

        if (filteredData.length > 2500) {
          notification.error({
              message: 'You can only upload up to 2500 products at once.',
              placement: 'top',
              duration: 4,
          });
          setFileList([]);
          setReadingSheet(false);
          return;
        }
        setSheetData(filteredData);
        if (selectedTenants?.length > 0) {
          setCurrentStep(1);
        }

        setReadingSheet(false);
        const productColumns = isBulkUploadProducts ? bulkUploadMetadata?.import_columns : bulkUploadMetadata?.update_columns;
        const columnData = productColumns?.map((i) => (
          {
            key: uuidv4(),
            xlsx_column: null,
            xlsx_column_example: null,
            procuzy_field: i?.column_name,
            procuzy_field_key: i?.column_key,
            required: i?.is_required,
            description: i?.description,
            is_custom_field: i?.is_custom_field,
            mapped: false,
          }
        ));
        setColumnMappingData(columnData);
      };
      reader.readAsBinaryString(file);
      }
  }, [fileList]);

  useEffect(() => {
    getBulkUploadMetadata();
  }, []);

  const columnMappingColumns = () => {
    const columns = [
      {
        title: 'Import',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '60px',
        render: (text, record) => (
          <div style={{ textAlign: 'center' }}>
            <Checkbox checked={record?.mapped} disabled />
          </div>
        ),
      },
      {
        title: 'Procuzy Field',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '250px',
        render: (text, record) => (
          <Fragment>
            <div className="flex-display flex-align-c">
              <H3Text text={record?.procuzy_field} className="" required={record?.required} />
              {record?.is_custom_field && <div className="status-tag" style={{ marginLeft: '5px' }}>Custom</div>}
            </div>
            {record?.description && <H3Text text={record?.description} className="bulk-import-description" />}
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'XLSX Column',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '266px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.xlsx_column}
              showSearch
              filterOption={false}
              onSelect={(value) => {
                const columnMappingCopy = JSON.parse(JSON.stringify(columnMappingData));
                for (let i = 0; i < columnMappingCopy?.length; i++) {
                  if (columnMappingCopy[i].key === record?.key) {
                    columnMappingCopy[i].xlsx_column = value;
                    columnMappingCopy[i].mapped = !!value;
                  }
                }
                setColumnMappingData(columnMappingCopy);
                setSearchXlsxField('');
              }}
              onSearch={(value) => setSearchXlsxField(value)}
              placeholder="Select field"
              style={{
                width: '250px',
              }}
              allowClear
              onClear={() => {
                const columnMappingCopy = JSON.parse(JSON.stringify(columnMappingData));
                const updatedColumnMapping = columnMappingCopy.map(item => {
                    if (item.key === record?.key) {
                        return {
                            ...item,
                            xlsx_column: null,
                            mapped: false, 
                        };
                    }
                    return item;
                });
                setColumnMappingData(updatedColumnMapping);
              }}
            >
              {sheetHeaders
                ?.filter((item) => item?.toLowerCase().includes(searchXlsxField?.toLowerCase()))
                .map(
                  (item, index) => (
                    <Option key={index} value={item} disabled={columnMappingData?.find((i) => i?.procuzy_field === item)?.mapped}>
                      {item}
                    </Option>
                  ),
                )}
            </PRZSelect>
            {sheetData?.[0]?.[record?.xlsx_column]?.toString() ? <H3Text text={`eg. ${sheetData?.[0]?.[record?.xlsx_column]?.toString()}`} className="bulk-import__column-example" /> : ''}
          </Fragment>
        ),
      },
    ];
    return columns;
  };
  const uomMappingColumns = () => {
    const columns = [
      {
        title: 'XLSX Unit',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <Fragment>
            <H3Text text={record?.xlsx_uom} className="" />
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'Procuzy Unit',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '266px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.procuzy_uom}
              showSearch
              filterOption={false}
              onChange={(value) => {
                const uomMappingCopy = JSON.parse(JSON.stringify(uomMappingData));
                for (let i = 0; i < uomMappingCopy?.length; i++) {
                  if (uomMappingCopy[i].key === record?.key) {
                    uomMappingCopy[i].procuzy_uom = value;
                    uomMappingCopy[i].mapped = !!value;
                  }
                }
                setUomMappingData(uomMappingCopy);
                setUomSearchKeyword('');
              }}
              onSearch={(val) => {
                setUomSearchKeyword(val);
              }}
              placeholder="Select field"
              style={{
                width: '250px',
              }}
              allowClear
              onClear={() => {
                const uomMappingCopy = JSON.parse(JSON.stringify(uomMappingData));
                const updatedUomMapping = uomMappingCopy.map(item => {
                    if (item.key === record?.key) {
                        return {
                            ...item,
                            xlsx_uom: null,
                            mapped: false, 
                        };
                    }
                    return item;
                });
                setUomMappingData(updatedUomMapping);
              }}
            >
              {bulkUploadMetadata?.uoms
                .filter((i) => i.uom_name?.toLowerCase()?.includes(uomSearchKeyword?.toLowerCase()) || i.uqc?.toLowerCase()?.includes(uomSearchKeyword?.toLowerCase()))
                .map(
                  (uom) => <Option key={uom?.uom_id} value={uom?.uom_id}>{`${uom?.uqc} - ${uom?.uom_name}`}</Option>,
                )}
            </PRZSelect>
          </Fragment>
        ),
      },
    ];
    return columns;
  };
  const taxesMappingColumns = () => {
    const columns = [
      {
        title: 'XLSX Tax',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <Fragment>
            <H3Text text={record?.xlsx_tax} className="" />
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'Procuzy Tax',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '266px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.procuzy_tax}
              showSearch
              onChange={(value) => {
                const taxMappingCopy = JSON.parse(JSON.stringify(taxMappingData));
                for (let i = 0; i < taxMappingCopy?.length; i++) {
                  if (taxMappingCopy[i].key === record?.key) {
                    taxMappingCopy[i].procuzy_tax = value;
                    taxMappingCopy[i].mapped = !!value;
                  }
                }
                setTaxMappingData(taxMappingCopy);
                setTaxSearchKeyword('');
              }}
              onSearch={(val) => setTaxSearchKeyword(val)}
              placeholder="Select field"
              style={{
                width: '250px',
              }}
              filterOption={false}
              allowClear
              onClear={() => {
                const taxMappingCopy = JSON.parse(JSON.stringify(taxMappingData));
                const updatedTaxMapping = taxMappingCopy.map(item => {
                    if (item.key === record?.key) {
                        return {
                            ...item,
                            xlsx_tax: null,
                            mapped: false, 
                        };
                    }
                    return item;
                });
                setTaxMappingData(updatedTaxMapping);
              }}
            >
              {bulkUploadMetadata?.taxes
                .filter((i) => i.tax_name?.toLowerCase()?.includes(taxSearchKeyword?.toLowerCase()) || i.tax_value?.toString()?.toLowerCase()?.includes(taxSearchKeyword?.toLowerCase()))
                .map(
                  (uom) => <Option key={uom?.tax_id} value={uom?.tax_id}>{`${uom?.tax_name} - ${uom?.tax_value}`}</Option>,
                )}
            </PRZSelect>
          </Fragment>
        ),
      },
    ];
    return columns;
  };
  /**
     *
     * @return {JSX.Element}
     */
  return (
    <Fragment>
      <div className="bulk-import__wrapper">
        <Steps
          current={currentStep}
          size="small"
          items={[
            {
              title: 'Upload File',
            },
            {
              title: 'Column Mapping',
            },
            {
              title: 'UOM Mapping',
            },
            {
              title: 'Taxes Mapping',
            },
          ]}
        />
        {currentStep == 0 ? (
          <div className="bulk-import__step">
            <label className="bulk-import__form-label">
              Please upload products data in CSV/XLSX format
              <span style={{ color: 'red' }}>*</span>
            </label>
            <Dragger {...uploadProps}>
              <Fragment>
                <p className="ant-upload-drag-icon">
                  {readingSheet ? <LoadingOutlined /> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text" style={{ fontSize: '13px !important' }}>Click or drag file to this area to upload</p>
                <p className="ant-upload-hint">
                  (Note - We support up to 2500 products for a one-time upload)
                  <span style={{ color: 'red' }}>*</span>
                </p>
                <p className="ant-upload-hint">
                  (Supported formats .csv,.xlsx; max file size 5 MB)
                </p>
              </Fragment>
            </Dragger>
            <Alert
              showIcon
              message={(
                <Fragment>
                  <div>
                    <a
                      href={`${isBulkUploadProducts ? Constants.BULK_SKU_UPLOAD_SHEET : Constants.BULK_SKU_UPDATE_SHEET}?org_id=${user?.tenant_info?.org_id}&tenant_id=${user?.tenant_info?.tenant_id}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the data template.
                  </div>
                  <div>
                    {isBulkUploadProducts ? 'Only product name, tax percentage and unit are the mandatory fields for products bulk import.'
                      : 'Only Procuzy ref code is mandatory for updating products in bulk.' }
                  </div>
                </Fragment>
              )}
              type="warning"
              style={{ borderRadius: '4px', marginTop: '5px' }}
            />
            <div className="ant-col-md-24 ant-col-xs-24">
              <div className="bulk-update-product-form__tenant-list-wrapper">
                <div className="bulk-update-product-form__tenant-list-title">
                  <Checkbox
                    onChange={() => {
                      if (!allTenantsSelected) {
                        const defaultSelectedTenants = [];
                        for (let i = 0; i < tenants?.data?.length; i++) {
                          defaultSelectedTenants.push(tenants?.data?.[i]?.tenant_id);
                        }
                        setAllTenantsSelected(!allTenantsSelected);
                        setSelectedTenants(defaultSelectedTenants);
                      } else {
                        setSelectedTenants([]);
                        setAllTenantsSelected(!allTenantsSelected);
                      }
                    }}
                    checked={allTenantsSelected}
                  />
                  <div
                    className="bulk-update-product-form__tenant-list-title-text"
                  >
                    All Business Units
                    <span
                      onClick={() => setShowSelectedTenants(!showSelectedTenants)}
                    >
                      {showSelectedTenants ? "Show less" : "Show all"}
                    </span>
                  </div>
                </div>
                {(showSelectedTenants) && (
                  <TenantCheckboxGroup
                    tenants={tenants}
                    onChange={(value) => {
                      setSelectedTenants(value);
                      setAllTenantsSelected(value?.length === tenants?.data?.length);
                    }}
                    selectedValues={selectedTenants}
                  />
                )}
              </div>
            </div>
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  setCurrentStep(1);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '9px 15px',
                  width: '100px',
                }}
                disabled={readingSheet || !fileList?.length || getTenantsLoading || selectedTenants?.length === 0}
              />
            </div>
          </div>
        ) : ''}
        {currentStep == 1 ? (
          <div className="bulk-import__step">
            {incompletedColumnMapping && columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length > 0 && (
              <div style={{ marginBottom: '5px' }}>
                <Alert
                  type="error"
                  message={`${columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length} mandatory columns not mapped. Please map all mandatory fields.`}
                  showIcon
                />
              </div>
            )}
            {columnMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="warning"
                  message={`${columnMappingData?.filter((item) => !item?.mapped)?.length} columns not mapped. If you proceed, these columns won't get ${isBulkUploadProducts ? 'imported' : 'updated'}  into Procuzy.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={columnMappingColumns()}
              bordered={false}
              dataSource={columnMappingData || []}
              pagination={false}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => setCurrentStep(0)}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
              />
              &nbsp; &nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  if (columnMappingData?.filter((item) => !item?.mapped && item?.required)?.length) {
                    setIncompletedColumnMapping(true);
                  } else {
                    const columnDataCopy = JSON.parse(JSON.stringify(columnMappingData));
                    const uomColumn = columnDataCopy.find((item) => (isBulkUploadProducts ? item?.procuzy_field === 'Unit' : item?.procuzy_field === 'Purchase Uom'))?.xlsx_column;
                    const taxColumn = columnDataCopy.find((item) => item?.procuzy_field === 'Tax Percentage')?.xlsx_column;
                    const uomSet = new Set();
                    const taxSet = new Set();
                    for (let i = 0; i < sheetData?.length; i++) {
                      if (sheetData[i][uomColumn]) uomSet.add(sheetData[i][uomColumn]);
                      if (sheetData[i][taxColumn]?.toString()) taxSet.add(sheetData[i][taxColumn]);
                    }
                    const uomData = [];
                    uomSet?.forEach((uom) => uomData.push({
                      key: uuidv4(),
                      xlsx_uom: uom,
                      procuzy_uom: null,
                      mapped: false,
                    }));
                    setUomMappingData(uomData);
                    const taxData = [];
                    taxSet?.forEach((tax) => taxData.push({
                      key: uuidv4(),
                      xlsx_tax: tax,
                      procuzy_tax: null,
                      mapped: false,
                    }));
                    setTaxMappingData(taxData);
                    productsBulkUpdateError(null);
                    productsBulkUpdateSuccess(null);
                    bulkUploadProductsSuccess(null);
                    bulkUploadProductsError(null);
                    const nextStep = uomData.length === 0? (taxData.length === 0 ? 4 : 3): 2;
                    setCurrentStep(nextStep);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
              />
            </div>
          </div>
        ) : ''}
        {currentStep == 2 ? (
          <div className="bulk-import__step">
            {incompletedUomMapping && uomMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="error"
                  message={`You have not mapped ${uomMappingData?.filter((item) => !item?.mapped)?.length} units with procuzy units.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={uomMappingColumns()}
              bordered={false}
              dataSource={uomMappingData || []}
              pagination={false}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => {
                  setCurrentStep(1);
                  setIncompletedUomMapping(false);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  marginLeft: '0',
                  width: '100px',
                }}
              />
              &nbsp;&nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  if (uomMappingData?.filter((item) => !item?.mapped)?.length) {
                    setIncompletedUomMapping(true);
                  } else {
                    const nextStep = taxMappingData.length === 0 ? 4 : 3
                    setCurrentStep(nextStep);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
              />
            </div>
          </div>
        ) : ''}
        {currentStep == 3 ? (
          <div className="bulk-import__step">
            {(!isBulkUploadProducts && !isSubmitted && !uomMappingData?.length) && (
              <Fragment>
                <Alert
                  type="warning"
                  message={` Purchase UOM column is not mapped. If you proceed, this column won't get updated in Procuzy.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {incompletedTaxMapping && !isSubmitted &&  taxMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="error"
                  message={`You have not mapped ${taxMappingData?.filter((item) => !item?.mapped)?.length} units with procuzy units.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {(sheetData.length > 0 && !isSubmitted)  && (
              <Fragment>
                <Alert
                  type="info"
                  message={`${sheetData.length} records will be ${isBulkUploadProducts ? 'imported':'updated'} , click on ${isBulkUploadProducts ? 'import':'update'} to proceed.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={taxesMappingColumns()}
              bordered={false}
              dataSource={taxMappingData || []}
              pagination={false}
            />
            {!isBulkUploadProducts && !bulkUpdateError && (
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() =>{
                  const prevStep = uomMappingData.length === 0 ? 1 : 2;
                    setCurrentStep(prevStep)
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
                isLoading={productsBulkUpdateLoading}
                disabled={productsBulkUpdateLoading}
              />
            &nbsp;&nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Update"
                onClick={() => {
                  if (taxMappingData?.filter((item) => !item?.mapped)?.length) {
                    setIncompletedTaxMapping(true);
                  } else {
                    onSubmit();
                    setCurrentStep(4);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                isLoading={productsBulkUpdateLoading}
                disabled={productsBulkUpdateLoading}
              />
            </div>
            )}
            {isBulkUploadProducts && !bulkUploadError && (
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => setCurrentStep(2)}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '100px',
                }}
                isLoading={bulkUploadProductsLoading}
                disabled={bulkUploadProductsLoading}
              />
              &nbsp;&nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Import"
                onClick={() => {
                  if (taxMappingData?.filter((item) => !item?.mapped)?.length) {
                    setIncompletedTaxMapping(true);
                  } else {
                    onSubmit();
                    setCurrentStep(4);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                isLoading={bulkUploadProductsLoading}
                disabled={bulkUploadProductsLoading}
              />
            </div>
            )}
            </div>
        ) : ''}
        {currentStep == 4 ? (
          <div className="bulk-import__step">
            {(!isBulkUploadProducts && !isSubmitted && !taxMappingData?.length) && (
              <Fragment>
                <Alert
                  type="warning"
                  message={` Tax Percentage column is not mapped. If you proceed, this column won't get updated in Procuzy.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {(!isBulkUploadProducts && !isSubmitted && !uomMappingData?.length) && (
              <Fragment>
                <Alert
                  type="warning"
                  message={` Purchase UOM column is not mapped. If you proceed, this column won't get updated in Procuzy.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {(!isBulkUploadProducts && !isSubmitted && sheetData.length > 0) && (
              <Fragment>
                <Alert
                  type="info"
                  message={`${sheetData.length} records will be updated, click on update to proceed.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {(bulkUploadError?.success_count || bulkUploadResponse?.success_count) && !bulkUploadProductsLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadError?.success_count || bulkUploadResponse?.success_count }  records have been validated and the upload is in progress. Please refer to Bulk Uploads screen to check the status.`}
                  </div>
                )}
                type="success"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUploadError?.error_count && !bulkUploadProductsLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadError?.error_count} Records Failed.`}
                    <br />
                    <a
                      href={bulkUploadError?.records_errored_file}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    Continue
                    to download the records with error
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUploadError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Continue"
                  onClick={() => {
                    history.push('/analytics/bulkUpload');
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={bulkUploadProductsLoading}
                  disabled={bulkUploadProductsLoading}
                />
              </div>
            )}
            {isBulkUploadProducts && !bulkUploadError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_BORDERED}
                  text="Back"
                  onClick={() => setCurrentStep(2)}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={bulkUploadProductsLoading}
                  disabled={bulkUploadProductsLoading}
                />
              &nbsp;&nbsp;
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Import"
                  onClick={() => {
                    if (taxMappingData?.filter((item) => !item?.mapped)?.length) {
                      setIncompletedTaxMapping(true);
                    } else {
                      onSubmit();
                    }
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '125px',
                  }}
                  isLoading={bulkUploadProductsLoading}
                  disabled={bulkUploadProductsLoading}
                />
              </div>
            )}
            {(bulkUpdateError?.success_count || bulkUpdateResponse?.success_count) && !productsBulkUpdateLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUpdateError?.success_count || bulkUpdateResponse?.success_count }  Records are validated and Update In Progress Please Check Status on Bulk Upload Section.`}
                  </div>
                )}
                type="success"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUpdateError?.error_count && !productsBulkUpdateLoading && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUpdateError?.error_count} Records Failed.`}
                    <br />
                    <a
                      href={bulkUpdateError?.records_errored_file}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Click Here
                    </a>
                    {' '}
                    to download the records with error
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}
            {bulkUpdateError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Continue"
                  onClick={() => {
                    history.push('/analytics/bulkUpload');
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={productsBulkUpdateLoading}
                  disabled={productsBulkUpdateLoading}
                />
              </div>
            )}
            {!isBulkUploadProducts && !bulkUpdateError && (
              <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_BORDERED}
                  text="Back"
                  onClick={() => {
                    const prevStep = taxMappingData.length === 0 ? (uomMappingData.length === 0 ? 1 : 2): 3;
                    setCurrentStep(prevStep)
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '100px',
                  }}
                  isLoading={productsBulkUpdateLoading}
                  disabled={productsBulkUpdateLoading}
                />
              &nbsp;&nbsp;
                <H3Button
                  buttonType={defaultButtonTypes.BLUE_ROUNDED}
                  text="Update"
                  onClick={() => {
                    if (taxMappingData?.filter((item) => !item?.mapped)?.length) {
                      setIncompletedTaxMapping(true);
                    } else {
                      onSubmit();
                    }
                  }}
                  style={{
                    borderRadius: '5px',
                    padding: '7px 15px',
                    width: '125px',
                  }}
                  isLoading={productsBulkUpdateLoading}
                  disabled={productsBulkUpdateLoading}
                />
              </div>
            )}
          </div>
        ): '' }
      </div>
    </Fragment>
  );
}

const mapStateToProps = ({
  UserReducers, ProductReducers, TenantReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  bulkUpdateProductsLoading: ProductReducers.bulkUpdateProductsLoading,
  getBulkUploadMetadataLoading: ProductReducers.getBulkUploadMetadataLoading,
  bulkUploadMetadata: ProductReducers.bulkUploadMetadata,
  bulkUploadProductsLoading: ProductReducers.bulkUploadProductsLoading,
  bulkUploadResponse: ProductReducers.bulkUploadResponse,
  bulkUploadError: ProductReducers.bulkUploadError,
  productsBulkUpdateLoading: ProductReducers.productsBulkUpdateLoading,
  bulkUpdateResponse: ProductReducers.bulkUpdateResponse,
  bulkUpdateError: ProductReducers.bulkUpdateError,
  tenants: TenantReducers.tenants,
  getTenantsLoading: TenantReducers.getTenantsLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getBulkUploadMetadata: () => dispatch(ProductActions.getBulkUploadMetadata()),
  bulkUploadProducts: (payload, callback) => dispatch(ProductActions.bulkUploadProducts(payload, callback)),
  productsBulkUpdate: (payload, callback) => dispatch(ProductActions.productsBulkUpdate(payload, callback)),
  getTenants: (page, keyword, isVerified, orgId, limit, callback) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId, limit, callback)),
  bulkUploadProductsError: (empty) => dispatch(ProductActions.bulkUploadProductsError(empty)),
  bulkUploadProductsSuccess: (empty) => dispatch(ProductActions.bulkUploadProductsSuccess(empty)),
  productsBulkUpdateError: (empty) => dispatch(ProductActions.productsBulkUpdateError(empty)),
  productsBulkUpdateSuccess: (empty) => dispatch(ProductActions.productsBulkUpdateSuccess(empty)),
});

BulkImportProducts.propTypes = {
  user: PropTypes.any,
  MONEY: PropTypes.func,
  tenants: PropTypes.any,
  getTenants: PropTypes.func,
  getTenantsLoading: PropTypes.bool,
  productsBulkUpdateLoading: PropTypes.bool,
  bulkUploadProductsLoading: PropTypes.bool,

};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkImportProducts));
