/* eslint-disable no-nested-ternary */
import React, { Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import {
  DownloadOutlined, LoadingOutlined,
} from '@ant-design/icons';
import {
  Timeline, notification,
} from 'antd';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import './style.scss';
import Constants from '@Apis/constants';
import ProductActions from '@Actions/productActions';
import WorkflowActions from '@Actions/workflowActions';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';

const BulkUploadForm = ({
  user, callback, userDepartmentId, selectedSeller, getSellerCodes,
  getProductBySkusLoading,
  setData, filteredData, setFilteredData, adjustmentType, getProductBySkus,
}) => {

  const onFileSelect = (e) => {
    const [file] = e.target.files;
    const reader = new FileReader();
    reader.onload = (evt) => {
      const bstr = evt.target.result;
      const wb = XLSXRead(bstr, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSXUtils.sheet_to_json(ws, { header: 1 });

      function convertExcelForExpiryDate(excelDate) {
        if (!excelDate) {
          return;
        }
        const startDate = dayjs('1900-01-01', { year: 1900, month: 0, day: 1 });
        const targetDate = startDate.add(excelDate - 2, 'day');
        return targetDate.format('YYYY-MM-DD');
      }

      function convertExcelDateBatchInwardDate(excelDate) {
        if (!excelDate) {
          return dayjs().format('YYYY-MM-DD');
        }
        const startDate = dayjs('1900-01-01', { year: 1900, month: 0, day: 1 });
        const targetDate = startDate.add(excelDate - 2, 'day');
        return targetDate.format('YYYY-MM-DD');
      }

      const copyFileData = data?.slice(1)?.map((item) => {
        // Map headers to column indices
        const columnIndices = data[0]?.reduce((acc, col, colIndex) => {
          acc[col] = colIndex;
          return acc;
        }, {});
        // Extract custom fields with [BATCH CF]
        const customFields = Object.keys(columnIndices)
          .filter((header) => header.includes('[')) // Identify headers with brackets
          .map((header) => {
            const properName = header.split('[')[0].trim(); // Extract meaningful part before '['
            const id = header.match(/\[(\d+)\]/)?.[1] || ''; // Extract numeric ID from brackets
            const value = item[columnIndices[header]] || ''; // Get the corresponding value
            return {
              id, // Store the extracted ID
              name: properName, // Store the proper name
              value, // Store the corresponding value
            };
          });
        // Map standard fields along with custom_fields
        return {
          sku_id: item[columnIndices.sku_id],
          ref_product_code: item[columnIndices.ref_product_code],
          sku_name: item[columnIndices.sku_name],
          available_qty: item[columnIndices.available_stock],
          quantity: item[columnIndices.quantity],
          batch_number: item[columnIndices.batch_number],
          seller_code: item[columnIndices.seller_code],
          manufacturing_date: convertExcelForExpiryDate(
            item[columnIndices.manufacturing_date],
          ),
          expiry_date: convertExcelForExpiryDate(
            item[columnIndices.expiry_date],
          ),
          batch_inward_date: convertExcelDateBatchInwardDate(
            item[columnIndices.batch_inward_date],
          ),
          lot_number: item[columnIndices.lot_number],
          cost_price: item[columnIndices.cost_price],
          selling_price: item[columnIndices.selling_price],
          mrp: item[columnIndices.mrp],
          secondary_quantity: item[columnIndices.secondary_quantity],
          secondary_available_stock:
            item[columnIndices.secondary_available_stock],
          customFields, // Add the custom fields array here
          key: uuidv4(), // Add a unique identifier
        };
      });
      setFilteredData(copyFileData?.filter((item) => item?.sku_id));
    };
    reader.readAsBinaryString(file);
  };

  const downloadSampleFile = async (adjType) => {
    const url = `${Constants.GET_BULK_PRODUCT_SHEET}?tenant_id=${user?.tenant_info?.tenant_id}&type=${adjType}&tenant_department_id=${String(userDepartmentId || Helpers.getTenantDepartmentId(user, null, null, null))}`;
    const accessToken = localStorage.getItem('h3m-procuzy-access-token');
  
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
  
      if (!response.ok) {
        throw new Error('Failed to download file');
      }
  
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = 'sample_file.xlsx';
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download error:', error);
    }
  };
  
  // const downloadSampleFile = (adjType) => {
  //   const a = document.createElement('a');
  //   const url = `${Constants.GET_BULK_PRODUCT_SHEET}?tenant_id=${user?.tenant_info?.tenant_id}&type=${adjType}&tenant_department_id=${String(userDepartmentId || Helpers.getTenantDepartmentId(user, null, null, null))}`;
  //   a.href = url;
  //   a.download = url.split('/').pop();
  //   document.body.appendChild(a);
  //   a.click();
  //   document.body.removeChild(a);
  // };

  const addData = () => {
    if (filteredData?.length > 500) {
      notification.error({
        message: 'You can only upload upto 500 products at once.',
        placement: 'top',
        duration: 4,
      });
    } else {
      const skusData = filteredData
        ?.filter((item) => item?.sku_id)
        ?.map((item) => ({ key: item?.key, sku_id: item?.sku_id }));
      const uniqueSkuIds = [...new Set(filteredData?.filter((item) => item?.sku_id)?.map((item) => item?.sku_id))];

      const payload = {
        tenant_ids: String(user?.tenant_info?.tenant_id),
        sku_codes: uniqueSkuIds,
        ...(selectedSeller && { tenant_seller_ids: String(selectedSeller) }),
        org_id: String(user?.tenant_info?.org_id),
        tenant_department_ids: String(userDepartmentId || Helpers.getTenantDepartmentId(user, null, null, null)),
        sku_data: skusData,
      };
      getProductBySkus(payload, ((updatedData) => {
        setData(updatedData?.data);
        callback();
      }));
      // getSellerCodes(
      //   filteredData?.filter((item) => item?.sku_id)?.map((item) => item?.seller_code)?.join(','),
      //   user?.tenant_info?.org_id,
      // );
      const sellerCodes = filteredData?.filter((item) => item?.sku_id)?.map((item) => item?.seller_code);
      if (sellerCodes && sellerCodes.length > 0) {
        const concatenatedCodes = sellerCodes.join(',');
        if (concatenatedCodes.trim() !== '') {
          getSellerCodes(concatenatedCodes, user?.tenant_info?.org_id);
        }
      }
    }
  };

  return (
    <Fragment>
      <div className="ant-row" style={{ paddingBottom: '60px' }}>
        <div className="ant-col-md-24">
          <div className="view__timeline-workflow">
            <Timeline>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 1
                    </div>
                    <div className="timeline-item-person">
                      Download the list of products for this Business Unit
                      <div className="timeline-item-download">
                        <div
                          className="timeline-item-download-button"
                          onClick={() => {
                            downloadSampleFile(adjustmentType);
                          }}
                        >
                          <DownloadOutlined />
                          <H3Text text="Download products" className="" />
                          <div className="timeline-item-download-button__loading">
                            {getProductBySkusLoading && <LoadingOutlined />}
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 2
                    </div>
                    <div className="timeline-item-person">
                      Add the quantity against each product without changing SKU code.
                    </div>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 3
                    </div>
                    <div className="timeline-item-person">
                      Upload the sheet and verify the list of products.
                    </div>

                  </div>
                </div>
              </Timeline.Item>
            </Timeline>
            <div className="view__timeline-upload">
              <input
                type="file"
                onChange={(e) => onFileSelect(e)}
                className="view__timeline-upload-input"
                accept=".xlsx,.xls"
              />
            </div>
          </div>
        </div>
        {/* {!disableBatchCreate && (
          <Checkbox
            checked={createNewBatch}
            onChange={() => setCreateNewBatch()}
          >
            Create new batch for each product
          </Checkbox>
        ) } */}

        <div className="custom-drawer__footer">
          <H3Button
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            text="+ Add Products"
            isLoading={getProductBySkusLoading}
            onClick={() => addData()}
            disabled={getProductBySkusLoading}
            style={{
              width: '150px',
              borderRadius: '5px',
              padding: '7px 0px',
              marginLeft: '0',
            }}
          />
        </div>
      </div>
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, ProductReducers,
}) => ({
  user: UserReducers.user,
  getProductBySkusLoading: ProductReducers.getProductBySkusLoading,
  getSellerCodesLoading: ProductReducers.getSellerCodesLoading,

});

const mapDispatchToProps = (dispatch) => ({
  getPurchaseWorkflowSample: (tenantId, sampleType) => dispatch(WorkflowActions.getPurchaseWorkflowSample(tenantId, sampleType)),
  getSellerCodes: (sellerCode, orgId, callback) => dispatch(ProductActions.getSellerCodes(sellerCode, orgId, callback)),
  getProductBySkus: (payload, callback) => dispatch(ProductActions.getProductBySkus(payload, callback)),
});

BulkUploadForm.propTypes = {
  callback: PropTypes.func,
  getProductBySkusLoading: PropTypes.any,
  user: PropTypes.any,
  getSellerCodes: PropTypes.func,
  userDepartmentId: PropTypes.any,
  selectedSeller: PropTypes.any,
  setData: PropTypes.func,
  filteredData: PropTypes.any,
  setFilteredData: PropTypes.func,
  adjustmentType: PropTypes.string,
  getProductBySkus: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkUploadForm));
