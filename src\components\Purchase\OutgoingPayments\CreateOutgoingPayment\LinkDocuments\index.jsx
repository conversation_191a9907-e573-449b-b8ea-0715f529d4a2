import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Table } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from 'dayjs';

import { useApiQuery } from '../../../../../apiV2/hooks/useApiQuery';
import GRNActions from '@Actions/grnActions';
import H3Image from '@Uilib/h3Image';
import PRZButton from '@Components/Common/UI/PRZButton';
import PRZText from '@Components/Common/UI/PRZText';

import { cdnUrl } from '@Utils/cdnHelper';
import './style.scss';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import PRZSelect from '@Components/Common/UI/PRZSelect';
import SelectDepartment from '@Components/Common/SelectDepartment';
import H3DateSelector from '@Uilib/h3DateSelector';
import TagSelector from '@Components/Common/Selector/TagSelector';

const normalizeRecord = (record, type) => {
  const conversionRate = record?.conversion_rate || 1;
  const grandTotal = record?.grn_grand_total_amount ?? (record?.grn_grand_total ?? record?.grand_total) * conversionRate;
  const paymentMade = record?.payment_made ?? (record?.total_payment_made * conversionRate);
  const debitNoteTotal = record?.debit_note_info?.reduce((acc, dn) => acc + dn?.db_total, 0) || 0;
  const dueAmount = grandTotal - paymentMade - debitNoteTotal;
  const entityId = type === 'grn' ? record?.grn_id : record?.ap_invoice_id;
  const entityNumber = type === 'grn' ? record?.grn_number : record?.ap_invoice_number;
  const dateTime = type === 'grn' ? record?.grn_date_time : record?.ap_invoice_date;

  return {
    id: record?.grn_id ?? record?.ap_invoice_id,
    entity_id: entityId,
    entity_number: entityNumber,
    entity_type: type === 'grn' ? 'grn_id' : 'ap_invoice_id',
    grand_total_amount: grandTotal,
    payment_made: paymentMade,
    debit_note_total: debitNoteTotal,
    payment: 0,
    source: type,
    originalRecord: record,
    created_by_info: {
      ...record?.created_by_info,
      first_name: record?.first_name,
      last_name: record?.last_name,
    },
    date_time: dateTime,
    debit_note_info: record?.debit_note_info,
    due_amount: dueAmount,
  };
};

function LinkDocuments({
  vendor,
  callback,
  setDocuementData,
  documentData,
}) {

  const [selectedRowKeys, setSelectedRowKeys] = useState(null);
  const [selectedRows, setSelectedRows] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [limit] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [dataSource, setDataSource] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState([]);
  const [sourceOfGrn, setSourceOfGrn] = useState([]);
  const [paymentStatus, setPaymentStatus] = useState([]);
  const [grnFromDueDate, setGrnFromDueDate] = useState('');
  const [grnToDueDate, setGrnToDueDate] = useState('');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [dueDateSelectedIntervalDateRangeType, setDueDateSelectedIntervalDateRangeType] = useState('');
  const [orderDateSelectedIntervalDateRangeType, setOrderDateSelectedIntervalDateRangeType] = useState('');
  const [selectedTag, setSelectedTag] = useState([]);
  const [call, setCall] = useState(false);

  const dispatch = useDispatch();

  const getGRN = (tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId, selectedEntityType, searchKeyword, sellerId, startDate, endDate, scheduleGrn, paymentStatus, grnFromDueDate, grnToDueDate, tags, departmentIds, sourceOfGrn, callback, excludeGRNIds, excludeGRNVendorIds, is_list) => dispatch(
    GRNActions.getGRN(tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId, selectedEntityType, searchKeyword, sellerId, startDate, endDate, scheduleGrn, paymentStatus, grnFromDueDate, grnToDueDate, tags, departmentIds, sourceOfGrn, callback, excludeGRNIds, excludeGRNVendorIds, is_list),
  );

  const { user, MONEY } = useSelector((state) => state?.UserReducers);
  const { getGRNLoading, grnData } = useSelector((state) => state?.GRNReducers);

  const isApInvoiceEnabled = useMemo(() => user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active, [user]);

  const { data: apInvoiceData, isApInvoicesLoading, refetch } = useApiQuery({
    key: ['apInvoices'],
    url: '/account-payable-invoice/list',
    params: {
      tenant_id: user?.tenant_info?.tenant_id,
      status: 'ISSUED',
      seller_id: vendor?.seller_id,
      search_keyword: searchKeyword,
      page: currentPage,
      limit,
      tags: selectedTag?.join(','),
      department_ids: selectedDepartment?.join(','),
      source_of_grn: sourceOfGrn?.join(','),
      payment_status: paymentStatus?.join(','),
      from_date: fromDate,
      to_date: toDate,
    },
    enabled: false,
  });

  useEffect(() => {
    if (isApInvoiceEnabled) {
      refetch();
    } else {
      getGRN(user?.tenant_info?.tenant_id, '', '', '', 'ISSUED', limit, currentPage, null, null, searchKeyword, vendor?.seller_id, '', '', '', paymentStatus, grnFromDueDate, grnToDueDate, selectedTag, selectedDepartment, sourceOfGrn, () => {}, [], [], true);
    }
  }, [searchKeyword, currentPage, vendor?.seller_id, call]);

  useEffect(() => {
    if (isApInvoiceEnabled) {
      const normalizedData = apInvoiceData?.data?.map((record) => normalizeRecord(record, 'ap')).filter(Boolean) || [];
      setDataSource(normalizedData || []);
      setSelectedRowKeys(documentData?.map((item) => item?.entity_id));
      setSelectedRows(documentData);
    } else {
      const normalizedData = grnData?.grn?.map((record) => normalizeRecord(record, 'grn')).filter(Boolean) || [];
      setDataSource(normalizedData || []);
      setSelectedRowKeys(documentData?.map((item) => item?.entity_id));
      setSelectedRows(documentData);
    }
  }, [apInvoiceData, grnData, documentData]);

  const getColumns = () => {
    const columns = [
      {
        title: 'Document Number#',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {record?.entity_number}
          </div>
        ),
        width: '150px'
      },
      {
        title: 'Document Date',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {dayjs(record?.date_time).format('DD/MM/YYYY')}
          </div>
        ),
      },
      {
        title: 'Grand Total',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {MONEY(record?.grand_total_amount)}
          </div>
        ),
      },
      {
        title: 'Payment Made',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {MONEY(record?.payment_made)}
          </div>
        ),
      },
      {
        title: 'Debit Note Total',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {MONEY(record?.debit_note_total)}
          </div>
        ),
      },
      {
        title: 'Due Amount',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <div>
            {MONEY(record?.due_amount)}
          </div>
        ),
      },
    ];
    return columns;
  };

  return (
    <Fragment>
      <div
        className="doc__search-filters-main_container"
      >
        <div className="doc__search-filters-container">
          <div className="orgInputContainer">
            <label className="orgFormLabel">
              Selected Vendor
            </label>
            <PRZText
              text={vendor?.alias_name.toProperCase() || 'No Vendor Selected'}
              className='customInput'
            />
          </div>

          <div className="orgInputContainer">
            <label className="orgFormLabel">
              Search {isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}
            </label>
            <div className="section-search customInput">
              <div className="doc-list__search-bar__wrapper">
                <div className="section-search-bar">
                  <input
                    value={keyword}
                    placeholder={`Search for ${isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setKeyword(e.target.value);
                        setCurrentPage(1);
                      }
                    }}
                    onChange={(event) => {
                      setKeyword(event.target.value);
                    }}
                  />
                  <H3Image
                    src={cdnUrl('icon-search.png', 'icons')}
                    className="section-search-bar__icon"
                    alt="search"
                    onClick={() => {
                      setSearchKeyword(keyword);
                      setCurrentPage(1);
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Business Unit
            </label>
            <TenantSelector
              selectedTenant={vendor?.tenant_id}
              placeholder="Select Business Unit"
              hideTitle
              disabled
            />
          </div>
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Status
            </label>
            <PRZSelect
              value="ISSUED"
              className=''
              disabled
              options={[{ key: 'ISSUED', value: 'ISSUED', label: 'Issued' }]}
            />
          </div>
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Department
            </label>
            <SelectDepartment
              title="Select Department"
              hideTitle
              selectedDepartment={selectedDepartment}
              noDropdownAlign
              onChange={(value) => {
                setSelectedDepartment(value);
              }}
              isMultiple
            />
          </div>
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Source
            </label>
            <PRZSelect
              value={sourceOfGrn}
              onChange={(value) => {
                setSourceOfGrn(value);
                setCurrentPage(1);
              }}
              mode="multiple"
              placeholder="Filter by status.."
              options={[
                { key: 'INVENTORY_TRANSFER', value: 'INVENTORY_TRANSFER', label: 'Stock Transfer' },
                { key: 'PURCHASE_ORDER', value: 'PURCHASE_ORDER', label: 'Purchase Order' },
                { key: 'GOOD_RECEIVING_NOTE', value: 'GOOD_RECEIVING_NOTE', label: 'Adhoc' },
              ]}
            />
          </div>
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Payment Status
            </label>
            <PRZSelect
              value={paymentStatus}
              onChange={(value) => {
                setPaymentStatus(value);
                setCurrentPage(1);
              }}
              mode="multiple"
              placeholder="Filter by status.."
              options={[
                { key: 'PAID', value: 'PAID', label: 'Paid' },
                { key: 'PARTIALLY_PAID', value: 'PARTIALLY_PAID', label: 'Partially Paid' },
                { key: 'NOT_PAID', value: 'NOT_PAID', label: 'Not Paid' },
              ]}
            />
          </div>
          {(<div className='orgInputContainer'>
            <label className='orgFormLabel'>
              GRN due date
            </label>
            <H3DateSelector
              selectedInterval={dueDateSelectedIntervalDateRangeType}
              startDate={grnFromDueDate}
              endDate={grnToDueDate}
              onDateChangeWithDateRangeType={({ date, dateRangeType }) => {
                setGrnFromDueDate(dayjs(date[0])?.startOf('day').valueOf());
                setGrnToDueDate(dayjs(date[1])?.endOf('day').valueOf());
                setCurrentPage(1);
                setDueDateSelectedIntervalDateRangeType(dateRangeType);
              }}
            />
          </div>)}
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Document Date
            </label>
            <H3DateSelector
              selectedInterval={orderDateSelectedIntervalDateRangeType}
              startDate={fromDate}
              endDate={toDate}
              onDateChangeWithDateRangeType={({ date, dateRangeType }) => {
                setFromDate(dayjs(date[0])?.startOf('day').valueOf());
                setToDate(dayjs(date[1])?.endOf('day').valueOf());
                setCurrentPage(1);
                setOrderDateSelectedIntervalDateRangeType(dateRangeType);
              }}
            />
          </div>
          <div className='orgInputContainer'>
            <label className='orgFormLabel'>
              Labels
            </label>
            <TagSelector
              filter
              entityType={isApInvoiceEnabled ? 'AP_INVOICE' : 'GRN'}
              selectedTags={selectedTag}
              showSearch
              showAll={false}
              noDropdownAlign
              isMultiple
              onChange={(value) => {
                setSelectedTag(value);
              }}
              placeholder="Select Label"
              hideTitle
            />
          </div>
        </div>
        <PRZButton
          onClick={() => {
            setCall(!call);
          }}
        >
          Apply Filters
        </PRZButton>
        <div className="doc__table" style={{ marginTop: '10px' }}>
          <Table
            title={() => (
              <div className="doc__table-title">
                <PRZText text={`${isApInvoiceEnabled ? apInvoiceData?.count : grnData?.count || 0} ${isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}`} className="" />
              </div>
            )}
            bordered
            showHeader
            size="small"
            loading={isApInvoicesLoading || getGRNLoading}
            columns={getColumns()}
            dataSource={dataSource}
            scroll={{ y: 350 }}
            rowKey='entity_id'
            pagination={
              {
                defaultCurrent: currentPage,
                current: currentPage,
                total: isApInvoiceEnabled ? apInvoiceData?.count : grnData?.count || 0,
                pageSize: limit,
                showTotal: (tot, range) => `${range[0]}-${range[1]} of ${tot} items`,
                onChange: (page) => {
                  setCurrentPage(page);
                },
                position: ['bottomLeft'],
              }
            }
            rowSelection={
              window.screen.width > 425
                ? {
                  onChange: (rowKeys, rows) => {
                    setSelectedRowKeys(rowKeys);
                    setSelectedRows(rows);
                  },
                  selectedRowKeys,
                  getCheckboxProps: (record) => ({
                    disabled: record?.due_amount <= 0 || documentData?.some((doc) => doc?.entity_id === record?.entity_id),
                  }),
                }
                : false
            }
          />
        </div>
      </div>
      <div className="custom-drawer__footer" style={{ width: '100%' }}>
        <div className="ant-col-md-6">
          <PRZButton
            onClick={() => {
              if (selectedRows?.length) {
                const combinedOldAndNewLines = [
                  ...(selectedRows || []),
                  ...(documentData || []),
                ].filter(Boolean) // remove undefined/null
                  .filter(
                    (item, index, self) =>
                      index === self.findIndex((t) => t?.entity_id === item?.entity_id) // remove duplicates by entity_id
                  );
                setDocuementData(combinedOldAndNewLines?.map((item) => ({ ...item, payment: 0 })));
                callback();
                setSelectedRows([]);
                setSelectedRowKeys([]);
              }
            }}
            loading={isApInvoicesLoading || getGRNLoading}
            disabled={isApInvoicesLoading || getGRNLoading}
          >
            Link {isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}
          </PRZButton>
        </div>
      </div>
    </Fragment>
  );
}

export default LinkDocuments;