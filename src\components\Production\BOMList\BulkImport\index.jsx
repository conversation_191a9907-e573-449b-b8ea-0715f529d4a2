/* eslint-disable array-callback-return */
/* eslint-disable no-use-before-define */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Steps, Alert, Upload, Table, Select, Checkbox,
} from 'antd';
import { read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import { InboxOutlined, LoadingOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCircleCheck, faTriangleExclamation,
} from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import Constants from '@Apis/constants';
import ProductActions from '@Actions/productActions';
import H3Text from '@Uilib/h3Text';
import './style.scss';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import BOMActions from '@Actions/bomActions';
import PRZSelect from '../../../Common/UI/PRZSelect';
import TenantCheckboxGroup from '@Components/Common/TenantCheckboxGroup';
import Helpers from '@Apis/helpers';

const { Option } = Select;
const { Dragger } = Upload;
/**
 *
 */
function BulkImportBOM({
  getBulkUploadMetadataLoading, getBulkUploadMetadata, bulkUploadMetadata, callback, bulkUploadBOM, bulkUploadBOMLoading, bulkUploadBOMResults, getProducts, user, products,
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [readingSheet, setReadingSheet] = useState(false);
  const [uomMappingData, setUomMappingData] = useState([]);
  const [uomSearchKeyword, setUomSearchKeyword] = useState('');
  const [incompletedUomMapping, setIncompletedUomMapping] = useState(false);
  const [productNameMappingData, setProductNameMappingData] = useState([]);
  const [productNameSearchKeyword, setProductNameSearchKeyword] = useState('');
  const [incompletedProductNameMapping, setIncompletedProductNameMapping] = useState(false);
  const [createAllNewProducts, setCreateAllNewProducts] = useState(false);
  const [allTenantsSelected, setAllTenantsSelected] = useState(false);
  const [showSelectedTenants, setShowSelectedTenants] = useState(false);
  const [selectedTenants, setSelectedTenants] = useState([user?.tenant_info?.tenant_id]);

  const allowedTenants = user?.user_tenants?.filter((tenant) => tenant?.is_active && Helpers.getPermissionInTenant(tenant, Helpers.permissionEntities.BOM, Helpers.permissionTypes.CREATE)) || [];

  const sortedTenantIds = [
    user?.tenant_info,
    ...allowedTenants.filter((tenant) => tenant?.tenant_id !== user?.tenant_info?.tenant_id),
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      getProducts(productNameSearchKeyword, user?.tenant_info?.tenant_id);
    }, 300);
    return () => clearTimeout(timer);
  }, [productNameSearchKeyword]);

  const handleFileUpload = (file) => {
    const payload = {
      file,
    };
    bulkUploadBOM(payload, (data) => {
      const productNameData = data?.product_not_found?.map((productName) => ({
        key: uuidv4(),
        xlsx_product_name: productName,
        procuzy_product_name: null,
        mapped: false,
        createNew: false,
      }));
      setProductNameMappingData(productNameData);
      if (data?.product_not_found?.length > 0) {
        setCurrentStep(1);
      } else {
        setCurrentStep(2);
      }
    });
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      setReadingSheet(true);
      setFileList([file]);
      handleFileUpload(file);
      return false;
    },
    fileList,
    disabled: getBulkUploadMetadataLoading || readingSheet,
    loading: getBulkUploadMetadataLoading || readingSheet,
  };

  useEffect(() => {
    getBulkUploadMetadata();
  }, []);

  const getHeaders = (sheet) => {
    const range = XLSXUtils.decode_range(sheet['!ref']);
    const headers = [];

    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = sheet[XLSXUtils.encode_cell({ r: range.s.r, c: C })];
      headers.push(headerCell ? headerCell.v : 'undefined');
    }
    return headers;
  };

  useEffect(() => {
    if (fileList?.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();
      reader.onload = (evt) => {
        const bstr = evt.target.result;
        const wb = XLSXRead(bstr, { type: 'binary' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSXUtils.sheet_to_json(ws, { header: 1 });
        const headers = getHeaders(ws);
        const structuredData = data.slice(1).map((row) => {
          const rowData = {};
          headers?.filter((i) => i !== 'undefined')?.forEach((header, index) => {
            rowData[header] = row[index];
          });
          return rowData;
        });
        const uomDataMapRM = {};
        const uomDataMapFG = {};

        const uomDataRM = structuredData
          ?.filter((row) => row['RM UNIT']) // Check for existence of 'RM UNIT'
          ?.map((row) => {
            const unit = row['RM UNIT']; // Use the actual value
            if (!uomDataMapRM[unit]) {
              uomDataMapRM[unit] = true;
              return {
                key: uuidv4(),
                xlsx_uom: unit,
                procuzy_uom: null,
                mapped: false,
              };
            }
            return null; // Return null for duplicate units
          })
          ?.filter((item) => item !== null);

        const uomDataFG = structuredData
          ?.filter((row) => row['FG UNIT']) // Check for existence of 'FG UNIT'
          ?.map((row) => {
            const unit = row['FG UNIT']; // Use the actual value
            if (!uomDataMapFG[unit]) {
              uomDataMapFG[unit] = true;
              return {
                key: uuidv4(),
                xlsx_uom: unit,
                procuzy_uom: null,
                mapped: false,
              };
            }
            return null; // Return null for duplicate units
          })
          ?.filter((item) => item !== null);

        // Combine unique values from both mappings
        const combinedUomData = Array.from(new Set([...uomDataRM, ...uomDataFG].map((item) => item.xlsx_uom)))
          .map((xlsx_uom) => {
            const item = [...uomDataRM, ...uomDataFG].find((items) => items.xlsx_uom === xlsx_uom);
            return item;
          });
        setUomMappingData(combinedUomData);
        setReadingSheet(false);
      };
      reader.readAsBinaryString(file);
    }
  }, [fileList]);

  const productNameMappingColumns = () => {
    const columns = [
      {
        title: 'XLSX Product Name',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <Fragment>
            <H3Text text={record?.xlsx_product_name} className="" />
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'Procuzy Product Name',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '230px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.procuzy_product_name}
              showSearch
              filterOption={false}
              onChange={(value) => {
                const productNameMappingCopy = JSON.parse(JSON.stringify(productNameMappingData));
                for (let i = 0; i < productNameMappingCopy?.length; i++) {
                  if (productNameMappingCopy[i].key === record?.key) {
                    productNameMappingCopy[i].procuzy_product_name = value;
                    productNameMappingCopy[i].mapped = !record?.mapped;
                  }
                }
                setProductNameMappingData(productNameMappingCopy);
                setProductNameSearchKeyword('');
              }}
              onSearch={(val) => setProductNameSearchKeyword(val)}
              placeholder="Select field"
              style={{
                width: '230px',
              }}
              disabled={record?.createNew}
            >
              {products?.result?.products
                ?.filter((i) => i.alias_name
                  ?.toLowerCase()?.includes(productNameSearchKeyword?.toLowerCase()))
                ?.map(
                  (product) => <Option key={product?.product_sku_id} value={product?.product_sku_id}>{product?.alias_name}</Option>,
                )}
            </PRZSelect>
            <div
              className="create_new-product-table_wrapper"
            >
              <Checkbox
                className="create_new-product-table_checkbox"
                checked={record?.createNew}
                onClick={() => {
                  const productNameMappingCopy = JSON.parse(JSON.stringify(productNameMappingData));
                  for (let i = 0; i < productNameMappingCopy?.length; i++) {
                    if (productNameMappingCopy[i].key === record?.key) {
                      productNameMappingCopy[i].createNew = !record?.createNew;
                      productNameMappingCopy[i].mapped = !record?.createNew;
                      productNameMappingCopy[i].procuzy_product_name = null;
                    }
                  }
                  setProductNameMappingData(productNameMappingCopy);
                }}
              />
              <H3Text text="Create New Product " className="" />
            </div>
          </Fragment>
        ),
      },
    ];
    return columns;
  };

  const uomMappingColumns = () => {
    const columns = [
      {
        title: 'XLSX Unit',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <Fragment>
            <H3Text text={record?.xlsx_uom} className="" />
          </Fragment>
        ),
      },
      {
        title: 'Mapped',
        width: '60px',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => (
          <div className="bulk-import__status-icon" style={{ textAlign: 'center' }}>
            {record?.mapped ? <FontAwesomeIcon icon={faCircleCheck} color="#1AC05D" /> : <FontAwesomeIcon icon={faTriangleExclamation} color="#dcaa06" />}
          </div>
        ),
      },
      {
        title: 'Procuzy Unit',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '266px',
        render: (text, record) => (
          <Fragment>
            <PRZSelect
              value={record?.procuzy_uom}
              showSearch
              filterOption={false}
              onChange={(value) => {
                const uomMappingCopy = JSON.parse(JSON.stringify(uomMappingData));
                for (let i = 0; i < uomMappingCopy?.length; i++) {
                  if (uomMappingCopy[i].key === record?.key) {
                    uomMappingCopy[i].procuzy_uom = value;
                    uomMappingCopy[i].mapped = true;
                  }
                }
                setUomMappingData(uomMappingCopy);
                setUomSearchKeyword('');
              }}
              onSearch={(val) => setUomSearchKeyword(val)}
              placeholder="Select field"
              style={{
                borderRadius: '2px',
                width: '250px',
                fontSize: '13px',
              }}
            >
              {bulkUploadMetadata?.uoms
                ?.filter((i) => i.uom_name?.toLowerCase()?.includes(uomSearchKeyword?.toLowerCase()) || i.uqc?.toLowerCase()?.includes(uomSearchKeyword?.toLowerCase()))
                ?.map(
                  (uom) => <Option key={uom?.uom_id} value={uom?.uom_id}>{`${uom?.uqc} - ${uom?.uom_name}`}</Option>,
                )}
            </PRZSelect>
          </Fragment>
        ),
      },
    ];
    return columns;
  };
  // const handleRadioChange = (e) => {
  //   setSelectedOption(e.target.value);
  // };

  const onSubmit = () => {
    const uomMapping = {};
    for (let i = 0; i < uomMappingData?.length; i++) {
      uomMapping[uomMappingData[i]?.xlsx_uom] = uomMappingData[i]?.procuzy_uom;
    }
    const productNameMapping = [];
    for (let i = 0; i < productNameMappingData?.length; i++) {
      productNameMapping.push({
        product_sku_name: productNameMappingData[i]?.xlsx_product_name,
        product_sku_id: productNameMappingData[i]?.createNew ? null : productNameMappingData[i]?.procuzy_product_name,
        create_product: productNameMappingData[i]?.createNew,
      });
    }

    const payload = {
      file: fileList[0],
      uomMapping,
      productNameMapping,
      tenant_id: user?.tenant_info?.tenant_id,
      tenant_ids_to_be_enabled: selectedTenants?.filter((item) => item !== user?.tenant_info?.tenant_id),
    };

    bulkUploadBOM(payload, () => {
      callback();
    });
  };

  /**
     *
     * @return {JSX.Element}
     */
  return (
    <Fragment>
      <div className="bulk-import__wrapper">
        <Steps
          current={currentStep}
          size="small"
          items={[
            {
              title: 'Upload File',
            },
            {
              title: 'Product Mapping',
            },
            {
              title: 'UOM Mapping',
            },
          ]}
        />
        {currentStep === 0 ? (
          <div className="bulk-import__step" style={{ margin: '10px 0' }}>
            {/* <div style={{
              padding: '5px',
              marginBottom: '5px',
            }}
            >
              <Radio.Group onChange={handleRadioChange} value={selectedOption}>
                <Radio value="procuzy_ref_code">Procuzy Ref Code</Radio>
                <Radio value="internal_sku_code">Internal SKU Code</Radio>
              </Radio.Group>
            </div> */}

            <label className="bulk-import__form-label">
              Please upload BOM data in CSV/XLSX format
              <span style={{ color: 'red' }}>*</span>
            </label>

            <Dragger {...uploadProps} style={{ marginBottom: '16px' }}>
              <Fragment>
                <p className="ant-upload-drag-icon">
                  {readingSheet ? <LoadingOutlined /> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text" style={{ fontSize: '13px !important' }}>Click or drag file to this area to upload</p>
                <p className="ant-upload-hint">
                  (Supported formats .csv,.xlsx; max file size 5 MB)
                </p>
              </Fragment>
            </Dragger>

            <Alert
              showIcon
              message={(
                <div>
                  <a
                    href={`${Constants.BOM}/download_sheet?type=${'basic_sheet'}&org_id=${user?.tenant_info?.org_id}`}
                    target="_blank"
                    rel="noreferrer"
                  >
                    Click Here
                  </a>
                  {' '}
                  to download the data template
                </div>
              )}
              type="warning"
              style={{ borderRadius: '4px', marginTop: '10px' }}
            />

            <div className="ant-col-md-24">
              <div className="add-product__tenant-list-wrapper">
                <div className="add-product__tenant-list-title">
                  <Checkbox
                    onChange={() => {
                      if (!allTenantsSelected) {
                        const defaultSelectedTenants = [];
                        for (let i = 0; i < sortedTenantIds?.length; i++) {
                          defaultSelectedTenants.push(sortedTenantIds?.[i]?.tenant_id);
                        }
                        setAllTenantsSelected(!allTenantsSelected);
                        setSelectedTenants(defaultSelectedTenants);
                      } else {
                        setSelectedTenants([user?.tenant_info?.tenant_id]);
                        setAllTenantsSelected(!allTenantsSelected);
                      }
                    }}
                    checked={allTenantsSelected}
                  />
                  <div className="add-product__tenant-list-title-text">
                    All Business Units
                    <span
                      onClick={() => setShowSelectedTenants(!showSelectedTenants)}
                    >
                      {showSelectedTenants ? 'Show less' : 'Show all'}
                    </span>
                  </div>
                </div>
                {(showSelectedTenants) && (
                  <TenantCheckboxGroup
                    allowedTenants={sortedTenantIds}
                    onChange={(value) => {
                      setSelectedTenants(value);
                      setAllTenantsSelected(value?.length === allowedTenants?.length);
                    }}
                    selectedValues={selectedTenants}
                    currentTenantId={user?.tenant_info?.tenant_id}
                  />
                )}
              </div>
            </div>

            <div className="custom-drawer__footer flex-display" style={{ width: '100%', marginTop: '20px' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  // if (bulkUploadBOMResults && bulkUploadBOMResults.length > 0) {
                  //   setCurrentStep(1);
                  // } else {
                  //   setCurrentStep(2);
                  // }
                  setCurrentStep(1);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '9px 15px',
                  width: '100px',
                }}
                disabled={readingSheet || !fileList?.length}
              />
            </div>
          </div>
        ) : ''}
        {currentStep === 1 ? (
          <div className="bulk-import__step">
            {incompletedProductNameMapping && productNameMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="error"
                  message={`You have not mapped ${productNameMappingData?.filter((item) => !item?.mapped)?.length} units with procuzy units.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            {bulkUploadBOMResults?.product_not_found.length > 0 && (
              <Alert
                showIcon
                message={(
                  <div>
                    {`${bulkUploadBOMResults?.product_not_found.length} records have errors in the sheet you have uploaded.`}
                  </div>
                )}
                type="error"
                style={{ borderRadius: '4px', marginTop: '10px' }}
              />
            )}

            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={productNameMappingColumns()}
              bordered={false}
              dataSource={productNameMappingData || []}
              pagination={false}
              loading={bulkUploadBOMLoading}
              title={() => (
                <div
                  className="table_heading_wrapper"
                >
                  <Checkbox
                    className="table_heading_checkbox"
                    checked={createAllNewProducts}
                    onClick={() => {
                      const productNameMappingCopy = JSON.parse(JSON.stringify(productNameMappingData));
                      for (let i = 0; i < productNameMappingCopy?.length; i++) {
                        productNameMappingCopy[i].createNew = !createAllNewProducts;
                        productNameMappingCopy[i].mapped = !createAllNewProducts;
                        productNameMappingCopy[i].procuzy_product_name = null;
                      }
                      setProductNameMappingData(productNameMappingCopy);
                      setCreateAllNewProducts(!createAllNewProducts);
                    }}
                  />
                  <H3Text text="Create New Products For All The Unmatched Products" className="table_heading" />
                </div>
              )}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => {
                  setCurrentStep(0);
                  setIncompletedProductNameMapping(false);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  marginLeft: '0',
                  width: '100px',
                }}
              />
              &nbsp;&nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  if (productNameMappingData?.filter((item) => !item?.mapped)?.length) {
                    setIncompletedProductNameMapping(true);
                  } else {
                    setCurrentStep(2);
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                disabled={productNameMappingData?.filter((item) => !item?.mapped)?.length}
                isLoading={bulkUploadBOMLoading}
              />
            </div>
          </div>
        ) : ''}
        {currentStep === 2 ? (
          <div className="bulk-import__step">
            {incompletedUomMapping && uomMappingData?.filter((item) => !item?.mapped)?.length > 0 && (
              <Fragment>
                <Alert
                  type="error"
                  message={`You have not mapped ${uomMappingData?.filter((item) => !item?.mapped)?.length} units with procuzy units.`}
                  showIcon
                />
                <br />
              </Fragment>
            )}
            <Table
              showHeader
              size="small"
              scroll={{ x: 'max-content' }}
              columns={uomMappingColumns()}
              bordered={false}
              dataSource={uomMappingData || []}
              pagination={false}
            />
            <div className="custom-drawer__footer flex-display" style={{ width: '100%' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_BORDERED}
                text="Back"
                onClick={() => {
                  setCurrentStep(1);
                  setIncompletedUomMapping(false);
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  marginLeft: '0',
                  width: '100px',
                }}
              />
              &nbsp;&nbsp;
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Continue"
                onClick={() => {
                  if (uomMappingData?.filter((item) => !item?.mapped)?.length) {
                    setIncompletedUomMapping(true);
                  } else {
                    onSubmit();
                  }
                }}
                style={{
                  borderRadius: '5px',
                  padding: '7px 15px',
                  width: '125px',
                }}
                disabled={uomMappingData?.filter((item) => !item?.mapped)?.length}
                isLoading={bulkUploadBOMLoading}
              />
            </div>
          </div>
        ) : ''}
      </div>
    </Fragment>
  );
}

const mapStateToProps = ({
  UserReducers, ProductReducers, BOMReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  getBulkUploadMetadataLoading: ProductReducers.getBulkUploadMetadataLoading,
  bulkUploadMetadata: ProductReducers.bulkUploadMetadata,
  bulkUploadResponse: ProductReducers.bulkUploadResponse,
  bulkUploadBOMLoading: BOMReducers.bulkUploadBOMLoading,
  bulkUploadBOMResults: BOMReducers.bulkUploadBOMResults,
  products: ProductReducers.products,
});

const mapDispatchToProps = (dispatch) => ({
  getBulkUploadMetadata: () => dispatch(ProductActions.getBulkUploadMetadata()),
  bulkUploadBOM: (payload, callback) => dispatch(BOMActions.bulkUploadBOM(payload, callback)),
  getProducts: (keyword, tenantId, stockStatus, limit, page, tenantDepartmentId, excludeOutOfStock, productType, category, isSalesProduct, isPurchaseProduct, search, sortBy, filterReservedQuantity, barcode, isShopifyProduct, isMarketplaceProduct) => dispatch(ProductActions.getProducts(keyword, tenantId, stockStatus, limit, page, tenantDepartmentId, excludeOutOfStock, productType, category, isSalesProduct, isPurchaseProduct, search, sortBy, filterReservedQuantity, barcode, isShopifyProduct, isMarketplaceProduct)),
});

BulkImportBOM.propTypes = {
  user: PropTypes.any,
  MONEY: PropTypes.func,
  bulkUploadBOM: PropTypes.func,
  bulkUploadBOMLoading: PropTypes.bool,
  callback: PropTypes.func,
  getBulkUploadMetadataLoading: PropTypes.bool,
  getBulkUploadMetadata: PropTypes.func,
  bulkUploadMetadata: PropTypes.any,
  bulkUploadBOMResults: PropTypes.any,
  getProducts: PropTypes.func,
  products: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkImportBOM));
