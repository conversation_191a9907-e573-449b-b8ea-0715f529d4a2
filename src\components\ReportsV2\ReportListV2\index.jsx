// 📦 React & Hooks
import React, { Fragment, useEffect, useState } from 'react';

// 🚦 Routing
import { withRouter } from 'react-router-dom';

// 🧩 Redux
import { connect } from 'react-redux';

// 🧾 Redux Actions
import AnalyticsActions from '@Actions/application/analyticsActions';
import ReportV2Actions from '../../../actions/Reports/reportV2Actions';

// 📅 Utilities
import dayjs from 'dayjs';
import { cdnUrl } from '@Utils/cdnHelper';

// 🎨 UI Components - H3 UI Kit
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import PRZText from '../../Common/UI/PRZText';
import PRZSelect from '../../Common/UI/PRZSelect';

// 📄 Module-Specific Components
import ReportFilterForm from '../ReportFilterForm';

// 🧰 Ant Design Components
import { Badge, Divider, Drawer, Empty, Menu, Popconfirm, Popover, Select, Skeleton, Table, Tabs, Tooltip } from 'antd';

// 🎨 Icons
import { DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import IconSwitcher from '../IconSwitcher';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleChevronRight, faCircleInfo, faClockRotateLeft, faDownload } from '@fortawesome/free-solid-svg-icons';

// 🖌️ Styles
import './style.scss';

// 🔍 Prop Types
import PropTypes from 'prop-types';
import PRZLottieT from '@Components/Common/UI/PRZLottieT';

const { TabPane } = Tabs;

const { SubMenu } = Menu;
const { Option } = Select;

const ReportsListV2 = ({
  user, getReportsV2List, getReportsV2ListLoading, reportsV2ListData, downloadDocument, deleteScheduledReportLoading,
  reportsV2ListDetailData, getReportsV2ListDetail, getReportsV2ListDetailLoading, deleteScheduledReport,
}) => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [activeTab, setActiveTab] = useState('MY_REPORTS');
  const [selectedEmails, setSelectedEmails] = useState([]);
  const [pageNumber, setPageNumber] = useState(1);
  const [limit, setLimit] = useState(10);
  const [selectedFilters, setSelectedFilters] = useState({});
  const [keyword, setKeyword] = useState('');
  const [lastReportType, setLastReportType] = useState('');
  const [call, setCall] = useState(false);

  const fetchData = () => {
    getReportsV2ListDetail({
      type: activeTab,
      page: pageNumber,
      limit: limit,
      ...selectedFilters,
    });
  };

  const handleFilterChange = (filterName, value) => {
    setSelectedFilters({ ...selectedFilters, [filterName]: value });
  };

  useEffect(() => {
    getReportsV2List();
  }, [call]);

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, [getReportsV2ListDetail, activeTab, selectedFilters, pageNumber, limit, call]);

  useEffect(() => {
    setSelectedEmails([user?.email]);
  }, [user]);

  const transformedData = reportsV2ListData?.report_groups?.map(({ report_group, report_list }) => ({
    title: report_group.name,
    icon: report_group.icon,
    subList: report_list?.map((reportItem) => ({
      ...reportItem,

    })),
  }));

  const handleMenuItemClick = (reportId) => {
    setSelectedReport(reportId);
    setDrawerVisible(true);
  };

  const handleTabChange = (key) => {
    setPageNumber(1);
    setActiveTab(key);
  };

  const reportColumns = [
    {
      title: '',
      render: (record) => {
        const statusColor = {
          PENDING: 'rgb(224, 188, 67)',
          COMPLETED: '#32CD32',
          ERROR: '#FF0000',
          ACTIVE: 'rgb(45, 124, 247)',
          DISABLED: 'rgb(181, 181, 181)',
        };
        return (
          <div className="mobile-list__card">
            <div className="mobile-list__card_header">
              <div className="mobile-list__card_header-left">
                <PRZText text={`${record?.report_name}`} className="mobile-list__card_item-number" />
              </div>
              <div className="mobile-list__card_header-right" >
                <div style={{ color: statusColor[record?.status] }} className="flex-display flex-align-c">
                  {record?.status}
                  &nbsp;
                  {record?.status === 'PENDING' ? <LoadingOutlined /> : ''}
                  {
                    record?.error_message && (
                      <Popover
                        content={record?.error_message}
                        trigger="click"
                      >
                        <FontAwesomeIcon
                          icon={faCircleInfo}
                          color="grey"
                        />
                      </Popover>
                    )
                  }
                </div>
              </div>
            </div>
            <div className="mobile-list__card_body">
              <div className="mobile-list__card_body-left" style={{ width: '100%' }}>
                <PRZText text={`Created Date - ${dayjs(record?.created_at).format('DD/MM/YYYY hh:mm A')} by ${(record?.created_by_name ? `${record?.created_by_name}` : 'Unknown')}`} className="mobile-list__card_item-details" />
              </div>
            </div>
            <Divider className="mobile-list__card_divider" />
            <div className="mobile-list__card_footer">
              <div className='mobile-list__card_footer-right'>
                {!record?.download_url && (
                  <Tooltip
                    title="Report is not ready for download"
                  >
                    <FontAwesomeIcon icon={faDownload} style={{ color: '#cecece' }} />
                  </Tooltip>
                )}
                {record?.download_url && (
                  <Popconfirm
                    placement="topRight"
                    title="Are you sure you want to download this report?"
                    onConfirm={() => {
                      // downloadDocument({
                      //   url: record?.download_url,
                      //   document_type: record?.report_name,
                      //   document_number: `${record?.report_name}_${dayjs().format('DD-MM-YYYY')}`,
                      //   key: uuidv4(),
                      // });
                      window.open(record?.download_url, '_blank');
                    }}
                    okText="Yes"
                    cancelText="No"
                  >
                    <div>
                      <FontAwesomeIcon icon={faDownload} style={{ color: '#2d7cf7' }} />
                    </div>
                  </Popconfirm>
                )}
              </div>
            </div>
          </div>
        )
      },
      responsive: ['xs'],
    },
    {
      title: 'Report Name',
      render: (record) => {
        const newData = { ...record.ui_component_snapshot, filtersValues: record.input_log };

        // Get the current date and time
        const currentDate = new Date();

        // Convert the record's created_at string to a Date object
        const createdAt = new Date(record?.created_at);

        // Calculate the difference in milliseconds between current date/time and created date/time
        const differenceInMilliseconds = currentDate.getTime() - createdAt.getTime();

        // If the difference is less than a minute (60,000 milliseconds), display a new tag
        const isNew = differenceInMilliseconds < 60000;

        return (
          <div>
            <div
              className={(activeTab !== 'MY_SCHEDULED_REPORTS' && activeTab !== 'ORG_SCHEDULED_REPORTS') ? 'field-name__wrapper' : ''}
              onClick={() => {
                if (record?.input_log) {
                  handleMenuItemClick(newData);
                }
              }}
            >
              <span>{record?.report_name}</span>
            </div>
            {record.is_scheduled_report && <span className="user-menu__title-beta">Scheduled</span>}
            {isNew && <span className="user-menu__title-beta">New</span>}
          </div>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
    }, {
      title: 'Scheduled At',
      render: (record) => (
        <div>
          {record?.cron_expression_readable}
        </div>
      ),
      responsive: ['sm', 'md', 'lg', 'xxl'],
      hidden: ['MY_SCHEDULED_REPORTS', 'ORG_SCHEDULED_REPORTS'].includes(activeTab),
    }, {
      title: 'Status',
      width: '140px',
      render: (record) => {
        const statusColor = {
          PENDING: 'rgb(224, 188, 67)',
          COMPLETED: '#32CD32',
          ERROR: '#FF0000',
          ACTIVE: 'rgb(45, 124, 247)',
          DISABLED: 'rgb(181, 181, 181)',
        };
        return (
          <div style={{ color: statusColor[record?.status] }} className="flex-display flex-align-c">
            {record?.status}
            &nbsp;
            {record?.status === 'PENDING' ? <LoadingOutlined /> : ''}
            {
              record?.error_message && (
                <Popover
                  content={record?.error_message}
                >
                  <FontAwesomeIcon
                    icon={faCircleInfo}
                    color="grey"
                  />
                </Popover>
              )
            }
            {
              ['MY_SCHEDULED_REPORTS', 'ORG_SCHEDULED_REPORTS'].includes(activeTab) && (
                <div className="delete-button__wrapper">
                  <Popconfirm
                    disabled={deleteScheduledReportLoading}
                    title="Are you sure you want to cancel this scheduled report?"
                    onConfirm={() => {
                      deleteScheduledReport({
                        report_scheduling_id: record?.report_scheduler_id,
                      });
                    }}
                  >
                    {deleteScheduledReportLoading ? <LoadingOutlined /> : <DeleteOutlined />}
                  </Popconfirm>
                </div>
              )
            }
          </div>
        );
      },
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: 'Created Date',
      width: '120px',
      render: (text, record) => (
        <div>
          <div className="date__wrapper">
            {dayjs(record?.created_at).format('DD/MM/YYYY hh:mm A')}
          </div>
          <H3Text text={(record?.created_by_name ? `${record?.created_by_name}` : 'Unknown')} className="table-subscript" />
        </div>
      ),
      hidden: (activeTab !== 'MY_SCHEDULED_REPORTS' && activeTab !== 'ORG_SCHEDULED_REPORTS'),
      responsive: ['sm', 'md', 'lg', 'xxl'],
    },
    {
      title: '',
      render: (record) => (
        <div className="list__actions-button-wrapper-download-report-v2">
          {!record?.download_url && (
            <Tooltip
              title="Report is not ready for download"
            >
              <FontAwesomeIcon icon={faDownload} style={{ color: '#cecece' }} />
            </Tooltip>
          )}
          {record?.download_url && (
            <Popconfirm
              placement="topRight"
              title="Are you sure you want to download this report?"
              onConfirm={() => {
                // downloadDocument({
                //   url: record?.download_url,
                //   document_type: record?.report_name,
                //   document_number: `${record?.report_name}_${dayjs().format('DD-MM-YYYY')}`,
                //   key: uuidv4(),
                // });
                window.open(record?.download_url, '_blank');
              }}
              okText="Yes"
              cancelText="No"
            >
              <div>
                <FontAwesomeIcon icon={faDownload} style={{ color: '#2d7cf7' }} />
              </div>
            </Popconfirm>
          )}
          <div className="list__actions-button-ai">
            <Tooltip
              title="Coming Soon"
            >
              <button className="ai-button">
                <img className="report-v2-premium-img" src={cdnUrl('ai.png', 'images')} alt="premium" />
                <span className="ai-button-text">Summarize</span>
              </button>
            </Tooltip>
          </div>
        </div>
      ),
      responsive: ['sm', 'md', 'lg', 'xxl'],
      hidden: activeTab !== 'MY_SCHEDULED_REPORTS' && activeTab !== 'ORG_SCHEDULED_REPORTS',
    },

  ].filter((column) => column.hidden !== false);

  return (
    <Fragment>
      <Drawer
        title="Report Details"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        visible={drawerVisible}
        width={720}
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header">
            <H3Text text={selectedReport?.report_name} className="custom-drawer__title" />
            <H3Image src={cdnUrl('icon-close-blue.png', 'icons')} className="custom-drawer__close-icon" onClick={() => setDrawerVisible(false)} />
          </div>
        </div>
        <ReportFilterForm
          reportData={selectedReport}
          callback={() => {
            setDrawerVisible(false);
            setCall(true);
          }}
        />
      </Drawer>
      <div className="reports-v2__wrapper">
        <div className="ant-row custom-responsive-row">
          <div className="ant-col-md-10">
            <div className="reports-v2__wrapper-header">
              <div className="reports-v2__wrapper-header-icon">
                <PRZLottieT
                  file={'reports.json'}
                />
              </div>
              <div className="reports-v2__wrapper-header-search section-search">
                <H3Text text="Reporting Center" className="reports-v2__wrapper-header-title" />
                <H3Text text="Generate, share, schedule reports & get AI powered summaries" className="reports-v2__wrapper-header-subtitle" />
                <div className="section-search-bar__wrapper">
                  <div className="section-search-bar">
                    <input
                      value={keyword}
                      placeholder="Search report.."
                      onChange={(event) => {
                        setKeyword(event.target.value);
                      }}
                    />
                    <H3Image
                      src={cdnUrl('icon-search.png', 'icons')}
                      className="section-search-bar__icon"
                      alt="search"
                    />
                  </div>
                </div>
              </div>
            </div>
            <Menu
              defaultOpenKeys={['0']}
              mode="inline"
              className="report-list_left_side__menu"
            >
              {getReportsV2ListLoading ? (
                <React.Fragment>
                  {[...Array(4)]?.map((i, skeletonIndex) => (
                    <Skeleton key={`skeleton-${skeletonIndex}`} active style={{ marginBottom: '20px', padding: '10px' }} />
                  ))}
                </React.Fragment>
              ) : transformedData
                ?.filter((item) => item?.subList?.filter((subItem) => subItem?.report_name?.toLowerCase()?.includes(keyword?.toLowerCase()))?.length)
                ?.map((item, index) => {
                  const IconComponent = IconSwitcher(item?.icon); // Get the icon component dynamically
                  if (!IconComponent) return null; // Handle case where icon is not found
                  return (
                    <SubMenu
                      key={index}
                      icon={IconComponent}
                      title={(
                        <Fragment>
                          {item?.title}
                          &nbsp;
                          <Badge count={item?.subList?.filter((subItem) => subItem?.report_name?.toLowerCase()?.includes(keyword?.toLowerCase()))?.length} size="small" />
                        </Fragment>
                      )}
                      className="report-list_left_side__menu__sub-menu"
                    >
                      {item?.subList
                        ?.filter((subItem) => subItem?.report_name?.toLowerCase()?.includes(keyword?.toLowerCase()))
                        ?.map((subItem, subIndex) => (
                          <Menu.Item key={`${index}-${subIndex}`} onClick={() => handleMenuItemClick(subItem)}>
                            <div className="flex-display flex-align-c">
                              <div>
                                <H3Text text={subItem.report_name} />
                                <H3Text text={subItem?.report_description} className="report-list_left_side__menu__sub-menu-desc" />
                              </div>
                              <FontAwesomeIcon icon={faCircleChevronRight} />
                            </div>
                          </Menu.Item>
                        ))}
                    </SubMenu>
                  );
                })}
              {
                !getReportsV2ListLoading && transformedData && transformedData
                  ?.filter((item) => item?.subList?.filter((subItem) => subItem?.report_name?.toLowerCase()?.includes(keyword?.toLowerCase()))?.length)?.length <= 0 && (
                  <div className="report-list_left_side__menu-empty">
                    <Empty />
                  </div>
                )
              }
            </Menu>
          </div>
          <div className="ant-col-md-14">
            <div className="report-list_right_side">
              <div>
                <Tabs activeKey={activeTab} onChange={handleTabChange}>
                  <TabPane tab="My Reports" key="MY_REPORTS" />
                  {user?.org_id && (
                    <TabPane tab="All Reports" key="ORG_REPORTS" />
                  )}
                  {(user?.tenant_info?.global_config?.settings?.enable_report_scheduling) && (
                    <TabPane
                      tab={(
                        <span>
                          My Scheduled Reports
                          {' '}
                          <FontAwesomeIcon icon={faClockRotateLeft} />
                        </span>
                      )}
                      key="MY_SCHEDULED_REPORTS"
                    />
                  )}
                  {(user?.org_id && user?.tenant_info?.global_config?.settings?.enable_report_scheduling) && (
                    <TabPane
                      tab={(
                        <span>
                          All Scheduled Reports
                          {' '}
                          <FontAwesomeIcon icon={faClockRotateLeft} />
                        </span>
                      )}
                      key="ORG_SCHEDULED_REPORTS"
                    />
                  )}
                </Tabs>
              </div>
              <Table
                title={() => (
                  <div className="reports-table__header">
                    <div>
                      {reportsV2ListDetailData?.page_information?.total_record}
                      &nbsp;
                      Reports
                      &nbsp;
                      {getReportsV2ListDetailLoading && reportsV2ListDetailData?.result && (
                        <LoadingOutlined color="rgb(22, 119, 255)" />
                      )}
                    </div>
                    <div className="report-header__selector-wrapper">
                      {reportsV2ListDetailData?.filters?.map((filter) => (
                        <div key={filter?.label} className="report-header__sub-header-selector-wrapper">
                          <PRZSelect
                            className="report-value-selector"
                            value={selectedFilters[filter?.payload_key_name] || null}
                            onChange={(value) => handleFilterChange(filter?.payload_key_name, value)}
                            placeholder={`Select ${filter?.label}`}
                            allowClear
                          >
                            {filter?.dropdown_values?.map((option) => (
                              <Option key={option?.value} value={option?.value}>
                                {option?.name}
                              </Option>
                            ))}
                          </PRZSelect>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                dataSource={reportsV2ListDetailData?.result}
                columns={reportColumns}
                loading={(getReportsV2ListDetailLoading && !reportsV2ListDetailData?.result) || deleteScheduledReportLoading}
                size="small"
                scroll={{ y: 'calc(100dvh - 272px)' }}
                pagination={{
                  total: reportsV2ListDetailData?.page_information?.total_record,
                  pageSize: reportsV2ListDetailData?.page_information?.page_size,
                  current: reportsV2ListDetailData?.page_information?.page_number,
                  pageSize: limit,
                  onChange: (page, pageSize) => {
                    setPageNumber(page);
                    setLimit(pageSize);
                  },
                  showSizeChanger: true,
                  pageSizeOptions: [10, 25, 50, 100],
                  showTotal: (total, range) => `Showing ${range[0]}-${range[1]} of ${total} entries`,
                  position: ['bottomLeft'],
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

const mapStateToProps = ({ UserReducers, ReportV2Reducers }) => ({
  user: UserReducers.user,
  getReportsV2ListLoading: ReportV2Reducers.getReportsV2ListLoading,
  reportsV2ListData: ReportV2Reducers.reportsV2ListData,
  reportsV2ListDetailData: ReportV2Reducers.reportsV2ListDetailData,
  getReportsV2ListDetailLoading: ReportV2Reducers.getReportsV2ListDetailLoading,
  deleteScheduledReportLoading: ReportV2Reducers.deleteScheduledReportLoading,
});
const mapDispatchToProps = (dispatch) => ({
  getReportsV2List: (params, callback) => dispatch(ReportV2Actions.getReportsV2List(params, callback)),
  getReportsV2ListDetail: (params, callback) => dispatch(ReportV2Actions.getReportsV2ListDetail(params, callback)),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  deleteScheduledReport: (payload, callback) => dispatch(ReportV2Actions.deleteScheduledReport(payload, callback)),
});

ReportsListV2.propTypes = {
  user: PropTypes.object,
  getReportsV2List: PropTypes.func,
  getReportsV2ListLoading: PropTypes.bool,
  reportsV2ListData: PropTypes.array,
  reportsV2ListDetailData: PropTypes.array,
  getReportsV2ListDetail: PropTypes.func,
  getReportsV2ListDetailLoading: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ReportsListV2));
