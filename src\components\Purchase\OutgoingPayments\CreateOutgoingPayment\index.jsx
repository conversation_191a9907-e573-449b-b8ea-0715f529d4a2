import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { DatePicker, Select, Upload, Checkbox, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import Decimal from 'decimal.js';

// Constants & Helpers
import Constants, { toISTDate } from '@Apis/constants';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import helpers from '@Apis/helpers';

// UI Components
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import PRZSelect from '../../../Common/UI/PRZSelect';
import PRZButton from '@Components/Common/UI/PRZButton';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';

// Common Components
import CustomFieldV3 from '@Components/Common/CustomFieldV3';

// Actions
import SellerActions from '@Actions/sellerActions';
import PaymentOutgoingActions from '@Actions/paymentOutgoingActions';
import GRNActions from '@Actions/grnActions';
import PaymentRequestActions from '@Actions/paymentRequestActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import TenantActions from '@Actions/tenantActions';
import WorkflowActions from '@Actions/workflowActions';
import PayoutActions from '@Actions/payoutActions';
import PaymentModeActions from '@Actions/configurations/paymentModeAction';

// Services / Modules
import { GetPurchaseOrders } from '@Modules/purchase/purchaseOrder';
import { GetAPInvoiceList } from '../../../../modules/purchase/accountPayableInvoice';
import { cdnUrl } from '@Utils/cdnHelper';

// Current File Imports
import CreateOutgoingPaymentLines from './OutgoingPaymentLines';
import LinkDocuments from './LinkDocuments';

// Styles
import './style.scss';

const { Option } = Select;

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

class CreateOutgoingPayment extends Component {

  constructor(props) {
    super(props);
    this.state = {
      paymentDate: dayjs(),
      selectedTenantId: '',
      selectedPoValue: '',
      selectedPo: '',
      selectedPaymentMode: '',
      showLinkDocDrawer: false,
    };
  }

  static getDerivedStateFromProps(props, state) {
    const { getPaymentMode } = props;

    if (!props?.paymentModeResults?.length) {
      getPaymentMode('NOT_NUll');
    }

    if (props.cfV2DocVendorPayout && !state.isUserReadyOne) {
      return {
        ...state,
        cfVendorPayoutDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocVendorPayout?.data?.document_custom_fields, true) || [],
        isUserReadyOne: true,
      };
    }
    return state;
  }

  componentDidMount() {
    const {
      getGRNSuccess, getDocCFV2Success, getVendorWorkflows, getSellers, user, getDocCFV2,
    } = this.props;
    getDocCFV2Success(null);
    getGRNSuccess(null);
    getVendorWorkflows();
    getSellers('', user?.tenant_info?.tenant_id, 1, 30, '', () => {}, true);
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'VENDOR_PAYOUT',
    };
    getDocCFV2(payload);
  }

  handleAddPayment(paymentRequest) {
    this.setState({ formSubmitted: true });
    const {
      createAndLinkPayment, createPaymentsOutgoing, history, user, purchaseOrdersV2,
    } = this.props;
    const {
      currentSelectedSeller, amount, utrNumber, paymentDate, remarks, fileList, paymentType, cfVendorPayoutDoc, selectedTenantId, selectedPo, selectedPaymentMode, data
    } = this.state;

    function thePaymentHistory() {
      // Assuming purchaseOrdersV2 is an array
      const matchingOrder = purchaseOrdersV2?.purchase_order?.find((order) => order?.po_id === selectedPo?.poId);

      if (matchingOrder) {
        const sum = matchingOrder?.po_billing_details?.total_grn_amount ? matchingOrder?.po_billing_details?.total_advance_amount + matchingOrder?.po_billing_details?.total_grn_amount : matchingOrder?.advance_amount;
        return sum;
      }

      // Return a default value or handle the case when there is no matching order
      return 0;
    }

    let allPaymentsUnderAmount;
    // check if all GRNs have valid payment amount
    for (let i = 0; i < data?.length; i++) {
      if (Number(data[i]?.payment) > (data[i]?.grand_total_amount) - (data[i]?.payment_made) || Number(data[i]?.payment) > Number(amount)) {
        allPaymentsUnderAmount = true;
      }
    }
    // if all validations are clear, start payment request creation process
    if (currentSelectedSeller && amount && paymentDate && !allPaymentsUnderAmount && selectedPaymentMode && !cfVendorPayoutDoc?.filter((customField) => customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
      const grnDataLines = data?.filter((record) => record.payment).map((item) => ({
        [item?.entity_type]: item.entity_id,
        amount: item.payment,
      }));
      const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

      const entityDetails = isApInvoiceEnabled ? 'ap_invoice_details' : 'grn_details';
      // if GRN payments are also there, we will create and link payment
      if (paymentType === 'GRN_PAYMENT') {
        // check if the payment is an online payment request
        if (paymentRequest) {
          // get PICE payment mode ID and save it in paymentMode
          const payload = {
            payment_details: [
              {
                amount,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                payment_date: paymentDate,
                tenant_seller_id: currentSelectedSeller,
                attachments: fileList?.map((attachment) => ({
                  url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
                })) || [],
                remarks,
                approval_status: 'DRAFT',
                payment_status: 'PENDING',
              },
            ],
            [entityDetails]: grnDataLines,
            custom_fields: CustomFieldHelpers.postCfStructure(cfVendorPayoutDoc),
          };
          createAndLinkPayment(payload, () => {
            // if payment status is already PAYMENT_SUCCESS, we do not need to send payment for approval
            // now handled by backend
            history.push('/purchase/vendor-payouts');
          });
        } else {
          // in the case where it is not an online payment, we will set the value of CASH payment account in paymentMode
          const payload = {
            payment_details: [
              {
                amount,
                tenant_id: selectedTenantId,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                payment_date: paymentDate,
                tenant_seller_id: currentSelectedSeller,
                attachments: fileList?.map((attachment) => ({
                  url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
                })) || [],
                remarks,
                approval_status: 'ISSUED',
                payment_status: 'PAYMENT_SUCCESS',

              },
            ],
            [entityDetails]: grnDataLines,
            custom_fields: CustomFieldHelpers.postCfStructure(cfVendorPayoutDoc),
          };
          createAndLinkPayment(payload, () => {
            history.push('/purchase/vendor-payouts');
          });
        }
      } else if (paymentRequest && paymentType !== 'GRN_PAYMENT') {
        // if GRN lines are not there and payment request has to be PICE
        const payload = [
          {
            amount,
            tenant_id: selectedTenantId,
            paid_through_account_id: selectedPaymentMode,
            utr_number: utrNumber,
            payment_date: paymentDate,
            tenant_seller_id: currentSelectedSeller,
            attachments: fileList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            remarks,
            approval_status: 'DRAFT',
            payment_status: 'PENDING',
            custom_fields: CustomFieldHelpers.postCfStructure(cfVendorPayoutDoc),
          },
        ];

        createPaymentsOutgoing(payload, (payment) => {
          this.setState({ selectedPayment: payment?.payments?.[0] });
          history.push('/purchase/vendor-payouts');
        });
      } else if (!paymentRequest && paymentType !== 'GRN_PAYMENT') {
        if ((((selectedPo?.poGrandTotal * selectedPo?.conversionRate) - thePaymentHistory()) <= 0) || (amount > ((selectedPo?.poGrandTotal * selectedPo?.conversionRate) - thePaymentHistory()))) {
          notification.error({
            message: 'Payment amount exceeds the total amount due',
            placement: 'top',
            duration: 4,
          });
        } else {
          const payload = [
            {
              amount,
              entity_id: selectedPo?.poId || null,
              entity_name: selectedPo?.poId ? 'PURCHASE_ORDER' : null,
              payment_name: 'Vendor Advance',
              tenant_id: selectedTenantId,
              paid_through_account_id: selectedPaymentMode,
              utr_number: utrNumber,
              payment_date: paymentDate,
              tenant_seller_id: currentSelectedSeller,
              attachments: fileList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              remarks,
              approval_status: 'ISSUED',
              payment_status: 'PAYMENT_SUCCESS',
              custom_fields: CustomFieldHelpers.postCfStructure(cfVendorPayoutDoc),
            },
          ];
          createPaymentsOutgoing(payload, () => {
            history.push('/purchase/vendor-payouts');
          });
        }
      }
    }
  }
  checkMaxAmount(Id) {
    const { data, amount } = this.state;
    let totalAmount = 0;
    if (Id) {
      data?.filter((i) => i?.id !== Id)?.map((item) => totalAmount += (Number(item?.payment)));
    } else {
      data?.map((item) => totalAmount += (Number(item?.payment)));
    }
    return { totalAmount, remainingAmount: amount - totalAmount, paymentExceed: totalAmount < amount };
  }

  resetAllPayments() {
    const { data } = this.state;
    const copyData = data?.map((item) => ({
      ...item,
      payment: 0,
    }));
    this.setState({
      data: copyData,
    });
  }

  customInputChange(fieldValue, cfId) {
    const { cfVendorPayoutDoc } = this.state;
    const newCustomField = cfVendorPayoutDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfVendorPayoutDoc: newCustomField,
    });
  }

  handleDelete = (entityId) => {
    const { data } = this.state;
    const copyData = structuredClone(data)?.filter((item) => item?.entity_id !== entityId);
    const amountDue = copyData?.reduce(
      (acc, item) => acc.plus(new Decimal(item?.due_amount ?? 0)),
      new Decimal(0)
    )?.toNumber();
    this.setState({ data: copyData, amount: amountDue });
  };

  render() {
    const {
      currentSelectedSeller, formSubmitted, amount, utrNumber, paymentDate,
      data, remarks, fileList, paymentType, selectedVendor, fullAmountSelected,
      cfVendorPayoutDoc, selectedPoValue, selectedPo, selectedPaymentMode,
      showLinkDocDrawer,
    } = this.state;
    const {
      getSellers, sellers, user, createAndLinkPaymentLoading, getGRNLoading, getPurchaseOrdersV2, createPaymentsOutgoingLoading, updatePaymentOutgoingStatusLoading,
      MONEY_SYMBOL, MONEY, purchaseOrdersV2, paymentModeResults, getAPInvoiceSuccess, getGRNSuccess,
    } = this.props;
    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
    // Parse once, use multiple times

    const amountDue = data?.reduce(
      (acc, item) => acc.plus(new Decimal(item?.due_amount ?? 0)),
      new Decimal(0)
    )?.toNumber();

    function thePaymentHistory() {
      // Assuming purchaseOrdersV2 is an array
      const matchingOrder = purchaseOrdersV2?.purchase_order?.find((order) => order?.po_id === selectedPo?.poId);

      if (matchingOrder) {
        const sum = matchingOrder?.po_billing_details?.total_grn_amount ? matchingOrder?.po_billing_details?.total_advance_amount + matchingOrder?.po_billing_details?.total_grn_amount : matchingOrder?.advance_amount;
        return sum;
      }

      // Return a default value or handle the case when there is no matching order
      return 0;
    }

    const precision = user?.tenant_info?.global_config?.settings?.price_precision || 2;

    return (
      <Fragment>
        <div className='form__wrapper form-component' style={{ paddingTop: '90px' }}>
          <div className="ant-row">
            <div className="ant-col-md-24">
              <div className='form__section'>
                <div className='flex-display flex-align-c mg-bottom-5 pd-right-15'>
                  <H3Text text="PART A" className="form__section-title" />
                  <div className="form__section-line" />
                </div>
                <div className='form__section-inputs mg-bottom-20'>
                  <div className='ant-row'>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Select Vendor" required className="form__input-row__label" />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !selectedVendor ? 'ant_selector_with_error' : ''}`}>
                          <PRZSelect
                            filterOption={false}
                            showSearch
                            value={selectedVendor}
                            onChange={(vendor) => {

                              const value = JSON.parse(vendor);
                              const docCf = CustomFieldHelpers.postCfStructure(cfVendorPayoutDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
                              const vendorCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
                              const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, vendorCf) || [];

                              this.setState({
                                selectedTenantId: JSON.parse(vendor)?.tenant_id,
                                currentSelectedSeller: JSON.parse(vendor)?.tenant_seller_id,
                                cfVendorPayoutDoc: mergedCf,
                                paymentType: null,
                                data: [],
                                amount: 0,
                                fullAmountSelected: false,
                              });
                              getAPInvoiceSuccess(null);
                              getGRNSuccess(null);
                              getSellers('', user?.tenant_info?.tenant_id, 1, 30, JSON.parse(vendor)?.seller_id, (sellersData) => {
                                this.setState({ selectedVendor: JSON.stringify(sellersData?.sellers?.[0]), });
                              }, true);
                              getPurchaseOrdersV2({
                                query: {
                                  tenant_id: user?.tenant_info?.tenant_id,
                                  tenant_seller_id: JSON.parse(vendor)?.tenant_seller_id,
                                  status: 'ISSUED',
                                  payment_status: 'PARTIALLY_PAID,NOT_PAID',
                                },
                              });
                            }}
                            onSearch={(value) => {
                              getSellers(value, user?.tenant_info?.tenant_id, 1, 30, '', () => {}, true);
                            }}
                            style={{
                              width: '100%',
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                            }}
                            disabled={(createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                            status={formSubmitted && !currentSelectedSeller ? 'error' : ''}
                          >
                            {sellers && sellers?.sellers && sellers.sellers
                              .map((item) => (
                                <Option key={item.tenant_seller_id} value={JSON.stringify(item)}>
                                  <div className="seller-info">
                                    <span className="internal-seller-code">
                                      {`#${item?.seller_info?.internal_slr_code || ''}`}
                                    </span>
                                    <span className="seller-name">
                                      {` - ${item?.seller_info?.seller_name}${item?.seller_info?.office_address_details ? ` (${item?.seller_info?.office_address_details?.city})` : ''}`}
                                    </span>
                                  </div>
                                </Option>
                              ))}
                          </PRZSelect>
                          {formSubmitted && !currentSelectedSeller && <div className="input-error">Please select a vendor*</div>}
                        </div>
                      </div>
                    </div>
                    <div className='ant-col-md-6'>
                      <div className="form__input-row">
                        <H3Text text="Payment Type" required className="form__input-row__label" />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !paymentType ? 'ant_selector_with_error' : ''}`}>
                          <PRZSelect
                            filterOption={false}
                            value={paymentType}
                            onChange={(val) => this.setState({ paymentType: val, selectedPo: '', amount: 0, data: [], })}
                            status={formSubmitted && !paymentType ? 'error' : ''}
                            disabled={!currentSelectedSeller || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                          >
                            <Option key="VENDOR_ADVANCE" value="VENDOR_ADVANCE">
                              Vendor Advance
                            </Option>
                            <Option key="GRN_PAYMENT" value="GRN_PAYMENT">
                              {isApInvoiceEnabled ? 'AP Invoice Payment' : 'GRN Payment'}
                            </Option>
                          </PRZSelect>
                          {formSubmitted && !paymentType && <div className="input-error">Please select a payment type*</div>}
                        </div>
                      </div>
                    </div>
                    <div className='ant-col-md-6'>
                      <div className="form__input-row">
                        <H3Text text="Payment Account" required className="form__input-row__label" />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !selectedPaymentMode ? 'ant_selector_with_error' : ''}`}>
                          <PRZSelect
                            filterOption={false}
                            value={selectedPaymentMode}
                            onChange={(val) => this.setState({ selectedPaymentMode: val })}
                            status={formSubmitted && !selectedPaymentMode ? 'error' : ''}
                            disabled={!currentSelectedSeller || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                          >
                            {paymentModeResults?.map((item) => (
                              <Option key={item?.account_id} value={item?.account_id}>
                                {item?.account_name?.split('_')?.join(' ')?.toUpperCase()}
                              </Option>
                            ))}
                          </PRZSelect>
                          {formSubmitted && !selectedPaymentMode && <div className="input-error">Please select a payment account*</div>}
                        </div>
                      </div>
                    </div>
                    {paymentType === 'VENDOR_ADVANCE' && (
                      <div className='ant-col-md-6'>
                        <div className="form__input-row">
                          <H3Text text="Select Order" className="form__input-row__label" />
                          <div className="orgInputContainer form__input-row__input">
                            <PRZSelect
                              placeholder="Select purchase order.."
                              filterOption={false}
                              value={selectedPoValue}
                              onChange={(val) => {
                                this.setState({ selectedPoValue: val });
                                this.setState({ selectedPo: JSON.parse(val) });
                              }}
                              disabled={(createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                            >
                              {purchaseOrdersV2 && purchaseOrdersV2?.purchase_order
                                .map((item) => (
                                  <Option key={item.po_id} value={JSON.stringify({ poId: item.po_id, poGrandTotal: item.po_grand_total, conversionRate: item?.conversion_rate })}>
                                    {`#${item.po_number} (${toISTDate(item.created_date).format('DD/MM/YYYY')}) - ${MONEY((item.po_grand_total), item?.org_currency_info?.currency_code)}`}
                                  </Option>
                                ))}
                            </PRZSelect>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="ant-col-md-6">
                      <div className="form__input-row " style={{ marginBottom: '15px' }}>
                        <H3Text text="Payment Amount" required className="form__input-row__label" />
                        <div className="payment-amount__wrapper-outer form__input-row__input">
                          <div className="payment-amount__wrapper">
                            <H3Text text={MONEY_SYMBOL} className="payment-amount__currency" />
                            <H3FormInput
                              name="payment amount"
                              type="number"
                              disabled={
                                !currentSelectedSeller
                                || fullAmountSelected
                                || createAndLinkPaymentLoading
                                || createPaymentsOutgoingLoading
                              }
                              containerClassName="payment-amount__wrapper-outer "
                              labelClassName="orgFormLabel"
                              inputClassName={`orgFormInput input ${formSubmitted && !amount ? 'form__input-row__input-error' : ''}`}
                              placeholder=""
                              onChange={(event) => {
                                this.resetAllPayments();
                                this.setState({ amount: helpers.trimDecimalsAsNumber(Number(event.target.value), precision) });
                              }}
                              value={amount}
                            />
                          </div>
                          {formSubmitted && !amount && <div className="input-error">Please enter payment amount*</div>}
                          {selectedVendor && (
                            (paymentType === 'GRN_PAYMENT') ? (
                              <Checkbox
                                onChange={(event) => {
                                  if (event?.target.checked) {
                                    const dataCopy = data ? structuredClone(data) : [];

                                    for (let i = 0; i < dataCopy?.length; i++) {
                                      dataCopy[i].payment = (dataCopy[i]?.grand_total_amount) - (dataCopy[i]?.payment_made) - dataCopy[i]?.debit_note_info?.reduce((acc, dn) => acc + dn?.db_total, 0);
                                    }
                                    this.setState({
                                      amount: amountDue,
                                      fullAmountSelected: event?.target.checked,
                                      data: dataCopy,
                                    });
                                  } else {
                                    this.setState({
                                      fullAmountSelected: event?.target.checked,
                                    });
                                    this.resetAllPayments();
                                  }
                                }}
                                disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                                checked={fullAmountSelected}
                                className="payment-amount__checkbox"
                              >
                                Pay total due amount (
                                {MONEY(amountDue)}
                                )
                              </Checkbox>
                            ) : (
                              <div
                                className="payment-amount__checkbox"
                              >
                                Total due amount
                                {' '}
                                {selectedPo ? MONEY(Math.max((selectedPo?.poGrandTotal * selectedPo?.conversionRate) - thePaymentHistory(), 0)) : MONEY(0)}
                                {(amount > ((selectedPo?.poGrandTotal * selectedPo?.conversionRate) - thePaymentHistory())) && (
                                  <div className="input-error">
                                    Payment amount exceeds the total amount due.
                                  </div>
                                )}
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row orgInputContainer">
                        <H3Text text="Payment Date" required className="form__input-row__label" />
                        <div className={`form__input-row__input ${formSubmitted && !paymentDate ? 'form__input-row__input-error' : ''}`}>
                          <DatePicker
                            value={paymentDate}
                            onChange={(value) => {
                              this.setState({ paymentDate: value });
                            }}
                            style={{
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                              height: '28px',
                              padding: '1px 3px',
                              width: '100%',
                              background: 'white',
                            }}
                            disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}

                          />
                          {formSubmitted && !paymentDate && <div className="input-error">Please select a payment date*</div>}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row orgInputContainer">
                        <H3Text text="Reference #" className="form__input-row__label" />
                        <H3FormInput
                          name="payment reference number"
                          type="text"
                          containerClassName=" form__input-row__input orgInputContainer"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput input"
                          placeholder=""
                          onChange={(event) => this.setState({ utrNumber: event.target.value })}
                          value={utrNumber}
                          disabled={!currentSelectedSeller || createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                        />
                      </div>
                    </div>
                    {cfVendorPayoutDoc?.length > 0
                      && (
                        <CustomFieldV3
                          customFields={cfVendorPayoutDoc || []}
                          formSubmitted={formSubmitted}
                          customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                          disableCase={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
                          hideTitle
                          wrapperClassName="ant-col-md-6"
                          containerClassName="form__input-row"
                          labelClassName="form__input-row__label"
                          inputClassName="form__input-row__input"
                          errorClassName="form__input-row__input-error"
                        />
                      )}
                  </div>
                </div>
              </div>
              <div className="form__section">
                <div className='flex-display flex-align-c mg-bottom-5 pd-right-15'>
                  <H3Text text="PART B" className="form__section-title" />
                  <div className="form__section-line" />
                </div>
                <div className='form__section-inputs mg-bottom-20'>
                  <div className='ant-row'>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Internal Remark" className="form__input-row__label" />
                        <textarea
                          className="orgFormInput form__input-row__input"
                          value={remarks}
                          onChange={(e) => this.setState({ remarks: e.target.value })}
                          disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                          style={{
                            resize: 'none',
                            backgroundColor: 'rgb(239, 239, 239)',
                            border: 'none',
                            borderRadius: '4px',
                            marginRight: '15px',
                            height: '103px',
                            padding: '4px 8px',
                          }}
                        />
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text=" Attachment(s)" className="form__input-row__label" />
                        <div style={{ marginLeft: '0px' }}>
                          <Upload
                            action={Constants.UPLOAD_FILE}
                            listType="picture-card"
                            fileList={fileList}
                            onPreview={this.handlePreview}
                            onChange={(fileListData) => this.setState({ fileList: fileListData.fileList })}
                            disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                            multiple
                            limit={10}
                          >
                            {fileList?.length >= 20 ? null : uploadButton}
                          </Upload>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className='form__lines-wrapper'>
            {paymentType === 'GRN_PAYMENT' && data?.length > 0 && (
              <CreateOutgoingPaymentLines
                data={data}
                updateData={(updatedData) => {
                  this.setState({ data: updatedData });
                }}
                loading={createAndLinkPaymentLoading || getGRNLoading}
                checkMaxAmount={(id) => this.checkMaxAmount(id)}
                amount={amount}
                MONEY={MONEY}
                showLinkDocButton={data?.length > 0}
                showLinkDocDrawer={() => this.setState({ showLinkDocDrawer: true })}
                isApInvoiceEnabled={isApInvoiceEnabled}
                handleDelete={this.handleDelete}
              />
            )}
            {
              paymentType === 'GRN_PAYMENT' && data?.length === 0 && (
                <div className="doc__no-doc-wrapper">
                  <div className="doc__no-doc">
                    <img className="doc__no-doc__image" src={isApInvoiceEnabled ? cdnUrl('NoAPInvoice.png', 'images') : cdnUrl('NoGrn.png', 'images')} alt="bundle" />
                    <H3Text className="doc__no-doc__text" text={`No ${isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'} linked. Click 'Link ${isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}' to proceed.`} />
                    <PRZButton
                      className="doc__no-doc__button hide__in-mobile"
                      onClick={() => this.setState({ showLinkDocDrawer: true })}
                    >
                      {`Link ${isApInvoiceEnabled ? 'AP Invoices' : 'GRNs'}`}
                    </PRZButton>
                  </div>
                </div>
              )
            }
          </div>
          <div className='form__data-wrapper'>
            <div className='ant-row'>
              <div className="ant-col-md-24">
                <div className='form-section'>
                  <div className='form__section-inputs mg-bottom-20'>
                    <div className='ant-row'>
                      <div className="ant-col-md-6" />
                      <div className="ant-col-md-6" />
                      <div className="ant-col-md-6" />
                      {paymentType === 'GRN_PAYMENT' && (
                        <div className="ant-col-md-6">
                          <div className="purchase-payment-totals">
                            <div className="purchase-payment__field">
                              <H3Text text="Amount Used" className="purchase-payment__field-name" />
                              <H3Text
                                text={MONEY(this.checkMaxAmount()?.totalAmount || '0')}
                                className="purchase-payment__field-value"
                              />
                            </div>
                            <div className="purchase-payment__field">
                              <H3Text text="Amount Remaining" className="purchase-payment__field-name" />
                              <H3Text
                                text={MONEY(this.checkMaxAmount()?.remainingAmount || '0')}
                                className="purchase-payment__field-value"
                              />
                            </div>
                            <div className="purchase-payment__field grn-total_field-total">
                              <H3Text text="Amount in Excess" className="purchase-payment__field-name" />
                              <H3Text
                                text={MONEY((amount - this.checkMaxAmount()?.totalAmount) || '0')}
                                className="purchase-payment__field-value  "
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="form__footer">
            <H3Button
              text="Record Payment"
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              style={{
                padding: '7px 10px',
                marginRight: '10px',
                minWidth: '130px',
              }}
              onClick={() => {
                if (!createAndLinkPaymentLoading) {
                  this.handleAddPayment(false);
                }
              }}
              isLoading={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
              disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading || (paymentType === 'GRN_PAYMENT' && data?.length === 0)}
            />
          </div>
        </div>
        <PRZDrawer
          open={showLinkDocDrawer}
          width={800}
          onClose={() => this.setState({ showLinkDocDrawer: false })}
          headerWidth={750}
          title={'Link Document(s) to this payment'}
        >
          <div className="custom-drawer__header-wrapper">
            <LinkDocuments
              vendor={JSON.parse(selectedVendor ?? '{}')}
              callback={() => this.setState({ showLinkDocDrawer: false })}
              setDocuementData={(data) => this.setState({ data })}
              documentData={data}
            />
          </div>
        </PRZDrawer>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers, SellerReducers, GRNReducers, PaymentOutgoingReducers, GetAPInvoiceList,
  TenantReducers, WorkflowsReducers, CFV2Reducers, PaymentModeReducers, GetPurchaseOrders
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  MONEY_SYMBOL: UserReducers.MONEY_SYMBOL,
  sellers: SellerReducers.sellers,
  getGRNLoading: GRNReducers.getGRNLoading,
  grnData: GRNReducers.grnData,
  createPaymentsOutgoingLoading: PaymentOutgoingReducers.createPaymentsOutgoingLoading,
  createAndLinkPaymentLoading: PaymentOutgoingReducers.createAndLinkPaymentLoading,
  updatePaymentOutgoingStatusLoading: PaymentOutgoingReducers.updatePaymentOutgoingStatusLoading,
  tenantsConfiguration: TenantReducers.tenantsConfiguration,
  vendorWorkflows: WorkflowsReducers.vendorWorkflows,
  selectedPaymentOutgoing: PaymentOutgoingReducers.selectedPaymentOutgoing,
  environment: UserReducers.environment,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocVendorPayout: CFV2Reducers.cfV2DocVendorPayout,
  paymentModeResults: PaymentModeReducers.paymentModeResults,
  purchaseOrdersV2: GetPurchaseOrders.data,
  selectedAPInvoices: GetAPInvoiceList.data,
});
const mapDispatchToProps = (dispatch) => ({
  getSellers: (keyword, tenantId, page, limit, sellerId, callback, isActive) => dispatch(SellerActions.getSellers(keyword, tenantId, page, limit, sellerId, callback, isActive)),
  getGRN: (tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId, selectedEntityType, searchKeyword, sellerId) => dispatch(GRNActions.getGRN(tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId, selectedEntityType, searchKeyword, sellerId)),
  getGRNSuccess: (grnData) => dispatch(GRNActions.getGRNSuccess(grnData)),
  createAndLinkPayment: (payload, callback) => dispatch(PaymentOutgoingActions.createAndLinkPayment(payload, callback)),
  createPaymentsOutgoing: (payload, callback) => dispatch(PaymentOutgoingActions.createPaymentsOutgoing(payload, callback)),
  createPaymentRequest: (payload, callback) => dispatch(PaymentRequestActions.createPaymentRequest(payload, callback)),
  updatePaymentOutgoingStatus: (payload, callback) => dispatch(PaymentOutgoingActions.updatePaymentOutgoingStatus(payload, callback)),
  getTenantsConfiguration: (tenantId) => dispatch(TenantActions.getTenantsConfiguration(tenantId)),
  getVendorWorkflows: () => dispatch(WorkflowActions.getVendorWorkflows()),
  getPaymentOutgoingById: (tenantId, paymentId, callback) => dispatch(PaymentOutgoingActions.getPaymentOutgoingById(tenantId, paymentId, callback)),
  createPaymentSession: (payload, callback) => dispatch(PayoutActions.createPaymentSession(payload, callback)),
  getDocCFV2: (orgId, entityType, cfId, page, limit) => dispatch(CFV2Actions.getDocCFV2(orgId, entityType, cfId, page, limit)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getPaymentMode: (accountType, callback) => dispatch(PaymentModeActions.getPaymentMode(accountType, callback)),
  getPurchaseOrdersV2: (payload, callback) => dispatch(GetPurchaseOrders.actions.request(payload, callback)),
  getAPInvoice: (payload, callback) => dispatch(GetAPInvoiceList.actions.request(payload, callback)),
  getAPInvoiceSuccess: (payload) => dispatch(GetAPInvoiceList.actions.success(payload)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CreateOutgoingPayment));
