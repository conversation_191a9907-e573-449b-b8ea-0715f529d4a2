/* eslint-disable no-param-reassign */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-expressions */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Table, Button, DatePicker, Select, Drawer, notification, Checkbox } from 'antd';
import dayjs from 'dayjs';
import { EditFilled, LoadingOutlined } from '@ant-design/icons';
import { QUANTITY } from '@Apis/constants';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import H3Image from '@Uilib/h3Image';
import { cdnUrl } from '@Utils/cdnHelper';
import Helpers from '@Apis/helpers';
import SelectTax from '@Components/Admin/Common/SelectTax';
import './style.scss';
import H3Text from '@Uilib/h3Text';
import SelectDepartment from '@Components/Common/SelectDepartment';
import H3FormInput from '@Uilib/h3FormInput';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import HideValue from '../../../../../Common/RestrictedAccess/HideValue';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import RestrictedAccessMessage from '../../../../../Common/RestrictedAccess/RestrictedAccessMessage';
import PRZSelect from '../../../../../Common/UI/PRZSelect';
import DocumentNumberSeqInput from '../../../../../Admin/Common/DocumentNumberSeqInput';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';

/**
 * @param value
 */

const { Option } = Select;

class PRFromMaterialShortage extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    const { priceMasking } = props;
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
    this.state = {
      formSubmitted: false,
    };
  }


  getColumns(tenantSellerId) {
    const { isDataMaskingPolicyEnable, cfPurchaseOrdersLine, visibleColumns, isHideCostPrice, updateSellerInfo, user } = this.props;
    const { actionType } = this.state;
    const columns = [
      {
        title: '',
        width: '60px',
        fixed: 'left',
        render: (text, record) => (
          record?.product_sku_info?.assets?.[0]?.url
            ? (
              <H3Image
                src={record?.product_sku_info?.assets[0].url}
                className="prod-table__image"
                style={{ width: '40px', height: '40px' }}
              />
            )
            : <H3Image src={cdnUrl("imageDefault.png", "icons")} className="prod-table__image prod-table__image__default" style={{ width: '40px', height: '40px' }} />
        ),
      },
      {
        title: 'PRODUCT',
        width: '220px',
        fixed: 'left',
        render: (record) => <div style={{ width: '220px' }}>{record?.product_sku_name}</div>,
      },
      {
        title: 'QUANTITY',
        width: '12%',
        render: (text, record) => {
          const { formSubmitted } = this.state;
          const { updateSellerInfo } = this.props;
          return (
            <H3FormInput
              value={record.quantity}
              name="Quantity"
              type="number"
              containerClassName={`orgInputContainer ${(formSubmitted && Number(record.quantity) <= 0) ? 'pr-selection__input-error' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                updateSellerInfo(record?.selected_sku_offer?.tenant_seller_id, 'quantity', QUANTITY(Number(e.target.value), record?.uom_info?.[0]?.precision), (record?.key));
              }}
              required
              noDecimal
              showError={actionType && !record?.quantity}
              disabled={!user?.tenant_info?.permission?.editable_purchase_order_qty}
            />
          );
        },
      },
      {
        title: 'Unit',
        width: '15%',
        render: (text, record) => {
          const { updateSellerInfo } = this.props;
          return (
            <div className="orgInputContainer">
              <PRZSelect
                dropdownAlign={{ offset: [-6, 4] }}
                filterOption={false}
                className=""
                value={record?.uomId}
                onChange={(e) => {
                  updateSellerInfo(record?.selected_sku_offer?.tenant_seller_id, 'uomId', e, (record?.key));
                }}
              >
                {
                  record?.product_uom_list?.map((uom) => (
                    <Option key={uom?.uom_id} value={uom?.uom_id}>{`${uom?.uqc?.toProperCase()} (${uom?.uom_name?.toProperCase()})`}</Option>
                  ))
                }
              </PRZSelect>
            </div>
          );
        },
        hidden: !this.props.poFromMrpScreen,
      },
      {
        title: 'UNIT PRICE',
        width: '12%',
        render: (text, record) => {
          const { formSubmitted } = this.state;
          const { updateSellerInfo } = this.props;
          return (
            <H3FormInput
              value={record.unitPrice}
              name="Unit Price"
              type="number"
              // containerClassName={`orgInputContainer ${(formSubmitted && !Number(record.quantity)) ? 'pr-selection__input-error' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => updateSellerInfo(record?.selected_sku_offer?.tenant_seller_id, 'unitPrice', Number(e.target.value), (record?.key))}
              required
              noDecimal
              hideInput={isDataMaskingPolicyEnable && isHideCostPrice}
              popOverMessage={"You don't have access to view unit price"}
              showError={actionType && !record?.unitPrice}
            />
          );
        },
      },
      {
        title: 'TAX',
        width: '12%',
        render: (item, record) => {
          const { formSubmitted } = this.state;
          const { updateSellerInfo, taxesGroupData, user } = this.props;
          const isVendorOverseas = record?.selected_sku_offer?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
          return isVendorOverseas ? '-' : (
            <SelectTax
              onChange={(value) => {
                updateSellerInfo(record?.selected_sku_offer?.tenant_seller_id, 'tax', value, (record?.key));
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              selectedGST={record.tax_id}
              customStyle={{
                border: (formSubmitted && !record.tax_id) ? ' 1px solid #ff4d4f' : 'none',
              }}
            />
          );
        },
      },
      {
        title: 'TOTAL',
        width: '12%',
        fixed: 'right',
        render: (record) => ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view total price"} /> : this.props.MONEY(this.getLineTotal(record) || '0')),
      },
    ].filter((item) => !item.hidden)

    if (cfPurchaseOrdersLine?.length) {
      columns.splice(3, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfPurchaseOrdersLine, visibleColumns, (value, key) => {
        updateSellerInfo(tenantSellerId, 'lineCustomFields', value, key)
      }, actionType, '', '', '', '', '', '', '', true));
    }
    return columns;
  }


  /**
   * @param withApproval
   */

  areAllQuantitiesPositive(data) {
    for (const outerArray of data) {
      for (const item of outerArray) {
        if ('quantity' in item) {
          if (item.quantity <= 0) {
            return false;
          }
        }
      }
    }
    return true;
  }


  handleCreatePo(withApproval) {
    const { tenantSellerPRItems, callback } = this.props;
    const {
      user, createPurchaseOrder, history, tenantsConfiguration, selectedPurchaseRequest, poFromMrpScreen, taxesGroupData, getTaxesLoading,
    } = this.props;
    const { formSubmitted } = this.state;
    this.setState({ formSubmitted: true });
    const cartItemGroup = Object.values(tenantSellerPRItems);
    const payload = [];
    const vendorAddressMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.seller_address_required;

    for (let i = 0; i < cartItemGroup?.length; i++) {
      if (cartItemGroup?.[i]?.[0]?.checkedRecipients && !cartItemGroup?.[i]?.[0]?.email_id?.length) {
        notification.error({
          placement: 'top',
          message: 'Please enter email id of recipients',
          duration: 4,
        });
        return;
      }
      if (!cartItemGroup?.[i]?.[0]?.vendorAddress?.address_id && vendorAddressMandatory) {
        notification.error({
          placement: 'top',
          message: 'Please enter vendor address',
          duration: 4,
        });
        return;
      }
      if (!cartItemGroup?.[i]?.[0]?.billingAddress?.address_id) {
        notification.error({
          placement: 'top',
          message: 'Please enter billing address',
          duration: 4,
        });
        return;
      }
      const purchaseOrder = {};
      const isVendorOverseas = cartItemGroup?.[i]?.[0]?.selected_sku_offer?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
      purchaseOrder.tenant_department_id = cartItemGroup?.[i]?.[0]?.tenant_department_id;
      purchaseOrder.tenant_seller_id = cartItemGroup?.[i]?.[0]?.selected_sku_offer?.tenant_seller_id;
      purchaseOrder.tenant_seller_info = {
        ...cartItemGroup?.[i]?.[0]?.selected_sku_offer,
        gst_number: cartItemGroup?.[i]?.[0]?.gst_number,
      };
      purchaseOrder.mrp_id = selectedPurchaseRequest?.mrp_id;
      purchaseOrder.tenant_id = selectedPurchaseRequest?.tenant_id || user?.tenant_info?.tenant_id;
      purchaseOrder.shipping_address_id = selectedPurchaseRequest?.tenant_info?.default_shipping_address;
      // purchaseOrder.billing_address_id = selectedPurchaseRequest?.tenant_info?.default_billing_address;
      purchaseOrder.seller_address_id = cartItemGroup?.[i]?.[0]?.vendorAddress?.address_id;
      purchaseOrder.seller_address = cartItemGroup?.[i]?.[0]?.vendorAddress;
      purchaseOrder.billing_address_id = cartItemGroup?.[i]?.[0]?.billingAddress?.address_id;
      purchaseOrder.billing_address = cartItemGroup?.[i]?.[0]?.billingAddress;
      purchaseOrder.payment_terms = [{
        advance_amount: 0,
        due_days: cartItemGroup?.[i]?.[0]?.payment_terms,
        remark: '',
      }];
      purchaseOrder.is_po_automatic_notification_enabled = cartItemGroup?.[i]?.[0]?.sendWhatsappNotification || false;
      purchaseOrder.notification_recipients = cartItemGroup?.[i]?.[0]?.email_id || [];
      purchaseOrder.is_automatic_notification_enabled = cartItemGroup?.[i]?.[0]?.checkedRecipients || false;
      purchaseOrder.po_date = dayjs(cartItemGroup?.[i]?.[0]?.po_date).format('YYYY/MM/DD');
      purchaseOrder.seq_id = cartItemGroup?.[i]?.[0]?.docSeqId;
      purchaseOrder.custom_fields = CustomFieldHelpers.postCfStructure(cartItemGroup?.[i]?.[0]?.documentCustomFields),
        purchaseOrder.edd = dayjs(cartItemGroup?.[i]?.[0]?.delivery_date).format('YYYY/MM/DD');
      purchaseOrder.po_expiry_date = dayjs(cartItemGroup?.[i]?.[0]?.expiry_date);
      purchaseOrder.t_and_c = tenantsConfiguration?.data?.[0]?.default_t_and_c || '';
      purchaseOrder.status = withApproval ? 'ISSUED' : 'DRAFT';
      purchaseOrder.purchase_order_lines = cartItemGroup?.[i]?.map((item) => ({
        remark: item?.remark || null,
        tenant_product_id: item?.tenant_product_id,
        product_sku_name: item?.product_sku_name,
        product_sku_id: item?.product_sku_id,
        offer_price: item?.unitPrice,
        quantity: Number(item?.quantity),
        tax_id: isVendorOverseas ? taxesGroupData?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
        uom_id: poFromMrpScreen ? item?.uomId : item?.uom_id,
        group_id: item?.group_id,
        custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
        uom_info: poFromMrpScreen ? Array.isArray(item?.uomInfo) ? item?.uomInfo : [item?.uomInfo] : Array.isArray(item?.uom_info) ? item?.uom_info : [item?.uom_info],
        tax_group_info: isVendorOverseas ? taxesGroupData?.data?.find((tax) => tax?.tax_value === 0) : item?.taxInfo,
      }));
      payload.push(purchaseOrder);
    }
    if (this.areAllQuantitiesPositive(cartItemGroup)) {
      createPurchaseOrder(payload, () => {
        this.setState({ actionType: null });
        this.setState({ formSubmitted: false });
        callback();
      }, false);
    }
  }

  /**
   * @return
   * @param record
   */
  getLineTotal(record) {
    const {
      selectedPurchaseRequest,
    } = this.props;
    let total = 0;
    if (record.quantity && record.unitPrice && record?.taxInfo) {
      const qyt = QUANTITY(Number(record.quantity), record?.uom_info?.[0]?.precision);
      const taxableValue = (qyt * record.unitPrice) * (record.discount ? Number(100 - record.discount) / 100 : 1);
      total = Helpers.computeTaxation(taxableValue, record.taxInfo, selectedPurchaseRequest?.tenant_info?.default_billing_address_info?.state, record?.selected_sku_offer?.office_address_details?.state)?.taxed_value;
    }
    return total;
  }

  /**
   *
   * @return
   */

  render() {
    const {
      tenantSellerPRItems, createPurchaseOrderLoading, selectedPurchaseRequest, updateSellerInfo, user, isDataValid, taxesGroupData,
    } = this.props;
    const { actionType, formSubmitted } = this.state;
    const vendorAddressMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.seller_address_required;
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';
    return (
      <div className="pr-verify-container">
        <div className="pr-verify-container-heading">
          {Object.keys(tenantSellerPRItems)?.length}
          {' '}
          Purchase orders will be created as part of this order
        </div>
        {Object.values(tenantSellerPRItems)?.map((row) => (
          <React.Fragment>
            <Table
              title={() => (
                <div className="pr-verify-title">
                  <div>
                    {`${row?.[0]?.selected_sku_offer?.internal_slr_code} - ${row?.[0]?.selected_sku_offer?.seller_name}`}
                  </div>
                  <div className="ant-row">
                    <DocumentNumberSeqInput
                      valueFromProps={row?.[0]?.poNumber}
                      setInitialDocSeqNumber={(value) => { }}
                      entityName="PURCHASE_ORDER"
                      docSeqId={row?.[0]?.docSeqId}
                      tenantId={selectedPurchaseRequest?.tenant_info?.tenant_id}
                      onChangeFromProps={(event, newValue, seqId) => {
                        const updates = {
                          poNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                          docSeqId: seqId,
                        };
                        updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'poNumber', updates, row?.[0]?.key);
                      }}
                      docTitle="Purchase Order#"
                      rowClassName='ant-col-md-8'
                      inputRowClassName="create-po__input-row"
                      inputWrapperClassName="create-po__input-row__input"
                      labelClassName="create-po__input-row__label"
                      seqClassName='sqn__selector'
                      prefixRequiredOnly
                      preventApiCall
                    />
                    <div className="ant-col-md-8">
                      <div className="create-po__input-row">
                        <H3Text text="Department" className="create-po__input-row__label" />
                        <div className="create-po__input-row__input">
                          <SelectDepartment
                            hideTitle
                            tenantId={selectedPurchaseRequest?.tenant_info?.tenant_id}
                            selectedDepartment={row?.[0]?.tenant_department_id}
                            noDropdownAlign
                            onChange={(value) => {
                              updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'tenant_department_id', value?.tenant_department_id, row?.[0]?.key);
                            }}
                            tenentLevelDepartment
                            emptyNotAllowed
                            loading={createPurchaseOrderLoading}
                            disabled={createPurchaseOrderLoading}
                            labelClassName="mo-form__input-row__label"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <div className="create-po__input-row">
                        <H3Text
                          text="GST Number"
                          className="create-po__input-row__label"
                        />
                        <H3FormInput
                          name="GST Number"
                          type="text"
                          disabled={createPurchaseOrderLoading || accountingGSTTransactionAsPerMaster}
                          containerClassName="orgInputContainer create-po__input-row__input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput input"
                          placeholder=""
                          onChange={(event) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'gst_number', event?.target?.value, row?.[0]?.key)}
                          value={row?.[0]?.gst_number}
                        />
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <div className="create-po__input-row">
                        <div className="create-po__input-row__label">
                          PO Date
                          <span style={{ color: 'red' }}>{'  *'}</span>
                        </div>
                        <div className="create-po__input-row__input">
                          <DatePicker
                            value={dayjs(row?.[0]?.po_date)}
                            allowClear={false}
                            onChange={(value) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'po_date', value, row?.[0]?.key)}
                            format="DD-MM-YYYY"
                            style={{
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                              height: '28px',
                              padding: '1px 7px',
                              width: '100%',
                              background: 'white',
                              marginBottom:
                                !row?.[0]?.po_date ? '0px' : '10px',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <div className="create-po__input-row">
                        <H3Text
                          text="Delivery Date"
                          className="create-po__input-row__label"
                        />
                        <div className="create-po__input-row__input">
                          <DatePicker
                            value={dayjs(row?.[0]?.delivery_date)}
                            allowClear={false}
                            onChange={(value) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'delivery_date', value, row?.[0]?.key)}
                            format="DD-MM-YYYY"
                            style={{
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                              height: '28px',
                              padding: '1px 7px',
                              width: '100%',
                              background: 'white',
                              marginBottom: !row?.[0]?.delivery_date ? '0px' : '10px',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <div className="create-po__input-row">
                        <H3Text text="Expiry Date" className="create-po__input-row__label" />
                        <div className="create-po__input-row__input">
                          <DatePicker
                            allowClear={false}
                            value={dayjs(row?.[0]?.expiry_date)}
                            onChange={(value) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'expiry_date', value, row?.[0]?.key)}
                            format="DD-MM-YYYY"
                            disabledDate={(current) => current && current < dayjs().startOf('day')}
                            style={{
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                              height: '28px',
                              padding: '1px 7px',
                              width: '100%',
                              background: 'white',
                              marginBottom: '10px',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <SelectPaymentTerm
                        selectedPaymentTerm={row?.[0]?.payment_terms}
                        onChange={(value) => {
                          updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'payment_terms', value?.due_days, row?.[0]?.key);
                        }}
                        callback={(value) => {
                          updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'payment_terms', Number(value), row?.[0]?.key);
                        }}
                        containerClassName="orgInputContainer create-po__input-row"
                        inputClassName=" create-po-seller__selector create-po__input-row__input"
                        labelClassName="create-po__input-row__label"
                        showError={!row?.[0]?.payment_terms}
                        disabled={
                          createPurchaseOrderLoading
                        }
                        showAddPaymentTerm
                        placeholder="Select Payment Term"
                      />
                    </div>
                    <CustomFieldV3
                      customFields={row?.[0]?.documentCustomFields}
                      formSubmitted={actionType}
                      customInputChange={(value, cfId) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'documentCustomFields', value, row?.[0]?.key, cfId)}
                      wrapperClassName="ant-col-md-8"
                      containerClassName="orgInputContainer create-po__input-row__input"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput input"
                      errorClassName="form__input-row__input-error"
                      disableCase={
                        createPurchaseOrderLoading
                      }
                      hideTitle
                      isCarryForward
                    />
                  </div>
                  <div
                    className="form__wrapper"
                    style={{ padding: '0px' }}
                  >
                    <div className="form__section">
                      <div className="form__section-inputs">
                        <div className="ant-row">
                          <div className="ant-col-md-12">
                            <div className="form__input-row">
                              <H3Text
                                text="Vendor's Address"
                                className="create-po__input-row__label"
                                required={vendorAddressMandatory}
                              />
                              <div className={`form__input-row__address__wrapper ${(formSubmitted && vendorAddressMandatory && !row?.[0]?.vendorAddress) ? 'form__input-row__address-error' : ''}`}>
                                <div className="form__input-row__address">
                                  {row?.[0]?.vendorAddress && (
                                    <div className="form__input-row__address-info"
                                      style={{
                                        color: 'rgb(58, 58, 58)'
                                      }}
                                    >
                                      <div className="form__input-row__address-l1">
                                        {row?.[0]?.vendorAddress?.address1}
                                      </div>
                                      <div className="form__input-row__address-l2">
                                        {`${row?.[0]?.vendorAddress?.city}, ${row?.[0]?.vendorAddress?.state}, ${row?.[0]?.vendorAddress?.postal_code}, ${row?.[0]?.vendorAddress?.country}`}
                                      </div>
                                    </div>
                                  )}
                                  {!row?.[0]?.vendorAddress && (
                                    <H3Text
                                      text="Select address.."
                                      className="form__input-row__address-placeholder"
                                    />
                                  )}
                                  <div
                                    className="form__input-row__address-icon"
                                    onClick={() => {
                                      updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'selectedAddressType', 'SELLER', row?.[0]?.key);
                                    }}
                                  >
                                    <EditFilled />
                                    {' '}
                                    Update
                                  </div>
                                </div>
                                {formSubmitted && !row?.[0]?.vendorAddress && vendorAddressMandatory && (
                                  <div className="input-error">
                                    *Please select vendor address
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="ant-col-md-12">
                            <div className="form__input-row">
                              <H3Text
                                text="Billing Address"
                                className="create-po__input-row__label"
                                required
                              />
                              <div className={`form__input-row__address__wrapper ${(formSubmitted && !row?.[0]?.billingAddress) ? 'form__input-row__address-error' : ''}`}>
                                <div className="form__input-row__address">
                                  {row?.[0]?.billingAddress && (
                                    <div className="form__input-row__address-info"
                                      style={{
                                        color: 'rgb(58, 58, 58)'
                                      }}
                                    >
                                      <div className="form__input-row__address-l1">
                                        {row?.[0]?.billingAddress?.address1}
                                      </div>
                                      <div className="form__input-row__address-l2">
                                        {`${row?.[0]?.billingAddress?.city}, ${row?.[0]?.billingAddress?.state}, ${row?.[0]?.billingAddress?.postal_code}, ${row?.[0]?.billingAddress?.country}`}
                                      </div>
                                    </div>
                                  )}
                                  {!row?.[0]?.billingAddress && (
                                    <H3Text
                                      text="Select address.."
                                      className="form__input-row__address-placeholder"
                                    />
                                  )}
                                  <div
                                    className="form__input-row__address-icon"
                                    onClick={() => {
                                      updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'selectedAddressType', 'TENANT_BILLING', row?.[0]?.key);

                                    }}
                                  >
                                    <EditFilled />
                                    {' '}
                                    Update
                                  </div>
                                </div>
                                {formSubmitted && !row?.[0]?.billingAddress && (
                                  <div className="input-error">
                                    *Please select billing address
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="ant-col-md-24">
                            {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && (
                              <div className="form__input-row" style={{ marginTop: '10px' }}>
                                <Checkbox
                                  checked={row?.[0]?.sendWhatsappNotification}
                                  onChange={() => {
                                    updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'sendWhatsappNotification', !row?.[0]?.sendWhatsappNotification, row?.[0]?.key);
                                  }}
                                />
                                <span
                                  style={{
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    marginLeft: '5px',
                                    color: ' rgb(58, 58, 58)',
                                  }}
                                >
                                  Send automatic whatsapp message when order is Issued
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="ant-col-md-24">
                            <div className="create-po__input-row__input">
                              <div>
                                <Checkbox
                                  checked={row?.[0]?.checkedRecipients}
                                  onChange={() => {
                                    updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'checkedRecipients', !row?.[0]?.checkedRecipients, row?.[0]?.key);
                                  }}
                                />
                                <span
                                  style={{
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    marginLeft: '5px',
                                    color: ' rgb(58, 58, 58)',
                                    marginBottom: '10px',
                                  }}
                                >
                                  Send automatic email when order is issued
                                </span>
                              </div>
                              {row?.[0]?.checkedRecipients && (
                                <div>
                                  <PRZSelect
                                    mode="tags"
                                    value={row?.[0]?.email_id}
                                    filterOption={false}
                                    maxTagCount="responsive"
                                    onChange={(value) => {
                                      const recipients = [];
                                      for (let i = 0; i < value?.length; i++) {
                                        if (Helpers.validateEmail(value[i])) {
                                          recipients.push(value[i]);
                                        }
                                      }
                                      updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'email_id', recipients, row?.[0]?.key);
                                    }}
                                    className={`create-po__input-row__input__email-input ${formSubmitted && !row?.[0]?.email_id?.length > 0 ? 'prz-select__error-border' : ''}`}
                                    style={{
                                      width: '51%',
                                      fontSize: '13px',
                                    }}
                                    placeholder=""
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              columns={this.getColumns(row?.[0]?.selected_sku_offer?.tenant_seller_id)}
              size="small"
              dataSource={row}
              key={row?.[0]?.sellerInfo?.seller_id}
              pagination={false}
              scroll={{ x: 'max-content' }}
            />
            <Drawer
              open={row?.[0]?.showAddressDrawer}
              onClose={(event) => updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'showAddressDrawer', false, row?.[0]?.key)}
              width="360"
              destroyOnClose
            >
              <AddressSelector
                title={row?.[0]?.selectedAddressType === 'TENANT_BILLING' ? 'Billing Address' : 'Vendor Address'}
                addressType={row?.[0]?.selectedAddressType?.split('_')[0]}
                selectedAddressId={row?.[0]?.selectedAddressType === 'TENANT_BILLING' ? row?.[0]?.billingAddress?.address_id : row?.[0]?.vendorAddress?.address_id}
                onAddressChange={(address) => {
                  if (row?.[0]?.selectedAddressType === 'TENANT_BILLING') {
                    updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'billingAddress', address, row?.[0]?.key);
                  } else {
                    updateSellerInfo(row?.[0]?.selected_sku_offer?.tenant_seller_id, 'vendorAddress', address, row?.[0]?.key);
                  }
                }}
                entityId={row?.[0]?.selectedAddressType === 'TENANT_BILLING' ? selectedPurchaseRequest?.tenant_info?.tenant_id : row?.[0]?.seller_id}
                entityType={row?.[0]?.selectedAddressType === 'SELLER' ? 'SELLER' : 'TENANT'}
                seller={row?.[0]?.selected_sku_offer}
                tenantId={selectedPurchaseRequest?.tenant_info?.tenant_id}
              />
            </Drawer>
          </React.Fragment>
        ))}
        <div className="custom-drawer__footer" style={{ width: 'calc(100% - 60px)' }}>
          <Button
            type="primary"
            onClick={() => {
              this.setState({ actionType: 'DRAFT' });
              // if (isDataValid()) {
              this.handleCreatePo(false);
              // }
            }}
            style={{ width: '150px' }}
            disabled={createPurchaseOrderLoading}
          >
            {(createPurchaseOrderLoading && actionType === 'DRAFT') ? <LoadingOutlined /> : 'Save as Draft'}
          </Button>
          <Button
            type="primary"
            onClick={() => {
              this.setState({ actionType: 'ISSUED' });
              if (isDataValid()) {
                this.handleCreatePo(true);
              }
            }}
            style={{ width: '150px', marginLeft: '10px' }}
            disabled={createPurchaseOrderLoading}
          >
            {(createPurchaseOrderLoading && actionType === 'ISSUED') ? <LoadingOutlined /> : 'Save and Issue'}
          </Button>
          {!user?.tenant_info?.permission?.editable_purchase_order_qty && (
            <RestrictedAccessMessage message={'You do not have access to edit Quantity of Products on this page'} />
          )}
        </div>
      </div>
    );
  }
}
const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, TenantReducers, CFV2Reducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  createPurchaseOrderLoading: PurchaseOrderReducers.createPurchaseOrderLoading,
  tenantsConfiguration: TenantReducers.tenantsConfiguration,
  priceMasking: UserReducers.priceMasking,
  cfV2DocPurchaseOrders: CFV2Reducers.cfV2DocPurchaseOrders,
});

const mapDispatchToProps = (dispatch) => ({
  createPurchaseOrder: (payload, callback, withApproval) => dispatch(PurchaseOrderActions.createPurchaseOrder(payload, callback, withApproval)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
});

PRFromMaterialShortage.propTypes = {
  user: PropTypes.any,
  createPurchaseOrder: PropTypes.func,
  createPurchaseOrderLoading: PropTypes.bool,
  tenantSellerPRItems: PropTypes.any,
  history: PropTypes.func,
  tenantsConfiguration: PropTypes.any,
  selectedPurchaseRequest: PropTypes.any,
  MONEY: PropTypes.any,
  updateSellerInfo: PropTypes.func,
  callback: PropTypes.func,

};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(PRFromMaterialShortage));
