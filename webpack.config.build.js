const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const PreloadWebpackPlugin = require('@vue/preload-webpack-plugin');
const BrotliPlugin = require('brotli-webpack-plugin');

require('dotenv').config();

module.exports = {
  mode: 'production',

  entry: path.resolve(__dirname, 'src/index.tsx'),

  output: {
    filename: '[name].[contenthash].js',
    path: path.resolve(__dirname, 'build'),
    publicPath: '/',
    clean: true,
  },

  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
    cacheDirectory: path.resolve(__dirname, 'node_modules/.cache/webpack'),
  },

  resolve: {
    alias: {
      '@Actions': path.resolve(__dirname, 'src/actions'),
      '@Components': path.resolve(__dirname, 'src/components'),
      // '@Images': path.resolve(__dirname, 'src/media/images'),
      '@Pages': path.resolve(__dirname, 'src/pages'),
      '@Reducers': path.resolve(__dirname, 'src/reducers'),
      '@Routes': path.resolve(__dirname, 'src/routes'),
      '@Sagas': path.resolve(__dirname, 'src/sagas'),
      '@Services': path.resolve(__dirname, 'src/services'),
      '@Store': path.resolve(__dirname, 'src/store'),
      '@Uilib': path.resolve(__dirname, 'src/uilib'),
      '@Apis': path.resolve(__dirname, 'src/apis'),
      '@Helpers': path.resolve(__dirname, 'src/helpers'),
      '@Hooks': path.resolve(__dirname, 'src/hooks'),
      '@Utils': path.resolve(__dirname, 'src/utils'),
      '@Modules': path.resolve(__dirname, 'src/modules'),
      'process/browser': require.resolve('process/browser'),
    },
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.scss', '.css', '.json'],
    fallback: {
      crypto: false,
      path: require.resolve('path-browserify'),
      stream: require.resolve('stream-browserify'),
      buffer: require.resolve('buffer/'),
      util: require.resolve('util/'),
      process: require.resolve('process/browser'),
    },
  },

  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'thread-loader',
            options: {
              workers: require('os').cpus().length - 1,
            },
          },
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
              happyPackMode: true,
            },
          },
        ],
      },
      {
        test: /\.(js|jsx)$/i,
        exclude: /node_modules\/(?!(uuid|\@tanstack))/,
        use: [
          {
            loader: 'thread-loader',
            options: {
              workers: Math.max(require('os').cpus().length - 1, 2),
              poolTimeout: 2000,
            },
          },
          {
            loader: 'babel-loader',
            options: {
              presets: [
                [
                  '@babel/preset-env',
                  {
                    targets: '> 1%', // Less aggressive for faster compilation
                    modules: false,
                    useBuiltIns: 'usage',
                    corejs: 3,
                    loose: true, // Faster compilation
                    bugfixes: true,
                    exclude: ['transform-typeof-symbol'] // Exclude unnecessary transforms
                  },
                ],
                [
                  '@babel/preset-react',
                  {
                    runtime: 'automatic', // New JSX transform
                    development: false,
                  },
                ],
              ],
              plugins: [
                '@babel/plugin-transform-class-properties',
                '@babel/plugin-transform-optional-chaining',
                '@babel/plugin-transform-nullish-coalescing-operator',
                [
                  '@babel/plugin-transform-runtime',
                  {
                    helpers: false, // Reduce bundle size
                    regenerator: false,
                  },
                ],
                '@babel/plugin-transform-private-methods',
                '@babel/plugin-transform-private-property-in-object',
              ],
              // Enable caching
              cacheDirectory: true,
              cacheCompression: false,
            },
          },
        ],
      },
      {
        test: /\.s[ac]ss$/i,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'sass-loader',
        ],
      },
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
        ],
      },
      {
        test: /\.(png|jpg|gif|svg|jpeg|webp)$/i,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 16384,
            },
          },
        ],
      },
      {
        test: /\.(woff(2)?|ttf|eot|mp3|svg)(\?v=\d+\.\d+\.\d+)?$/,
        use: [
          {
            loader: 'file-loader',
            options: {
              name: '[name].[ext]',
              outputPath: 'assets/fonts',
            },
          },
        ],
      },
      {
        test: /src[\/\\]media[\/\\]/,
        use: 'ignore-loader',
      },
    ],
  },

  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        extractComments: false,
        parallel: true,
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
          output: {
            comments: false,
          },
        },
      }),
      new CssMinimizerPlugin(),
    ],
    splitChunks: {
      chunks: 'all',
      maxSize: 1048576, // 1MB max chunk size
      cacheGroups: {
        // Combine smaller vendor chunks
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          minSize: 100000, // Only create chunk if > 100KB
          priority: 10
        },
        // Common code
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          minSize: 50000,
          priority: 5
        }
      }
    }
  },

  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),

    new HtmlWebpackPlugin({
      template: path.resolve(__dirname, 'src/index.html'),
      filename: 'index.html',
    }),

    new MiniCssExtractPlugin({
      filename: '[name].[contenthash].css',
      ignoreOrder: true,
    }),

    new PreloadWebpackPlugin({
      rel: 'preload',
      include: 'initial',
      as: (entry) => {
        if (/\.css$/.test(entry)) return 'style';
        if (/\.woff2?$/.test(entry)) return 'font';
        return 'script';
      },
    }),

    new CopyWebpackPlugin({
      patterns: [
        { from: 'favicon.ico', to: '' },
      ],
    }),

    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
    }),

    new BrotliPlugin({
      asset: '[path].br[query]',
      test: /\.(js|css|html|svg)$/,
      threshold: 8192,
      minRatio: 0.8,
    }),

    ...(process.env.ANALYZE === 'true' ? [
      new BundleAnalyzerPlugin({
        analyzerMode: 'static',
        openAnalyzer: true, // Change to true to see analysis
        reportFilename: 'bundle-report.html'
      }),
    ] : []),
  ],

  performance: {
    maxAssetSize: 2048000,
    maxEntrypointSize: 10485760,
  },

  devtool: 'source-map',
};