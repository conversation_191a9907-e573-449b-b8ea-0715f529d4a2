import React, { Fragment } from 'react';
import { Link } from 'react-router-dom';

// Ant Design Components
import { Popconfirm, Table } from 'antd';

// Constants & Helpers
import { toISTDate } from '@Apis/constants';

// UI Components
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import PRZButton from '@Components/Common/UI/PRZButton';
import { CloseOutlined } from '@ant-design/icons';

const CreateOutgoingPaymentLines = ({
  data,
  MONEY,
  amount,
  updateData,
  loading,
  checkMaxAmount,
  showLinkDocButton,
  showLinkDocDrawer,
  isApInvoiceEnabled,
  handleDelete,
}) => {
  const columns = [
    {
      title: 'REFERENCE #',
      render: (text, record) => {
        return (
          <Link
            to={record?.source === 'grn' ? `/purchase/goods-receiving/view/${record?.entity_id}` : `/purchase/account-payable-invoice/view/${record?.entity_id}`}
            target="_blank"
          >
            {record?.entity_number}
          </Link>
        );
      },
    },
    {
      title: 'CREATED TIME',
      render: (text, record) => (
        <div>
          {toISTDate(record.created_at).format('DD/MM/YYYY')}
          <H3Text
            text={
              record?.created_by_info
                ? `${record?.created_by_info.first_name || ''} ${record?.created_by_info.last_name || ''}`
                : 'Unknown'
            }
            className="table-subscript"
          />
        </div>
      ),
    },
    {
      title: 'DATE',
      render: (text, record) =>
        `${record?.date_time
          ? toISTDate(record?.date_time)?.format('DD/MM/YYYY')
          : ''
        }`,
    },
    {
      title: 'TOTAL AMOUNT',
      dataIndex: 'seller_name',
      render: (text, record) => MONEY(record?.grand_total_amount),
    },
    {
      title: 'AMOUNT DUE',
      dataIndex: 'seller_name',
      render: (text, record) =>
        MONEY(
          record?.grand_total_amount - record?.payment_made -
          record?.debit_note_info?.reduce((acc, dn) => acc + dn?.db_total, 0)
        ),
    },
    {
      title: 'PAYMENT',
      render: (text, record) => {
        return (
          <div style={{ width: '120px' }}>
            <H3FormInput
              value={record?.payment}
              type="number"
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                handlePaymentChange(e.target.value, record?.id);
              }}
              disabled={
                !amount ||
                (!record?.payment && !checkMaxAmount()?.paymentExceed)
              }
            />
            <H3Text
              text={<div>Pay in Full</div>}
              onClick={() =>
                checkMaxAmount()?.paymentExceed && handleFullPayment(record)
              }
              className="goods-receiving__po_no-pay"
            />
            {(Number(record?.payment) >
              Number(
                record?.grand_total_amount -
                record?.debit_note_info?.reduce(
                  (acc, dn) => acc + dn?.db_total,
                  0
                )
              ) ||
              Number(record?.payment) > Number(amount))
              && (
                <div className="input-error">Please enter valid amount*</div>
              )}
          </div>
        );
      },
    },
    {
      title: '',
      render: (text, record) => (
        <Fragment>
          <Popconfirm
            title="Are you sure you want to remove this invoice line?"
            onConfirm={() => handleDelete(record.entity_id)}
            okText="Yes"
            cancelText="No"
          >
            <div className="delete-line-button">
              <CloseOutlined />
            </div>
          </Popconfirm>
        </Fragment>
      ),
      width: '40px',
    },
  ];
  function handlePaymentChange(value, id) {
    const updatedData = JSON.parse(JSON.stringify(data));

    for (let i = 0; i < updatedData.length; i++) {
      if (updatedData[i]?.id === id) {
        updatedData[i].payment = Number(value);
        break;
      }
    }
    updateData(updatedData);
  }

  function handleFullPayment(record) {
    const copyData = data?.map((item) => {
      if (item?.id === record?.id) {
        return {
          ...item,
          payment:
            checkMaxAmount(record?.id)?.remainingAmount >
              Number(
                item?.grand_total_amount -
                item?.payment_made -
                record?.debit_note_info?.reduce(
                  (acc, dn) => acc + dn?.db_total,
                  0
                )
              )
              ? (item?.grand_total_amount ||
                item?.grand_total * (item?.conversion_rate || 1)) -
              item?.payment_made -
              record?.debit_note_info?.reduce(
                (acc, dn) => acc + dn?.db_total,
                0
              )
              : checkMaxAmount(record?.id)?.remainingAmount,
        };
      }
      return {
        ...item,
      };
    });
    updateData(copyData);
  }

  return (
    <Fragment>
      <div className="purchase-payment__table">
        <Table
          title={() => (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>{(data || []).length} {isApInvoiceEnabled ? 'AP Invoices' : 'Goods Received Notes'}</span>
              {showLinkDocButton && (<PRZButton
                className="doc__no-doc__button hide__in-mobile"
                onClick={() => showLinkDocDrawer()}
              >
                Link {isApInvoiceEnabled ? 'AP Invoices' : 'GRNs'}
              </PRZButton>)}
            </div>
          )}
          size="small"
          columns={columns}
          scroll={{ y: 400 }}
          loading={loading}
          dataSource={data || []}
          pagination={false}
        />
      </div>
    </Fragment>
  );
};

export default React.memo(CreateOutgoingPaymentLines);
