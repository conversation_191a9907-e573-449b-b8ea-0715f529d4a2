import React, { useState, useEffect, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Table, Button, Drawer, Dropdown, Select, Tag } from 'antd';
import {
  PhoneOutlined,
  EditOutlined,
  MailOutlined,
  CaretDownFilled,
} from '@ant-design/icons';
import H3Text from '@Uilib/h3Text';
import SellerActions from '@Actions/sellerActions';
import H3Image from '@Uilib/h3Image';
import Helpers from '@Apis/helpers';
import Constants, { CountryCodes } from '@Apis/constants';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import { cdnUrl } from "@Utils/cdnHelper";
import TenantActions from '@Actions/tenantActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import VendorForm from '../../Common/VendorForm';
import './style.scss';
import PRZSelect from '../../Common/UI/PRZSelect';

const { Option } = Select;

const Vendors = ({
  orgSellers,
  getOrgSellersLoading,
  user,
  getOrgSellers,
  history,
  commonGetRequests,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [keyword, setKeyword] = useState('');
  const [showNewSellerModal, setShowNewSellerModal] = useState(false);
  const [showUpdateVendorModal, setShowUpdateVendorModal] = useState(false);
  const [selectedSeller, setSelectedSeller] = useState(null);
  const [mobileVendorsLength, setMobileVendorsLength] = useState(10);
  const [selectedRowKeys, setSelectedRowKeys] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isVendorArchive, setIsVendorArchive] = useState('false');

  const archiveOptions = [
    { label: 'Active', value: 'false', key: 'ARCHIVE' },
    { label: 'Archived', value: 'true', key: 'UN_ARCHIVE' },
  ];


  const productListColumns = [
    {
      title: 'VENDOR #',
      render: (text, record) => (
        <div className="column-number__wrapper">
          {record?.internal_slr_code || '-'}
        </div>
      ),
    },
    {
      title: 'NAME',
      key: 'name',
      render: (text, record) => (
        <div>
          {record?.seller_name}
          <div className="vendor-name__wrapper">
            {record?.is_marketplace_seller && (
              <div>
                <H3Text text="Marketplace" className="marketplace-status-tag" />
              </div>
            )}
            {record?.is_subcontractor && (
              <div>
                <H3Text text="Subcontractor" className="subcontractor-status-tag" />
              </div>
            )}
            {record?.is_transporter && (
              <div>
                <H3Text text="Transporter" className="transporter-status-tag" />
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'EMAIL',
      render: (text, record) => (
        <div>
          {record?.email_id_1 || '-'}
        </div>
      ),
    },
    {
      title: 'CONTACT NUMBER',
      render: (text, record) => (
        <div>
          {record?.mobile_1 ? (
            `${CountryCodes?.find((code) => code?.dial_code == record?.dial_code)?.flag} +${record?.dial_code} ${record?.mobile_1}`
          ) : '-'}
        </div>
      ),
    },
    {
      title: 'AVAILABLE STORES',
      render: (text, record) => (
        <div>
          {record?.tenant_sellers?.length || '0'}
        </div>
      ),
    },
    {
      title: '',
      key: 'action',
      render: (text, record) => (
        <div className="list__actions">
          {Helpers.getPermission(Helpers.permissionEntities.VENDORS, Helpers.permissionTypes.UPDATE, user) && selectedRows?.length === 0 && (
            <div
              className="roundIconButton"
              onClick={() => {
                if (selectedRows?.length === 0) {
                  setSelectedSeller(record);
                  setShowUpdateVendorModal(true);
                }
              }}
            >
              <EditOutlined />
            </div>
          )}
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (user) {
      getOrgSellers(user?.tenant_info?.org_id, null, '', currentPage, 10, '', isVendorArchive);
    }
  }, [user, isVendorArchive]);

  const getSellersWithKey = (sellersData) => {
    const sellersCopy = JSON.parse(JSON.stringify(sellersData));
    for (let i = 0; i < sellersCopy?.length; i++) {
      sellersCopy[i].key = i;
    }
    return sellersCopy;
  };

  return (
    <Fragment>
      <div className="doc-list__wrapper">
        <div className="ant-row">
          <div className="ant-col-xs-24">
            <div className="doc-list__header-wrapper">
              <div className="doc-list__header-wrapper__left">
                <div className="section-search">
                  <div className="doc-list__search-bar__wrapper">
                    <div className="section-search-bar">
                      <input
                        value={keyword}
                        placeholder="Search for vendors name.."
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            getOrgSellers(user?.tenant_info?.org_id, '', '', 1, 10, e.target.value, isVendorArchive);
                            setKeyword(e.target.value);
                            setCurrentPage(1);
                          }
                        }}
                        onChange={(event) => {
                          setKeyword(event.target.value);
                        }}
                      />
                      <H3Image
                        src={cdnUrl("icon-search.png", "icons")}
                        className="section-search-bar__icon"
                        alt="search"
                        onClick={() => {
                          getOrgSellers(user?.tenant_info?.org_id, '', '', 1, 10, '', isVendorArchive);
                          setKeyword(keyword);
                          setCurrentPage(1);
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="doc-list__header-wrapper__right">
                {Helpers.getPermission(Helpers.permissionEntities.VENDORS, Helpers.permissionTypes.CREATE, user) && (
                  <div className="doc-list__buttons-wrapper">
                    <H3Button
                      buttonType={defaultButtonTypes.BLUE_ROUNDED}
                      text="+ New Vendor"
                      onClick={() => {
                        setShowNewSellerModal(true);
                      }}
                      customClass="add-button"
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="header__tags">
              {keyword && keyword?.length > 0 && (
                <Tag closable onClose={() => { setKeyword(''); setCurrentPage(1); getOrgSellers(user?.tenant_info?.org_id, '', '', 1, 10, '', isVendorArchive); }}>
                  Search for
                  &nbsp;
                  <span><b>{keyword}</b></span>
                </Tag>
              )}
            </div>
            <div className="doc-list__table-wrapper">
              <Table
                title={() => (
                  <div className="doc-list__table-header">
                    <div className="doc-list__table-header__left">
                      <H3Text text={`${orgSellers?.count || '0'} Registered Vendors`} className="" />
                    </div>
                    <div className="doc-list__table-header__right">
                      {selectedRowKeys?.length > 0 && (
                        <Dropdown
                          overlay={(
                            <div className="custom-columns__sort-filter">
                              <H3Text
                                text={(isVendorArchive == 'true' || isVendorArchive == true) ? 'Activate' : 'Archive'}
                                className="header__custom-dropdown__option"
                                onClick={() => {
                                  const selectedIds = selectedRows?.map((item) => item?.seller_id);
                                  const apiUrl = (isVendorArchive === 'true' || isVendorArchive === true) ? Constants.UNARCHIVE_SELLERS : Constants.ARCHIVE_SELLERS;
                                  commonGetRequests({
                                    url: `${apiUrl}?seller_ids=${selectedIds}`,
                                  }, () => {
                                    setCurrentPage(1);
                                    setSelectedRows([]);
                                    setSelectedRowKeys(null);
                                    getOrgSellers(user?.tenant_info?.org_id, '', '', 1, 10, '', isVendorArchive);
                                  });
                                }}
                              />
                            </div>
                          )}
                          trigger={['click']}
                          placement="bottomRight"
                        >
                          <div className="doc-list__action-buttons">
                            <div className="action-button">
                              <H3Text text="Actions" className="action-button-text" />
                              <CaretDownFilled />
                            </div>
                          </div>
                        </Dropdown>
                      )}
                      <div className="filter__container filter__container-section">
                        <PRZSelect
                          value={isVendorArchive}
                          onChange={(value) => {
                            setIsVendorArchive(value);
                            setCurrentPage(1);
                            getOrgSellers(user?.tenant_info?.org_id, '', '', 1, 10, '', value);
                          }}
                          placeholder="Vendor Status"
                          style={{
                            width: '100px',
                            borderRadius: '5px',
                          }}
                          options={archiveOptions}
                        />
                      </div>
                    </div>
                  </div>
                )}
                loading={getOrgSellersLoading}
                showHeader
                size="small"
                scroll="scroll"
                columns={productListColumns}
                dataSource={getSellersWithKey(orgSellers?.sellers || [])}
                pagination={orgSellers?.count > 10 ? {
                  defaultCurrent: 1,
                  current: currentPage,
                  total: orgSellers ? orgSellers?.count : 0,
                  pageSize: 10,
                  onChange: (page) => {
                    setCurrentPage(page);
                    setSelectedRows([]);
                    setSelectedRowKeys(null);
                    getOrgSellers(user?.tenant_info?.org_id, '', '', page, 10, keyword, isVendorArchive);
                  },
                  position: ['bottomRight'],
                  showSizeChanger: false,
                } : false}
                rowSelection={(window.screen.width > 325) ? {
                  onChange: (rowKeys, rows) => {
                    setSelectedRows(rows);
                    setSelectedRowKeys(rowKeys);
                  },
                  selectedRowKeys,
                } : false}
              />
            </div>
            <div className="vendor__wrapper-mobile">
              <div className="vendor__mobile-title">
                {orgSellers?.sellers?.length || '0'}
                {' '}
                Registered Vendors
                {' '}
              </div>
              <div className="vendor__mobile-body">
                {!getOrgSellersLoading
                  ? orgSellers?.sellers?.filter((item) => item)?.splice(0, mobileVendorsLength)?.map((item) => (
                    <div key={item?.seller_id} className="vendor__mobile-card-wrapper">
                      <div className="vendor__mobile-card-title">
                        {' '}
                        {item.seller_name}
                        {' '}
                      </div>
                      <div className="vendor__mobile-card-description">
                        <div className="vendor__mobile-card-description-right">
                          <div className="vendor__mobile-card-contact">
                            <MailOutlined />
                            {` ${item.email_id_1}` ? item.email_id_1 : '-'}
                            <br />
                            <PhoneOutlined />
                            {' '}
                            {item.mobile_1 ? item.mobile_1 : ''}
                          </div>
                          <div className="vendor__mobile-card-quantity">
                            {` ${item.count}` ? `${item.count} Products` : ''}
                          </div>
                        </div>
                        <div className="vendor__mobile-card-description-left">
                          <div
                            className="vendor__mobile-card-view-button"
                            onClick={() => {
                              history.push(`/vendors/view/${item.seller_id}`);
                            }}
                          >
                            View
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                  : [1, 2, 3, 4].map((item) => (
                    <div className="ant-col-md-5 ant-col-xs-24" key={item}>
                      <div className="loadingBlock mobile__card-loader" />
                    </div>
                  ))}
                {!getOrgSellersLoading && orgSellers?.sellers?.filter((item) => item).length >= mobileVendorsLength && (
                  <div className="vendor__mobile-card-load-more-wrapper">
                    <Button
                      onClick={() => setMobileVendorsLength(mobileVendorsLength + 3)}
                      className="vendor__mobile-card-load-more-button"
                    >
                      Load More
                    </Button>
                  </div>
                )}

              </div>
            </div>
          </div>
        </div>
      </div>
      <Drawer
        open={showNewSellerModal}
        width="720px"
        mask
        onClose={() => setShowNewSellerModal(false)}
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header">
            <H3Text text="Add New Vendor" className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => setShowNewSellerModal(false)} />
          </div>
        </div>
        <div className="create-po__new-vendor-wrapper">
          <VendorForm
            callback={() => {
              getOrgSellers(user?.tenant_info?.org_id, '', '', currentPage, 10, keyword, isVendorArchive);
              setShowNewSellerModal(false);
            }}
            isAdmin
          />
        </div>
      </Drawer>
      <Drawer
        title="Update Vendor Information"
        open={showUpdateVendorModal}
        onClose={() => setShowUpdateVendorModal(false)}
        width="720px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header">
            <H3Text text="Update Vendor Information" className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => setShowUpdateVendorModal(false)} />
          </div>
        </div>
        <VendorForm
          selectedSeller={selectedSeller}
          callback={() => {
            setCurrentPage(1);
            getOrgSellers(user?.tenant_info?.org_id, '', '', currentPage, 10, keyword, isVendorArchive);
            setShowUpdateVendorModal(false);
          }}
          isAdmin
        />
      </Drawer>
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, SellerReducers,
}) => ({
  user: UserReducers.user,
  getOrgSellersLoading: SellerReducers.getOrgSellersLoading,
  orgSellers: SellerReducers.orgSellers,
});

const mapDispatchToProps = (dispatch) => ({
  getOrgSellers: (orgId, sellerId, tenantId, page, limit, sellerName, isVendorArchive) => dispatch(SellerActions.getOrgSellers(orgId, sellerId, tenantId, page, limit, sellerName, isVendorArchive)),
  getTenants: (page, keyword, isVerified, orgId, limit, callback, tenantId) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId, limit, callback, tenantId)),
  commonGetRequests: (payload, callback) => dispatch(AnalyticsActions.commonGetRequests(payload, callback)),
});

Vendors.propTypes = {
  getOrgSellers: PropTypes.func,
  getTenants: PropTypes.func,
  getOrgSellersLoading: PropTypes.bool,
  orgSellers: PropTypes.any,
  user: PropTypes.any,
  history: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Vendors));
