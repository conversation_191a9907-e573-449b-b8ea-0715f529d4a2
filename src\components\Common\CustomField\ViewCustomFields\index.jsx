import React, { Fragment, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { CheckOutlined, EditFilled } from '@ant-design/icons';
import { DatePicker, Popover, Tooltip } from 'antd';
import dayjs from 'dayjs';
import Constants, { toISTDate } from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import useCheckPortrait from '../../../../hooks/useCheckPortrait';
import './style.scss';

/**
 *
 */
const ViewCustomFields = ({
  wrapperClass, labelClass, valueClass, customFields, isHorizontal, handleCustomFieldUpdate, isEditable, entityId, entityName, callback, inputClassName, getDocCFV2, entityType, rootClass,
}) => {
  const [editingId, setEditingId] = useState(null);
  const [fieldValue, setFieldValue] = useState('');
  const [dateValue, setDateValue] = useState(null);
  const [tempCustomFields, setTempCustomFields] = useState([]);

  const {
    isMobile, isPortrait, isMobileAndPortrait, isMobileOrPortrait,
  } = useCheckPortrait();

  useEffect(() => {
    if (entityType) {
      const payload = {
        entityName: entityType,
      };
      getDocCFV2(payload, (cfData) => {
        const tempCfData = cfData?.data?.document_custom_fields?.filter((cf) => cf.is_active);
        const mergedArrayForDocCfs = tempCfData?.map((newField) => {
          const oldField = customFields?.find((old) => old.cf_id === newField.cf_id);
          return { ...newField, field_value: oldField?.field_value || newField.field_value, visible: oldField?.visible || false };
        });
        setTempCustomFields(mergedArrayForDocCfs?.filter((item) => item?.visible || !item?.is_system_field));
      });
    } else {
      setTempCustomFields(customFields?.filter((item) => !item?.is_system_field));
    }
  }, [entityType]);

  const handleIconClick = (id, value, type) => {
    setEditingId(id);
    if (type === 'DATE') {
      setDateValue(dayjs(value));
    } else {
      setFieldValue(value);
    }
  };

  const handleInputChange = (e) => {
    setFieldValue(e.target.value);
  };

  const saveChanges = (id, type, date) => {
    let updatedValue = '';
    if (type === 'ATTACHMENT') {
      const attachment = [{
        url: date?.response?.location,
        type: date?.response?.contentType,
        name: date?.response?.originalname,
      }];
      updatedValue = attachment;
    } else if (type === 'DATE') {
      updatedValue = date?.format('YYYY-MM-DD');
    } else {
      updatedValue = fieldValue;
    }

    const payload = {
      [entityName]: entityId,
      doc_custom_fields: [{
        cf_id: id,
        field_value: updatedValue || null,
      }],
    };
    handleCustomFieldUpdate(payload, () => callback(), () => {
      setFieldValue('');
      setDateValue(null);
    });
    setEditingId(null);
  };

  const handleDateChange = (date, item) => {
    setDateValue(date);
    saveChanges(item.cf_id, item.field_type, date);
  };

  const handleFileChangeEWayBill = (cfId, fieldType, event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);

      fetch(Constants.UPLOAD_FILE, {
        method: 'POST',
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          saveChanges(cfId, fieldType, data);
        })
        .catch((error) => {
          console.error('Error uploading file:', error);
        });
    }
  };

  return (
    <Fragment>
      {tempCustomFields?.filter((item) => (item?.field_value || isEditable) && (item?.field_type !== 'ATTACHMENT'))?.map((item) => (
        <div className={`${isHorizontal ? 'ant-col-md-12' : ''} ${rootClass || ''}`} key={item.cf_id}>
          <div className={`${wrapperClass} custom-field-item`}>
            {!['CHECKBOX', 'DATE', 'DROPDOWN', 'TEXTAREA'].includes(item?.field_type) && (
              <Fragment>
                <H3Text text={item?.field_name} className={labelClass} />
                {(editingId === item.cf_id) ? (
                  <div className={`${inputClassName} custom_field_edit_and_view`}>
                    <input
                      value={fieldValue}
                      onChange={handleInputChange}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          saveChanges(item.cf_id, item.field_type);
                        }
                      }}
                      className="invoice-table__remarks document-custom-input"
                      style={{
                        border: '2px solid rgb(223, 237, 248)',
                      }}
                    />
                    &nbsp;
                    <CheckOutlined
                      style={{ color: '#1890ff', cursor: 'pointer' }}
                      onClick={() => saveChanges(item.cf_id, item.field_type)}
                    />
                  </div>
                ) : (
                  <div className="custom_field_edit_and_view">
                    {item?.field_value && !!item?.field_value?.toString()?.trim()
                      ? (
                        <Tooltip
                          overlayClassName="custom-field__tooltip"
                          color="rgb(234, 242, 254)"
                          title={(
                            <div
                              className="tooltip-content"
                            >
                              {item?.field_value}
                            </div>
                          )}
                        >
                          <span>
                            <H3Text text={item?.field_value} className={`${valueClass} nowrap ellipses`} />
                          </span>
                        </Tooltip>
                      ) : ''}
                    {isEditable && item?.is_editable_on_update && (
                      <EditFilled
                        className="edit-icon"
                        onClick={() => handleIconClick(item.cf_id, item.field_value, item.field_type)}
                      />
                    )}
                  </div>
                )}
              </Fragment>
            )}
            {item?.field_type === 'CHECKBOX' && (
              <Fragment>
                <H3Text text={item?.field_name} className={labelClass} />
                <H3Text text={item?.field_value ? 'Yes' : 'No'} className={valueClass} />
              </Fragment>
            )}
            {item?.field_type === 'TEXTAREA' && (
              <Fragment>
                <H3Text text={item?.field_name} className={labelClass} />
                {item?.field_value && !!item?.field_value?.toString()?.trim()
                  ? (
                    <Tooltip
                      overlayClassName="custom-field__tooltip"
                      color="rgb(234, 242, 254)"
                      title={(
                        <div
                          className="tooltip-content"
                        >
                          {item?.field_value?.split('\n')?.map((line, index) => (
                            <div key={index}>{line}</div>
                          ))}

                        </div>
                      )}
                    >
                      <div className={`${'ellipses'} `}>
                        {item?.field_value}
                      </div>
                    </Tooltip>
                  ) : ''}
              </Fragment>
            )}
            {item?.field_type === 'DROPDOWN' && (
              <Fragment>
                <H3Text text={item?.field_name} className={labelClass} />
                <H3Text text={item?.field_value?.[0] === 'null' ? '-' : item?.field_value || '-'} className={`${valueClass} nowrap`} />
              </Fragment>
            )}
            {item?.field_type === 'DATE' && (
              <Fragment>
                <H3Text text={item?.field_name} className={labelClass} />
                {isEditable ? (
                  <div className={inputClassName}>
                    <DatePicker
                      value={item?.field_value ? dayjs(item?.field_value) : null}
                      onChange={(val) => handleDateChange(val, item)}
                      className={valueClass}
                      format="MMM DD, YYYY"
                      style={{
                        width: '145px',
                      }}
                      disabled={!item?.is_editable_on_update}
                    />
                  </div>
                ) : (
                  <div className="custom_field_edit_and_view">
                    <H3Text text={toISTDate(item?.field_value).format('DD MMM, YYYY')} className={`${valueClass} nowrap`} />
                  </div>
                )}
              </Fragment>
            )}
          </div>
        </div>
      ))}
      {tempCustomFields
        ?.filter(
          (item) => (item?.field_value || isEditable) && (item?.field_type === 'ATTACHMENT'),
        )?.map((item) => (
          <div
            className={isHorizontal ? 'ant-col-md-12' : ''}
            key={item.cf_id}
          >
            <div className={`${wrapperClass} custom-field-item`}>
              {item?.field_type === 'ATTACHMENT'
                && item?.field_value?.length > 0 && (
                  <Fragment>
                    <H3Text text={item?.field_name} className={labelClass} />
                    <div style={{ display: 'block' }} className={valueClass}>
                      {editingId === item.cf_id ? (
                        <input
                          type="file"
                          onChange={(e) => handleFileChangeEWayBill(
                            item.cf_id,
                            item.field_type,
                            e,
                          )}
                          disabled={!item?.is_editable_on_update}
                          className="document-custom-input"
                        />
                      ) : (
                        <div className="custom_field_edit_and_view_attachment">
                          <Popover
                            content={(
                              <div>
                                {item?.field_value?.map((file) => (
                                  <div
                                    key={file?.name}
                                    onClick={() => {
                                      let url = file?.url?.includes('procuzystorage.blob.core.windows.net')
                                        ? file.url.replace('https://procuzystorage.blob.core.windows.net/', Constants.CDN_URL)
                                        : file.url
                                      window.open(url)
                                    }}
                                  >
                                    <a>
                                      {file?.name?.length > 30
                                        ? `${file?.name.substring(0, 30)}...`
                                        : file?.name}
                                    </a>
                                    <br />
                                  </div>
                                ))}
                              </div>
                            )}
                            title="Attachments"
                            trigger="click"
                          >
                            <div
                              style={{
                                cursor: 'pointer',
                                background: '#2d7cf7',
                                padding: '0.5px 4px',
                                borderRadius: '3px',
                                color: 'white',
                              }}
                            >
                              {isMobile ? 'Attachments' : 'Click to View Attachments'}
                            </div>
                          </Popover>
                        </div>
                      )}
                    </div>
                  </Fragment>
                )}
            </div>
          </div>
        ))}
    </Fragment>
  );
};

const mapStateToProps = () => ({});

const mapDispatchToProps = (dispatch) => ({
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewCustomFields));
