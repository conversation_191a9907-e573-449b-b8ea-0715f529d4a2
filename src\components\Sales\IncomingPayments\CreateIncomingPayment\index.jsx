import React, { Fragment, useEffect, useState } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  DatePicker, Select, Table, Upload, Checkbox, Drawer,
  Button,
  Spin,
  Popconfirm,
  Modal,
} from 'antd';
import {
  CloseOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import Constants, { toISTDate } from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import CustomerActions from '@Actions/customerActions';
import H3FormInput from '@Uilib/h3FormInput';
import PaymentOutgoingActions from '@Actions/paymentOutgoingActions';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import InvoiceActions from '@Actions/invoiceActions';
import TenantActions from '@Actions/tenantActions';
import H3Image from '@Uilib/h3Image';
import { cdnUrl } from '@Utils/cdnHelper';
import SelectCustomer from '../../../Common/SelectCustomer';
import CustomerForm from '../../Customer/CustomerHome/CreateCustomer/CustomerForm';
import PaymentModeActions from '../../../../actions/configurations/paymentModeAction';
import PRZSelect from '../../../Common/UI/PRZSelect';
import LinkInvoice from './LinkInvoice';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';
import helpers from '@Apis/helpers';
import './style.scss';

const { Option } = Select;

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);
const CreateIncomingPayment = (props) => {
  const {
    getPaymentMode,
    invoices,
    customFields: customFieldProps,
    user,
    getTenantsConfiguration,
    getInvoiceSuccess,
    getCustomers,
    createAndLinkPayment,
    createPaymentsOutgoing,
    history,
    MONEY,
    MONEY_SYMBOL,
    paymentModeResults,
    createAndLinkPaymentLoading,
    createPaymentsOutgoingLoading,
    getInvoiceLoading,
    updatePaymentOutgoingStatusLoading,
  } = props;

  const [tenantId, setTenantId] = useState('');
  const [paymentDate, setPaymentDate] = useState(dayjs());
  const [selectedPaymentMode, setSelectedPaymentMode] = useState('');
  const [invoiceData, setInvoiceData] = useState([]);
  const [customFields, setCustomFields] = useState([]);
  const [isUserReadyOne, setIsUserReadyOne] = useState(false);
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [amount, setAmount] = useState(0);
  const [utrNumber, setUtrNumber] = useState('');
  const [remarks, setRemarks] = useState('');
  const [fileList, setFileList] = useState([]);
  const [paymentType, setPaymentType] = useState('');
  const [currentSelectedCustomer, setCurrentSelectedCustomer] = useState(null);
  const [fullAmountSelected, setFullAmountSelected] = useState(false);
  const [showInvLinkDrawer, setShowInvLinkDrawer] = useState(false);
  const [selectedCustomerInfo, setSelectedCustomerInfo] = useState(null);
  const [recalculate, setReCalculate] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTitle, setPreviewTitle] = useState('');

  const checkMaxAmount = (invoiceId) => {
    let totalAmount = 0;

    if (invoiceId) {
      invoiceData
        ?.filter((invoice) => invoice?.invoice_id !== invoiceId)
        ?.forEach((item) => {
          totalAmount += Number(item.payment || 0);
        });
    } else {
      if (invoiceData) for (const item of invoiceData) {
        totalAmount += Number(item.payment || 0);
      }
    }

    return {
      totalAmount,
      remainingAmount: amount - totalAmount,
      paymentExceed: totalAmount < amount,
    };
  };

  const columns = [
    {
      title: 'INVOICE NUMBER',
      render: (text, record) => (
        <Link to={`/sales/invoice/view/${record?.invoice_id}`} target="_blank">
          {record?.invoice_number}
        </Link>
      ),
    },
    {
      title: 'ITEMS',
      render: (text, record) => `${record?.item_count}`,
    },
    {
      title: 'CREATED TIME',
      render: (text, record) => (
        <div>
          {toISTDate(record.created_at).format('DD/MM/YYYY')}
          <H3Text
            text={
              record?.created_by_info
                ? `${record?.created_by_info.first_name} ${record?.created_by_info.last_name}`
                : 'Unknown'
            }
            className="table-subscript"
          />
        </div>
      ),
    },
    {
      title: 'DATE',
      render: (text, record) =>
        record?.invoice_date
          ? toISTDate(record?.invoice_date).format('DD/MM/YYYY')
          : '',
    },
    {
      title: 'TOTAL AMOUNT',
      render: (text, record) =>
        MONEY(
          (record?.invoice_total_amount ||
            record?.invoice_grand_total * (record?.conversion_rate || 1)) +
          record?.invoice_round_off
        ),
    },
    {
      title: 'AMOUNT DUE',
      render: (text, record) =>
        MONEY(
          (record?.invoice_total_amount ||
            record?.invoice_grand_total * (record?.conversion_rate || 1)) -
          (record?.payment_made ||
            record?.total_payment_made * (record?.conversion_rate || 1)) -
          record?.credits_applied * (record?.conversion_rate || 1) +
          record?.invoice_round_off
        ),
    },
    {
      title: 'PAYMENT',
      width: '150px',
      render: (text, record) => (
        <div>
          <H3FormInput
            containerClassName="payment-amount__wrapper-outer "
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput input"
            type="number"
            value={record?.payment}
            onChange={(event) => {
              const updatedInvoiceData = invoiceData.map((item) => {
                if (item?.invoice_id === record?.invoice_id) {
                  return { ...item, payment: event.target.value };
                }
                return item;
              });
              setInvoiceData(updatedInvoiceData);
            }}
            disabled={!amount || (!record?.payment && !checkMaxAmount()?.paymentExceed)}
          />
          <H3Text
            text={<div>Pay in Full</div>}
            onClick={() =>
              checkMaxAmount()?.paymentExceed && handleFullPayment(record)
            }
            className="goods-receiving__po_no-pay"
          />
          {((record?.payment >
            ((record?.grn_grand_total ||
              record?.invoice_total_amount +
              record?.invoice_round_of ||
              record?.invoice_grand_total * (record?.conversion_rate || 1) +
              record?.invoice_round_of) -
              (record?.payment_made ||
                record?.total_payment_made *
                (record?.conversion_rate || 1)) -
              record?.credits_applied * (record?.conversion_rate || 1))) ||
            record?.payment >
            ((record?.invoice_total_amount ||
              record?.invoice_grand_total *
              (record?.conversion_rate || 1)) -
              (record?.payment_made ||
                record?.total_payment_made *
                (record?.conversion_rate || 1)) -
              record?.credits_applied *
              (record?.conversion_rate || 1) +
              record?.invoice_round_off)) && (
              <div className="input-error">Please enter valid amount*</div>
            )}
        </div>
      ),
    },
    {
      title: '',
      render: (text, record) => (
        <Fragment>
          <Popconfirm
            title="Are you sure you want to remove this invoice line?"
            onConfirm={() => handleDelete(record.key)}
            okText="Yes"
            cancelText="No"
          >
            <div className="delete-line-button">
              <CloseOutlined />
            </div>
          </Popconfirm>
        </Fragment>
      ),
      width: '40px',
    },
  ];

  useEffect(() => {
    getTenantsConfiguration(user?.tenant_info?.tenant_id);
    getInvoiceSuccess(null);
  }, [getTenantsConfiguration, getInvoiceSuccess, user]);

  useEffect(() => {
    if (!paymentModeResults?.length) {
      getPaymentMode('NOT_NUll');
    }

    if (customFieldProps && !isUserReadyOne) {
      setCustomFields(
        customFieldProps?.data?.custom_fields?.map((customField) => ({
          cfId: customField?.cf_id,
          fieldType: customField?.field_type,
          isRequired: customField?.is_required,
          isActive: customField?.is_active,
          fieldName: customField?.field_name,
          defaultData: customField?.default_data,
          placeholder: customField?.placeholder,
          fieldValue:
            customField?.field_type?.toUpperCase() !== 'ATTACHMENT' ? '' : [],
        }))
      );
      setIsUserReadyOne(true);
    }
  }, [paymentModeResults, getPaymentMode, invoices, customFieldProps]);

  const resetAllPayments = () => {
    setInvoiceData((prev) =>
      prev.map((item) => ({
        ...item,
        payment: 0,
      }))
    );
  };

  const handleFullPayment = (record) => {
    const copyInvoiceData = invoiceData?.map((item) => {
      if (item?.invoice_id === record?.invoice_id) {
        return {
          ...item,
          payment: Math.min(checkMaxAmount(record?.invoice_id)?.remainingAmount, ((item?.invoice_total_amount + item?.invoice_round_off) || ((item?.invoice_grand_total * (item?.conversion_rate || 1)) + item?.invoice_round_off)) - (item?.payment_made || (item?.total_payment_made * (item?.conversion_rate || 1))) - (item?.credits_applied * (item?.conversion_rate || 1))),
        };
      }
      return {
        ...item,
      };
    });
    setInvoiceData(copyInvoiceData);
  };

  const computetotal = () => {
    const copyData = invoiceData;
    const suminvTotal = copyData?.reduce(
      (sum, invoice) =>
        sum +
        (invoice?.invoice_total_amount ||
          invoice?.invoice_grand_total * (invoice?.conversion_rate || 1)) +
        invoice.invoice_round_off,
      0
    );
    const sumtotpayment = copyData?.reduce(
      (sum, invoice) =>
        sum +
        (invoice?.payment_made ||
          invoice.total_payment_made * (invoice?.conversion_rate || 1)) +
        invoice?.credits_applied * (invoice?.conversion_rate || 1),
      0
    );
    return { sumInvoiceGrandTotal: suminvTotal, sumTotalPaymentMade: sumtotpayment };
  };

  const showNewCustomerInSelect = (value) => {
    setTenantId(value?.tenant_id);
    setCurrentSelectedCustomer(value?.customer_id);
    setSelectedCustomerInfo(value?.customer_info);
    setPaymentType('');
    setFullAmountSelected(false);
    setAmount(0);
  };

  const handleAddPayment = (paymentRequest) => {
    setFormSubmitted(true);

    let paymentMode = '';
    let allPaymentsUnderAmount;

    // check if all GRNs have valid payment amount
    for (let i = 0; i < (invoiceData?.length || 0); i++) {
      const inv = invoiceData[i];
      if (
        Number(inv?.payment) >
        Number(
          (inv?.invoice_total_amount + inv?.invoice_round_off) ||
          (inv?.invoice_grand_total * (inv?.conversion_rate || 1)) +
          inv?.invoice_round_off
        ) -
        (inv?.payment_made ||
          inv?.total_payment_made * (inv?.conversion_rate || 1)) -
        inv?.credits_applied * (inv?.conversion_rate || 1)
        ||
        Number(inv?.payment) > Number(amount)
      ) {
        allPaymentsUnderAmount = true;
      }
    }

    // ✅ validations
    if (
      currentSelectedCustomer &&
      amount &&
      selectedPaymentMode &&
      paymentDate &&
      !allPaymentsUnderAmount &&
      !customFields?.filter(
        (cf) =>
          cf.isRequired &&
          (cf.fieldType === 'ATTACHMENT'
            ? !cf?.fieldValue?.length
            : !cf?.fieldValue)
      )?.length
    ) {
      const invoiceDataLines = invoiceData
        ?.filter((record) => record.payment)
        .map((item) => ({
          invoice_id: item.invoice_id,
          amount: item.payment,
        }));

      // ✅ CASE 1: INVOICE_PAYMENT
      if (paymentType === 'INVOICE_PAYMENT') {
        if (paymentRequest) {
          // online payment → PICE
          for (const acc of user?.tenant_info?.payment_accounts || []) {
            if (acc?.account_name === 'PICE') {
              paymentMode = acc?.account_id;
            }
          }
          const payload = {
            payment_details: [
              {
                amount,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                tenant_id: tenantId,
                payment_date: paymentDate,
                customer_id: currentSelectedCustomer,
                attachments:
                  fileList?.map((f) => ({
                    url: f?.response?.response?.location || f?.url,
                    type: f.type,
                    name: f.name,
                    uid: f.uid,
                  })) || [],
                remarks,
                approval_status: 'DRAFT',
                payment_status: 'PENDING',
              },
            ],
            invoice_details: invoiceDataLines,
            custom_fields: customFields?.map((cf) => ({
              cf_id: cf?.cfId,
              field_name: cf?.fieldName,
              field_value: cf?.fieldValue,
              field_type: cf?.fieldType,
              is_required: cf.isRequired,
              default_data: cf?.defaultData,
              placeholder: cf?.placeholder,
            })),
          };
          createAndLinkPayment(payload, () => history.push('/sales/incoming-payments'));
        } else {
          // offline payment → CASH
          for (const acc of user?.tenant_info?.payment_accounts || []) {
            if (acc?.account_name === 'CASH') {
              paymentMode = acc?.account_id;
            }
          }
          const payload = {
            payment_details: [
              {
                amount,
                tenant_id: tenantId,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                payment_date: paymentDate,
                customer_id: currentSelectedCustomer,
                attachments:
                  fileList?.map((f) => ({
                    url: f?.response?.response?.location || f?.url,
                    type: f.type,
                    name: f.name,
                    uid: f.uid,
                  })) || [],
                remarks,
                approval_status: 'ISSUED',
                payment_status: 'PAYMENT_SUCCESS',
              },
            ],
            invoice_details: invoiceDataLines,
            custom_fields: customFields?.map((cf) => ({
              cf_id: cf?.cfId,
              field_name: cf?.fieldName,
              field_value: cf?.fieldValue,
              field_type: cf?.fieldType,
              is_required: cf.isRequired,
              default_data: cf?.defaultData,
              placeholder: cf?.placeholder,
            })),
          };
          createAndLinkPayment(payload, () => history.push('/sales/incoming-payments'));
        }
      }

      // ✅ CASE 2: NON-INVOICE_PAYMENT
      else if (paymentRequest && paymentType !== 'INVOICE_PAYMENT') {
        // online → PICE
        for (const acc of user?.tenant_info?.payment_accounts || []) {
          if (acc?.account_name === 'PICE') {
            paymentMode = acc?.account_id;
          }
        }
        const payload = [
          {
            amount,
            customer_id: currentSelectedCustomer,
            tenant_id: tenantId,
            paid_through_account_id: selectedPaymentMode,
            utr_number: utrNumber,
            payment_date: paymentDate,
            attachments:
              fileList?.map((f) => ({
                url: f?.response?.response?.location || f?.url,
                type: f.type,
                name: f.name,
                uid: f.uid,
              })) || [],
            remarks,
            approval_status: 'ISSUED',
            payment_status: 'PAYMENT_SUCCESS',
            custom_fields: customFields?.map((cf) => ({
              cf_id: cf?.cfId,
              field_name: cf?.fieldName,
              field_value: cf?.fieldValue,
              field_type: cf?.fieldType,
              is_required: cf.isRequired,
              default_data: cf?.defaultData,
              placeholder: cf?.placeholder,
            })),
          },
        ];
        createPaymentsOutgoing(payload, (payment) => {
          // setSelectedPayment(payment?.payments?.[0]);
          history.push('/sales/incoming-payments');
        });
      } else if (!paymentRequest && paymentType !== 'INVOICE_PAYMENT') {
        // offline → CASH
        for (const acc of user?.tenant_info?.payment_accounts || []) {
          if (acc?.account_name === 'CASH') {
            paymentMode = acc?.account_id;
          }
        }
        const payload = [
          {
            amount,
            paid_through_account_id: selectedPaymentMode,
            utr_number: utrNumber,
            tenant_id: tenantId,
            payment_date: paymentDate,
            customer_id: currentSelectedCustomer,
            attachments:
              fileList?.map((f) => ({
                url: f?.response?.response?.location || f?.url,
                type: f.type,
                name: f.name,
                uid: f.uid,
              })) || [],
            remarks,
            approval_status: 'ISSUED',
            payment_status: 'PAYMENT_SUCCESS',
            custom_fields: customFields?.map((cf) => ({
              cf_id: cf?.cfId,
              field_name: cf?.fieldName,
              field_value: cf?.fieldValue,
              field_type: cf?.fieldType,
              is_required: cf.isRequired,
              default_data: cf?.defaultData,
              placeholder: cf?.placeholder,
            })),
          },
        ];
        createPaymentsOutgoing(payload, () => {
          history.push('/sales/incoming-payments');
        });
      }
    }
  };

  const handleFullAmountToggle = () => {
    const invoiceDataCopy = JSON.parse(JSON.stringify(invoiceData));

    for (let i = 0; i < invoiceDataCopy?.length; i++) {
      const record = invoiceDataCopy[i];
      const totalAmount =
        (record?.invoice_total_amount ||
          record?.invoice_grand_total * (record?.conversion_rate || 1)) +
        record?.invoice_round_off;

      const paidAmount =
        (record?.payment_made ||
          record?.total_payment_made * (record?.conversion_rate || 1)) +
        (record?.credits_applied * (record?.conversion_rate || 1));

      record.payment = totalAmount - paidAmount;
    }
    setAmount(computetotal()?.sumInvoiceGrandTotal - computetotal()?.sumTotalPaymentMade);
    setFullAmountSelected(!fullAmountSelected);
    setInvoiceData(invoiceDataCopy);
  };

  const handleDelete = (key) => {
    const copyData = structuredClone(invoiceData)?.filter((item) => item?.key !== key);
    setInvoiceData(copyData);
    setReCalculate(!recalculate);
  };

  // useEffect for recalculating amounts when any line is deleted.
  useEffect(() => {
    computetotal();
    handleFullAmountToggle();
  }, [recalculate]);

  const getDataSource = () => {
    const copyData = JSON.parse(JSON.stringify(invoiceData || []));
    return copyData;
  };

  const getBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.addEventListener('load', () => resolve(reader.result));
      reader.onerror = (error) => reject(error);
    });
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {

      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(
      file.name || file.url.slice(Math.max(0, file.url.lastIndexOf('/') + 1))
    );
  };

  const precision = user?.tenant_info?.global_config?.settings?.price_precision || 2;

  return (
    <Fragment>
      <Drawer
        open={showNewCustomerModal}
        onClose={() => setShowNewCustomerModal(false)}
        width="720px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '680px' }}>
            <H3Text text="Add New Customer" className="custom-drawer__title" />
            <H3Image
              src={cdnUrl('icon-close-blue.png', 'icons')}
              className="custom-drawer__close-icon"
              onClick={() => setShowNewCustomerModal(false)}
            />
          </div>
        </div>
        <CustomerForm
          callback={(createdCustomer) => {
            getCustomers(
              '',
              user?.tenant_info?.tenant_id,
              1,
              1000,
              '',
              (newCustomers) => {
                showNewCustomerInSelect(
                  newCustomers?.customers.find(
                    (customer) =>
                      customer?.customer_id === createdCustomer.customer_id
                  )
                );
                setShowNewCustomerModal(false);
              }
            );
          }}
        />
      </Drawer>

      <div className="form__wrapper form-component" style={{ paddingTop: '90px' }}>
        <div className="ant-row">
          <div className="ant-col-md-24">
            {/* PART A */}
            <div className="form__section">
              <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                <H3Text text="PART A" className="form__section-title" />
                <div className="form__section-line" />
              </div>

              <div className="form__section-inputs mg-bottom-20">
                <div className="ant-row">
                  {/* Select Customer */}
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <H3Text
                        text="Select Customer"
                        required
                        className="form__input-row__label"
                      />
                      <div
                        className={`orgInputContainer form__input-row__input ${formSubmitted && !currentSelectedCustomer
                          ? 'ant_selector_with_error'
                          : ''
                          }`}
                      >
                        <SelectCustomer
                          hideTitle
                          showAddCustomer={true}
                          selectedCustomer={currentSelectedCustomer}
                          addCustomer={() => setShowNewCustomerModal(true)}
                          onChange={(value) => {
                            setTenantId(value?.tenant_id);
                            setCurrentSelectedCustomer(value?.customer_id);
                            setSelectedCustomerInfo(value?.customer_info);
                            setPaymentType('');
                            setFullAmountSelected(false);
                            setAmount(0);
                            setInvoiceData([]);
                          }}
                          showError={formSubmitted && !tenantId}
                          tenantId={user?.tenant_info?.tenant_id}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Payment Type */}
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <H3Text
                        text="Payment Type"
                        required
                        className="form__input-row__label"
                      />
                      <div
                        className={`orgInputContainer form__input-row__input ${formSubmitted && !paymentType
                          ? 'ant_selector_with_error'
                          : ''
                          }`}
                      >
                        <PRZSelect
                          filterOption={false}
                          value={paymentType}
                          onChange={(val) => {
                            setPaymentType(val);
                            setFullAmountSelected(false);
                            setAmount(0);
                            computetotal();
                          }}
                          disabled={
                            !currentSelectedCustomer ||
                            createAndLinkPaymentLoading ||
                            createPaymentsOutgoingLoading
                          }
                        >
                          <Option key="CUSTOMER_ADVANCE" value="CUSTOMER_ADVANCE">
                            Customer Advance
                          </Option>
                          <Option key="INVOICE_PAYMENT" value="INVOICE_PAYMENT">
                            Invoice Payment
                          </Option>
                        </PRZSelect>
                        {formSubmitted && !paymentType && (
                          <div className="input-error">
                            Please select a payment type*
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Account */}
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <H3Text
                        text="Payment Account"
                        required
                        className="form__input-row__label"
                      />
                      <div
                        className={`orgInputContainer form__input-row__input ${formSubmitted && !selectedPaymentMode
                          ? 'ant_selector_with_error'
                          : ''
                          }`}
                      >
                        <PRZSelect
                          filterOption={false}
                          value={selectedPaymentMode}
                          onChange={(val) => setSelectedPaymentMode(val)}
                          disabled={
                            !currentSelectedCustomer ||
                            createAndLinkPaymentLoading ||
                            createPaymentsOutgoingLoading
                          }
                        >
                          {paymentModeResults?.map((item) => (
                            <Option key={item?.account_id} value={item?.account_id}>
                              {item?.account_name
                                ?.split('_')
                                ?.join(' ')
                                ?.toUpperCase()}
                            </Option>
                          ))}
                        </PRZSelect>
                        {formSubmitted && !selectedPaymentMode && (
                          <div className="input-error">
                            Please select a payment mode*
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Amount */}
                  <div className="ant-col-md-6">
                    <div
                      className="form__input-row "
                      style={{ marginBottom: '10px' }}
                    >
                      <H3Text
                        text="Payment Amount"
                        required
                        className="form__input-row__label"
                      />
                      <div className="payment-amount__wrapper-outer form__input-row__input">
                        <div className="payment-amount__wrapper">
                          <H3Text
                            text={MONEY_SYMBOL}
                            className="payment-amount__currency"
                          />
                          <H3FormInput
                            name="payment amount"
                            type="number"
                            disabled={
                              !currentSelectedCustomer ||
                              fullAmountSelected ||
                              createAndLinkPaymentLoading ||
                              createPaymentsOutgoingLoading
                            }
                            containerClassName="payment-amount__wrapper-outer "
                            labelClassName="orgFormLabel"
                            inputClassName={`orgFormInput input ${formSubmitted && !amount
                              ? 'form__input-row__input-error'
                              : ''
                              }`}
                            onChange={(event) => {
                              resetAllPayments();
                              setAmount(helpers.trimDecimalsAsNumber(Number(event.target.value), precision));
                            }}
                            value={amount}
                          />
                        </div>
                        {formSubmitted && !amount && (
                          <div className="input-error">
                            Please enter payment amount*
                          </div>
                        )}
                        {invoiceData &&
                          paymentType === 'INVOICE_PAYMENT'
                          && (
                            <Checkbox
                              onChange={() => handleFullAmountToggle()}
                              disabled={
                                createAndLinkPaymentLoading ||
                                createPaymentsOutgoingLoading
                              }
                              checked={fullAmountSelected}
                              className="payment-amount__checkbox"
                            >
                              Pay total due amount (
                              {MONEY(
                                (computetotal()?.sumInvoiceGrandTotal -
                                  computetotal()?.sumTotalPaymentMade) || 0
                              )}
                              )
                            </Checkbox>
                          )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Date */}
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <H3Text
                        text="Payment Date"
                        required
                        className="form__input-row__label"
                      />
                      <div
                        className={`form__input-row__input ${formSubmitted && !paymentDate
                          ? 'form__input-row__input-error'
                          : ''
                          }`}
                      >
                        <DatePicker
                          value={paymentDate}
                          onChange={(value) => setPaymentDate(value)}
                          style={{
                            border: '1px solid rgba(68, 130, 218, 0.2)',
                            borderRadius: '2px',
                            padding: '1px 3px',
                            width: '100%',
                            background: 'white',
                          }}
                          disabled={
                            createAndLinkPaymentLoading ||
                            createPaymentsOutgoingLoading
                          }
                        />
                        {formSubmitted && !paymentDate && (
                          <div className="input-error">
                            Please select a payment date*
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Reference # */}
                  <div className="ant-col-md-6">
                    <div className="form__input-row orgInputContainer">
                      <H3Text text="Reference #" className="form__input-row__label" />
                      <H3FormInput
                        name="payment reference number"
                        type="text"
                        containerClassName=" form__input-row__input"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput input"
                        onChange={(event) => setUtrNumber(event.target.value)}
                        value={utrNumber}
                        disabled={
                          createAndLinkPaymentLoading ||
                          createPaymentsOutgoingLoading
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* PART B */}
            <div className="form__section">
              <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                <H3Text text="PART B" className="form__section-title" />
                <div className="form__section-line" />
              </div>
              <div className="form__section-inputs mg-bottom-20">
                <div className="ant-row">
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <H3Text
                        text="Internal Remark"
                        className="form__input-row__label"
                      />
                      <textarea
                        className="orgFormInput form__input-row__input"
                        value={remarks}
                        onChange={(e) => setRemarks(e.target.value)}
                        disabled={
                          createAndLinkPaymentLoading ||
                          createPaymentsOutgoingLoading
                        }
                      />
                    </div>
                  </div>
                  <div className="ant-col-md-6">
                    <div className="form_input-row">
                      <H3Text
                        text="Attachment(s)"
                        className="form__input-row__label"
                      />
                      <div style={{ marginLeft: '0px' }}>
                        <Upload
                          action={Constants.UPLOAD_FILE}
                          listType="picture-card"
                          fileList={fileList}
                          onPreview={handlePreview}
                          accept="image/*"
                          onChange={(fileListData) =>
                            setFileList(fileListData.fileList)
                          }
                          multiple
                          disabled={
                            createAndLinkPaymentLoading ||
                            createPaymentsOutgoingLoading
                          }
                        >
                          {fileList?.length >= 10 ? null : uploadButton}
                        </Upload>
                        <Modal
                          open={previewVisible}
                          title={previewTitle}
                          footer={null}
                          onCancel={() => setPreviewVisible(false)}
                        >
                          <img
                            alt="example"
                            style={{ width: '100%' }}
                            src={previewImage}
                          />
                        </Modal>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Invoice Table */}
        <div className="form__lines-wrapper">
          {paymentType === 'INVOICE_PAYMENT' && (
            <div className="incoming-payment__table">
              {(getDataSource() || []).length > 0 ? (
                <Table
                  size="small"
                  title={() => (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <span>{(getDataSource() || []).length} Invoices</span>
                      <Button
                        className="inv__no-inv__button hide__in-mobile"
                        onClick={() => setShowInvLinkDrawer(true)}
                        type="primary"
                      >
                        Link Invoices
                      </Button>
                    </div>
                  )}
                  scroll={{ y: 400 }}
                  columns={columns}
                  bordered
                  loading={createAndLinkPaymentLoading || getInvoiceLoading}
                  dataSource={getDataSource()}
                  pagination={false}
                />
              ) : (
                <Fragment>
                  {
                    (getInvoiceLoading) ? <Spin className="payment__no-inv-loader" /> : (
                      <div className="inv__no-inv-wrapper">
                        <div className="inv__no-inv">
                          <img className="inv__no-inv__image" src={cdnUrl('NoInvoice.png', 'images')} alt="bundle" />
                          <H3Text className="inv__no-inv__text" text="No invoices linked. Click 'Link Invoices' to proceed." />
                          <Button
                            className="inv__no-inv__button hide__in-mobile"
                            onClick={() => setShowInvLinkDrawer(true)}
                            type="primary"
                          >
                            Link Invoices
                          </Button>
                        </div>
                      </div>
                    )
                  }
                </Fragment>
              )}
            </div>
          )}
        </div>

        {/* Totals */}
        <div className="form__data-wrapper">
          <div className="ant-row">
            <div className="ant-col-md-24">
              <div className="form-section">
                <div className="form__section-inputs mg-bottom-20">
                  <div className="ant-row">
                    <div className="ant-col-md-6" />
                    <div className="ant-col-md-6" />
                    <div className="ant-col-md-6" />
                    {paymentType === 'INVOICE_PAYMENT' && (
                      <div className="ant-col-md-6">
                        <div className="purchase-incoming-payment-totals">
                          <div className="purchase-incoming-payment__field">
                            <H3Text
                              text="Amount Used"
                              className="purchase-incoming-payment__field-name"
                            />
                            <H3Text
                              text={MONEY(checkMaxAmount()?.totalAmount || '0')}
                              className="purchase-incoming-payment__field-value"
                            />
                          </div>
                          <div className="purchase-incoming-payment__field">
                            <H3Text
                              text="Amount Remaining"
                              className="purchase-incoming-payment__field-name"
                            />
                            <H3Text
                              text={MONEY(
                                checkMaxAmount()?.remainingAmount || '0'
                              )}
                              className="purchase-incoming-payment__field-value"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <PRZDrawer
          open={showInvLinkDrawer}
          width={800}
          onClose={() => setShowInvLinkDrawer(false)}
          headerWidth={750}
          title={'Link Invoice(s) to this payment'}
        >
          <div className="custom-drawer__header-wrapper">
            <LinkInvoice
              setInvoiceData={setInvoiceData}
              invoiceData={invoiceData}
              customerId={currentSelectedCustomer}
              selectedCustomerInfo={selectedCustomerInfo}
              callback={() => {
                setShowInvLinkDrawer(false);
                setFullAmountSelected(false);
                setAmount(0);
              }}
            />
          </div>
        </PRZDrawer>

        {/* Footer */}
        <div className="form__footer">
          <H3Button
            text="Record Payment"
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            style={{
              padding: '7px 10px',
              marginRight: '10px',
              minWidth: '130px',
            }}
            onClick={() => {
              if (!createAndLinkPaymentLoading) {
                handleAddPayment(false);
              }
            }}
            isLoading={
              createAndLinkPaymentLoading ||
              createPaymentsOutgoingLoading ||
              updatePaymentOutgoingStatusLoading
            }
            disabled={
              createAndLinkPaymentLoading ||
              createPaymentsOutgoingLoading ||
              updatePaymentOutgoingStatusLoading
            }
          />
        </div>
      </div>
    </Fragment>
  );

};

const mapStateToProps = ({
  UserReducers, PaymentOutgoingReducers, InvoiceReducers,
  CFV2Reducers, PaymentModeReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  MONEY_SYMBOL: UserReducers.MONEY_SYMBOL,
  getInvoiceLoading: InvoiceReducers.getInvoiceLoading,
  invoices: InvoiceReducers.invoices,
  createPaymentsOutgoingLoading: PaymentOutgoingReducers.createPaymentsOutgoingLoading,
  createAndLinkPaymentLoading: PaymentOutgoingReducers.createAndLinkPaymentLoading,
  updatePaymentOutgoingStatusLoading: PaymentOutgoingReducers.updatePaymentOutgoingStatusLoading,
  customFields: CFV2Reducers.customFields,
  paymentModeResults: PaymentModeReducers.paymentModeResults,
});
const mapDispatchToProps = (dispatch) => ({
  getCustomers: (keyword, tenantId, page, limit, customerId, callback) => dispatch(CustomerActions.getCustomers(keyword, tenantId, page, limit, customerId, callback)),
  createAndLinkPayment: (payload, callback) => dispatch(PaymentOutgoingActions.createAndLinkPayment(payload, callback)),
  createPaymentsOutgoing: (payload, callback) => dispatch(PaymentOutgoingActions.createPaymentsOutgoing(payload, callback)),
  getTenantsConfiguration: (tenantId) => dispatch(TenantActions.getTenantsConfiguration(tenantId)),
  getInvoice: (orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status) => dispatch(InvoiceActions.getInvoice(orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status)),
  getInvoiceSuccess: (invoices) => dispatch(InvoiceActions.getInvoiceSuccess(invoices)),
  getPaymentMode: (accountType, callback) => dispatch(PaymentModeActions.getPaymentMode(accountType, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CreateIncomingPayment));
