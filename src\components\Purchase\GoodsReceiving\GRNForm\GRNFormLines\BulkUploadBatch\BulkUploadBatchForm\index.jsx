/* eslint-disable no-nested-ternary */
import React, { Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {read as XLSXRead, utils as XLSXUtils } from 'xlsx/xlsx.mjs';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import {
  DownloadOutlined, LoadingOutlined,
} from '@ant-design/icons';
import {
  Timeline, notification,
} from 'antd';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import './style.scss';
import Constants from '@Apis/constants';
import ProductActions from '@Actions/productActions';
import WorkflowActions from '@Actions/workflowActions';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';

const BulkUploadForm = ({
  user, callback,
  getProductBySkusLoading, filteredData, setFilteredData, onBulkUploadBatch, parentKey, inventoryLocationId, inventoryLocationPath,
}) => {
  const onFileSelect = (e) => {
    const [file] = e.target.files;
    const reader = new FileReader();
    reader.onload = (evt) => {
      const bstr = evt.target.result;
      const wb = XLSXRead(bstr, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSXUtils.sheet_to_json(ws, { header: 1 });

      function convertExcelForExpiryDate(excelDate) {
        if (!excelDate) {
          return;
        }
        const startDate = dayjs('1900-01-01', { year: 1900, month: 0, day: 1 });
        const targetDate = startDate.add(excelDate - 2, 'day');
        return targetDate.format('YYYY-MM-DD');
      }

      function convertExcelDateBatchMfgDate(excelDate) {
        if (!excelDate) {
          return dayjs().format('YYYY-MM-DD');
        }
        const startDate = dayjs('1900-01-01', { year: 1900, month: 0, day: 1 });
        const targetDate = startDate.add(excelDate - 2, 'day');
        return targetDate.format('YYYY-MM-DD');
      }

      const copyFileData = data?.slice(1)?.map((item) => {
        // Map headers to column indices
        const columnIndices = data[0]?.reduce((acc, col, colIndex) => {
          acc[col] = colIndex;
          return acc;
        }, {});
        // Extract custom fields dynamically
        const customFields = Object.keys(columnIndices)
          .filter((header) => header.includes('[')) // Identify headers with brackets
          .map((header) => {
            const properName = header.split('[')[0].trim(); // Extract meaningful part before '['
            const id = header.match(/\[(\d+)\]/)?.[1] || ''; // Extract numeric ID from brackets
            const value = item[columnIndices[header]] || ''; // Get the corresponding value
            return {
              id, // Store the extracted ID
              name: properName, // Store the proper name
              value, // Store the corresponding value
            };
          });
        return {
          parentKey,
          key: uuidv4(),
          custom_batch_number: String(item[columnIndices['Batch #']]),
          quantity: Number(item[columnIndices.Quantity]),
          cost_price: item[columnIndices['Cost Price']],
          selling_price: item[columnIndices['Selling Price']],
          mrp: item[columnIndices.MRP],
          ar_number: String(item[columnIndices['AR Number']]), // Convert to string
          expiry_date: convertExcelForExpiryDate(item[columnIndices.Expiry]),
          inventory_location_id: inventoryLocationId,
          inventory_location_path: inventoryLocationPath,
          batch_barcode: item[columnIndices.Barcode],
          roll_no: item[columnIndices['Roll No']],
          mfg_batch_no: item[columnIndices['Mfg Batch No']],
          manufacturing_date: convertExcelDateBatchMfgDate(item[columnIndices['Mfg Date']]),
          customFields, // Include dynamically extracted custom fields
        };
      });
      setFilteredData(copyFileData);
    };
    reader.readAsBinaryString(file);
  };

  const downloadSampleFile = (org_id) => {
    const a = document.createElement('a');
    const url = `${Constants.GET_BULK_PRODUCT_BATCH_SHEET}?org_id=${org_id}`;
    a.href = url;
    a.download = url.split('/').pop();
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const addBatchData = () => {
    if (filteredData?.length > 500) {
      notification.error({
        message: 'You can only upload upto 500 products at once.',
        placement: 'top',
        duration: 4,
      });
    } else {
      onBulkUploadBatch(filteredData);
      callback();
    }
  };

  return (
    <Fragment>
      <div className="ant-row" style={{ paddingBottom: '60px' }}>
        <div className="ant-col-md-24">
          <div className="view__timeline-workflow">
            <Timeline>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 1
                    </div>
                    <div className="timeline-item-person">
                      Download the XLS file and fill the details.
                      <div className="timeline-item-download">
                        <div
                          className="timeline-item-download-button"
                          onClick={() => {
                            downloadSampleFile(user?.org_id);
                          }}
                        >
                          <DownloadOutlined />
                          <H3Text text="Download Batches" className="" />
                          <div className="timeline-item-download-button__loading">
                            {getProductBySkusLoading && <LoadingOutlined />}
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 2
                    </div>
                    <div className="timeline-item-person">
                      Add the Batch #, quantity, cost price and Shelf/Rack against each batch.
                    </div>
                  </div>
                </div>
              </Timeline.Item>
              {/* <Timeline.Item color="blue">
                <div className="timeline-item">
                  <div>
                    <div className="timeline-item-title">
                      Step 3
                    </div>
                    <div className="timeline-item-person">
                      Upload the sheet and verify the list of products.
                    </div>

                  </div>
                </div>
              </Timeline.Item> */}
            </Timeline>
            <div className="view__timeline-upload">
              <input
                type="file"
                onChange={(e) => onFileSelect(e)}
                className="view__timeline-upload-input"
                accept=".xlsx,.xls"
              />
            </div>
          </div>
        </div>

        <div className="custom-drawer__footer">
          <H3Button
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            text="+ Add Batches"
            // isLoading={getProductBySkusLoading}
            onClick={() => addBatchData()}
            // disabled={getProductBySkusLoading}
            style={{
              width: '150px',
              borderRadius: '5px',
              padding: '7px 0px',
              marginLeft: '0',
            }}
          />
        </div>
      </div>
    </Fragment>
  );
};

const mapStateToProps = ({
  UserReducers, ProductReducers,
}) => ({
  user: UserReducers.user,
  getProductBySkusLoading: ProductReducers.getProductBySkusLoading,
  getSellerCodesLoading: ProductReducers.getSellerCodesLoading,

});

const mapDispatchToProps = (dispatch) => ({
  getPurchaseWorkflowSample: (tenantId, sampleType) => dispatch(WorkflowActions.getPurchaseWorkflowSample(tenantId, sampleType)),
  getSellerCodes: (sellerCode, orgId, callback) => dispatch(ProductActions.getSellerCodes(sellerCode, orgId, callback)),
  getProductBySkus: (payload, callback) => dispatch(ProductActions.getProductBySkus(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BulkUploadForm));
